import os
import time
import torch
import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error
from torch.utils.data import DataLoader
import matplotlib.pyplot as plt
from transformer_trading import TransformerRegressor, TimeSeriesDataset, load_tick_data, resample_ohlc, add_indicators, scale_features
import argparse

class TradingStrategy:
    """Trading strategy based on transformer predictions"""
    
    def __init__(self, threshold=0.001, stop_loss=0.02, take_profit=0.03):
        self.threshold = threshold  # Minimum price change to trigger signal
        self.stop_loss = stop_loss  # Stop loss percentage
        self.take_profit = take_profit  # Take profit percentage
        
    def generate_signals(self, current_prices, predicted_prices):
        """Generate buy/sell signals based on predictions"""
        signals = []
        
        for i in range(len(predicted_prices)):
            if i == 0:
                signals.append(0)  # No signal for first prediction
                continue
                
            current_price = current_prices[i]
            predicted_price = predicted_prices[i]
            price_change = (predicted_price - current_price) / current_price
            
            if price_change > self.threshold:
                signals.append(1)  # Buy signal
            elif price_change < -self.threshold:
                signals.append(-1)  # Sell signal
            else:
                signals.append(0)  # Hold
                
        return signals

class BacktestEngine:
    """Comprehensive backtesting engine"""
    
    def __init__(self, initial_capital=10000, commission=0.001):
        self.initial_capital = initial_capital
        self.commission = commission
        
    def backtest_strategy(self, prices, signals, strategy):
        """Run backtest with given signals"""
        capital = self.initial_capital
        position = 0  # 0: no position, 1: long, -1: short
        entry_price = 0
        
        trades = []
        equity_curve = [capital]
        
        for i in range(1, len(signals)):
            current_price = prices[i]
            signal = signals[i]
            
            # Close existing position if stop loss or take profit hit
            if position != 0:
                if position == 1:  # Long position
                    pnl_pct = (current_price - entry_price) / entry_price
                    if pnl_pct <= -strategy.stop_loss or pnl_pct >= strategy.take_profit:
                        # Close position
                        pnl = capital * pnl_pct - (capital * self.commission)
                        capital += pnl
                        trades.append({
                            'entry_price': entry_price,
                            'exit_price': current_price,
                            'position': position,
                            'pnl': pnl,
                            'pnl_pct': pnl_pct
                        })
                        position = 0
                        
                elif position == -1:  # Short position
                    pnl_pct = (entry_price - current_price) / entry_price
                    if pnl_pct <= -strategy.stop_loss or pnl_pct >= strategy.take_profit:
                        # Close position
                        pnl = capital * pnl_pct - (capital * self.commission)
                        capital += pnl
                        trades.append({
                            'entry_price': entry_price,
                            'exit_price': current_price,
                            'position': position,
                            'pnl': pnl,
                            'pnl_pct': pnl_pct
                        })
                        position = 0
            
            # Open new position based on signal
            if position == 0 and signal != 0:
                position = signal
                entry_price = current_price
                
            equity_curve.append(capital)
            
        return trades, equity_curve
        
    def calculate_metrics(self, trades, equity_curve):
        """Calculate comprehensive performance metrics"""
        if not trades:
            return {}
            
        # Basic metrics
        total_trades = len(trades)
        winning_trades = sum(1 for t in trades if t['pnl'] > 0)
        losing_trades = total_trades - winning_trades
        
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        total_pnl = sum(t['pnl'] for t in trades)
        avg_win = np.mean([t['pnl'] for t in trades if t['pnl'] > 0]) if winning_trades > 0 else 0
        avg_loss = np.mean([t['pnl'] for t in trades if t['pnl'] < 0]) if losing_trades > 0 else 0
        
        # Risk metrics
        returns = np.diff(equity_curve) / equity_curve[:-1]
        sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0
        
        # Drawdown
        peak = np.maximum.accumulate(equity_curve)
        drawdown = (equity_curve - peak) / peak
        max_drawdown = np.min(drawdown)
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'total_pnl': total_pnl,
            'total_return_pct': (equity_curve[-1] - equity_curve[0]) / equity_curve[0] * 100,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': abs(avg_win / avg_loss) if avg_loss != 0 else float('inf'),
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown * 100
        }

def run_comprehensive_backtest(model_path, csv_file, resample_freq, seq_len, batch_size):
    """Run comprehensive backtesting with trading signals and performance analysis"""
    
    print("Starting comprehensive backtest...")
    start_time = time.time()
    
    # Load saved model and scaler
    print("Loading model...")
    checkpoint = torch.load(model_path, weights_only=False)
    model_state_dict = checkpoint['model_state_dict']
    scaler = checkpoint['scaler']
    feature_cols = checkpoint['feature_cols']
    num_heads = checkpoint.get('num_heads', 3)
    num_layers = checkpoint.get('num_layers', 2)
    dropout = checkpoint.get('dropout', 0.1)
    
    # Load and preprocess data
    print("Loading and preprocessing data...")
    ticks = load_tick_data(csv_file)
    ohlc = resample_ohlc(ticks, freq=resample_freq)
    ohlc = add_indicators(ohlc)
    data_scaled, _ = scale_features(ohlc, feature_cols)
    
    print(f"Data loaded: {len(ohlc)} OHLC bars")
    
    # Create dataset and loader
    target_idx = feature_cols.index('close')
    dataset = TimeSeriesDataset(data_scaled, seq_len, target_idx)
    loader = DataLoader(dataset, batch_size=batch_size, shuffle=False)
    
    # Initialize model
    model = TransformerRegressor(seq_len, len(feature_cols), num_heads, num_layers, dropout)
    model.load_state_dict(model_state_dict)
    model.eval()
    
    print(f"Model loaded with {sum(p.numel() for p in model.parameters()):,} parameters")
    
    # Generate predictions
    print("Generating predictions...")
    all_preds = []
    all_actuals = []
    
    with torch.no_grad():
        for X_batch, y_batch in loader:
            preds = model(X_batch)
            all_preds.extend(preds.squeeze().numpy())
            all_actuals.extend(y_batch.squeeze().numpy())
    
    # Rescale predictions and actuals
    pred_prices = []
    actual_prices = []
    
    for i in range(len(all_preds)):
        # Create dummy array for inverse transform
        dummy_pred = np.zeros((1, len(feature_cols)))
        dummy_actual = np.zeros((1, len(feature_cols)))
        dummy_pred[0, target_idx] = all_preds[i]
        dummy_actual[0, target_idx] = all_actuals[i]
        
        pred_rescaled = scaler.inverse_transform(dummy_pred)[0, target_idx]
        actual_rescaled = scaler.inverse_transform(dummy_actual)[0, target_idx]
        
        pred_prices.append(pred_rescaled)
        actual_prices.append(actual_rescaled)
    
    print(f"Generated {len(pred_prices)} predictions")
    
    # Calculate prediction accuracy metrics
    rmse = np.sqrt(mean_squared_error(actual_prices, pred_prices))
    mae = mean_absolute_error(actual_prices, pred_prices)
    mape = np.mean(np.abs((np.array(actual_prices) - np.array(pred_prices)) / np.array(actual_prices))) * 100
    
    print(f"\nPrediction Accuracy Metrics:")
    print(f"RMSE: {rmse:.6f}")
    print(f"MAE: {mae:.6f}")
    print(f"MAPE: {mape:.2f}%")
    
    # Generate trading signals
    print("\nGenerating trading signals...")
    strategy = TradingStrategy(threshold=0.002, stop_loss=0.03, take_profit=0.05)
    signals = strategy.generate_signals(actual_prices, pred_prices)
    
    buy_signals = sum(1 for s in signals if s == 1)
    sell_signals = sum(1 for s in signals if s == -1)
    hold_signals = sum(1 for s in signals if s == 0)
    
    print(f"Signals generated: {buy_signals} BUY, {sell_signals} SELL, {hold_signals} HOLD")
    
    # Run backtest
    print("\nRunning backtest...")
    backtest_engine = BacktestEngine(initial_capital=10000, commission=0.001)
    trades, equity_curve = backtest_engine.backtest_strategy(actual_prices, signals, strategy)
    
    # Calculate performance metrics
    metrics = backtest_engine.calculate_metrics(trades, equity_curve)
    
    # Print results
    print("\n" + "="*60)
    print("BACKTESTING RESULTS")
    print("="*60)
    
    if metrics:
        print(f"Total Trades: {metrics['total_trades']}")
        print(f"Winning Trades: {metrics['winning_trades']}")
        print(f"Losing Trades: {metrics['losing_trades']}")
        print(f"Win Rate: {metrics['win_rate']:.2%}")
        print(f"Total Return: {metrics['total_return_pct']:.2f}%")
        print(f"Total P&L: ${metrics['total_pnl']:.2f}")
        print(f"Average Win: ${metrics['avg_win']:.2f}")
        print(f"Average Loss: ${metrics['avg_loss']:.2f}")
        print(f"Profit Factor: {metrics['profit_factor']:.2f}")
        print(f"Sharpe Ratio: {metrics['sharpe_ratio']:.2f}")
        print(f"Max Drawdown: {metrics['max_drawdown']:.2f}%")
    else:
        print("No trades executed during backtest period")
    
    # Create comprehensive plots
    print("\nCreating plots...")
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # Plot 1: Price predictions vs actual
    ax1.plot(actual_prices[:500], label='Actual Prices', color='blue', alpha=0.7)
    ax1.plot(pred_prices[:500], label='Predicted Prices', color='red', alpha=0.7)
    ax1.set_title('Price Predictions vs Actual (First 500 points)')
    ax1.set_xlabel('Time Steps')
    ax1.set_ylabel('Price')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Plot 2: Trading signals
    ax2.plot(actual_prices[:500], color='black', alpha=0.5, label='Price')
    buy_points = [i for i, s in enumerate(signals[:500]) if s == 1]
    sell_points = [i for i, s in enumerate(signals[:500]) if s == -1]
    
    if buy_points:
        ax2.scatter([actual_prices[i] for i in buy_points], [i for i in buy_points], 
                   color='green', marker='^', s=50, label='Buy Signal')
    if sell_points:
        ax2.scatter([actual_prices[i] for i in sell_points], [i for i in sell_points], 
                   color='red', marker='v', s=50, label='Sell Signal')
    
    ax2.set_title('Trading Signals (First 500 points)')
    ax2.set_xlabel('Time Steps')
    ax2.set_ylabel('Price')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # Plot 3: Equity curve
    if equity_curve:
        ax3.plot(equity_curve, color='green', linewidth=2)
        ax3.set_title('Equity Curve')
        ax3.set_xlabel('Time Steps')
        ax3.set_ylabel('Portfolio Value ($)')
        ax3.grid(True, alpha=0.3)
        
        # Add buy and hold comparison
        initial_price = actual_prices[0]
        final_price = actual_prices[-1] if len(actual_prices) == len(equity_curve) else actual_prices[len(equity_curve)-1]
        buy_hold_return = (final_price / initial_price - 1) * 10000 + 10000
        ax3.axhline(y=buy_hold_return, color='blue', linestyle='--', 
                   label=f'Buy & Hold: ${buy_hold_return:.0f}')
        ax3.legend()
    
    # Plot 4: Prediction error distribution
    errors = np.array(pred_prices) - np.array(actual_prices)
    ax4.hist(errors, bins=50, alpha=0.7, color='purple')
    ax4.set_title('Prediction Error Distribution')
    ax4.set_xlabel('Prediction Error')
    ax4.set_ylabel('Frequency')
    ax4.grid(True, alpha=0.3)
    ax4.axvline(x=0, color='red', linestyle='--', alpha=0.8)
    
    plt.tight_layout()
    plt.savefig('comprehensive_backtest_results.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    total_time = time.time() - start_time
    print(f"\nBacktest completed in {total_time:.2f} seconds")
    print(f"Results saved to: comprehensive_backtest_results.png")
    
    return metrics, trades, equity_curve

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Backtest Transformer Model')
    parser.add_argument('--model_path', type=str, default='transformer_trading_model.pth')
    parser.add_argument('--tick_csv', type=str, default='stpRNG_90days_ticks.csv')
    parser.add_argument('--resample_freq', type=str, default='1min')
    parser.add_argument('--seq_len', type=int, default=60)
    parser.add_argument('--batch_size', type=int, default=32)
    args = parser.parse_args()

    run_comprehensive_backtest(
        model_path=args.model_path,
        csv_file=args.tick_csv,
        resample_freq=args.resample_freq,
        seq_len=args.seq_len,
        batch_size=args.batch_size
    )
