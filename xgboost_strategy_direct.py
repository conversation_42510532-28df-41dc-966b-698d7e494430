import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import xgboost as xgb
from datetime import datetime
import os
import pickle
import math

# Set up logging
log_dir = "strategy_logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
log_filename = f"{log_dir}/xgboost_strategy_{timestamp}.log"

# Function to log messages
def log_message(message):
    """Log message to file and print to console"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_msg = f"[{timestamp}] {message}"
    print(log_msg)
    with open(log_filename, 'a') as f:
        f.write(log_msg + '\n')

log_message("=== XGBOOST TRADING STRATEGY (DIRECT) ===")

# Load the trained model
model_file = "xgboost_model_20250521_145606.pkl"
log_message(f"Loading model from {model_file}...")
with open(model_file, 'rb') as file:
    model = pickle.load(file)

# Load the Renko data for backtesting and feature creation
log_message("Loading Renko data...")
renko_df = pd.read_csv('stpRNG_renko_0_1_extended.csv')
renko_df['datetime'] = pd.to_datetime(renko_df['datetime'])
log_message(f"Renko data shape: {renko_df.shape}")

# Create features matching the trained model (44 features)
log_message("Creating features from Renko data...")
def create_features(df):
    """Create features matching the trained XGBoost model (44 features)"""
    df = df.copy()
    
    # Add time features first
    df['hour'] = df['datetime'].dt.hour
    df['minute'] = df['datetime'].dt.minute
    df['day_of_week'] = df['datetime'].dt.dayofweek
    
    # Basic price features
    df['price'] = df['close']
    df['price_change'] = df['close'].diff()
    df['price_pct_change'] = df['close'].pct_change()
    
    # Direction: 1 for up, -1 for down, 0 for neutral
    df['direction'] = df['direction'].map({'up': 1, 'down': -1}).fillna(0)
    
    # Calculate streaks
    def calculate_streaks(series):
        """Calculate up_streak and down_streak"""
        up_streak = []
        down_streak = []
        current_up = 0
        current_down = 0
        
        for val in series:
            if val == 1:  # up
                current_up += 1
                current_down = 0
            elif val == -1:  # down
                current_down += 1
                current_up = 0
            else:  # neutral
                current_up = 0
                current_down = 0
            
            up_streak.append(current_up)
            down_streak.append(current_down)
        
        return up_streak, down_streak
    
    up_streaks, down_streaks = calculate_streaks(df['direction'])
    df['up_streak'] = up_streaks
    df['down_streak'] = down_streaks
    
    # Calculate momentum and statistics for different periods
    for period in [5, 10, 15, 20, 30]:
        # Price momentum (sum of price changes)
        df[f'price_momentum_{period}'] = df['price_change'].rolling(period).sum()
        
        # Price percentage change over period
        df[f'price_pct_change_{period}'] = df['close'].pct_change(period)
        
        # Count of up and down moves
        df[f'up_count_{period}'] = df['direction'].rolling(period).apply(lambda x: (x == 1).sum())
        df[f'down_count_{period}'] = df['direction'].rolling(period).apply(lambda x: (x == -1).sum())
        
        # Volatility (rolling standard deviation)
        df[f'volatility_{period}'] = df['close'].rolling(period).std()
        
        # Maximum streaks in the period
        def rolling_max_streak(series, window, streak_type):
            """Calculate rolling maximum streak"""
            result = []
            for i in range(len(series)):
                start_idx = max(0, i - window + 1)
                window_data = series[start_idx:i+1]
                
                if len(window_data) == 0:
                    result.append(0)
                    continue
                
                max_streak = 0
                current_streak = 0
                
                for val in window_data:
                    if (streak_type == 'up' and val == 1) or (streak_type == 'down' and val == -1):
                        current_streak += 1
                        max_streak = max(max_streak, current_streak)
                    else:
                        current_streak = 0
                
                result.append(max_streak)
            
            return result
        
        df[f'max_up_streak_{period}'] = rolling_max_streak(df['direction'], period, 'up')
        df[f'max_down_streak_{period}'] = rolling_max_streak(df['direction'], period, 'down')
    
    # Remove NaN rows
    df = df.dropna()
    
    return df

renko_features = create_features(renko_df)
log_message(f"Features created. Shape: {renko_features.shape}")

# Expected feature columns in exact order for the model
expected_features = [
    'price', 'price_change', 'price_pct_change', 'direction', 'up_streak', 'down_streak',
    'price_momentum_5', 'price_pct_change_5', 'up_count_5', 'down_count_5', 'volatility_5', 'max_up_streak_5', 'max_down_streak_5',
    'price_momentum_10', 'price_pct_change_10', 'up_count_10', 'down_count_10', 'volatility_10', 'max_up_streak_10', 'max_down_streak_10',
    'price_momentum_15', 'price_pct_change_15', 'up_count_15', 'down_count_15', 'volatility_15', 'max_up_streak_15', 'max_down_streak_15',
    'price_momentum_20', 'price_pct_change_20', 'up_count_20', 'down_count_20', 'volatility_20', 'max_up_streak_20', 'max_down_streak_20',
    'price_momentum_30', 'price_pct_change_30', 'up_count_30', 'down_count_30', 'volatility_30', 'max_up_streak_30', 'max_down_streak_30',
    'hour', 'minute', 'day_of_week'
]

# Verify all expected features are present
missing_features = [f for f in expected_features if f not in renko_features.columns]
if missing_features:
    log_message(f"ERROR: Missing features: {missing_features}")
    exit(1)

# Select features in the correct order
X = renko_features[expected_features]

log_message(f"Feature columns ({len(expected_features)}): {expected_features}")
log_message(f"Features shape: {X.shape}")

# Dynamic risk management function
def calculate_dynamic_risk(equity, equity_history, win_streak, loss_streak, recent_outcomes=None):
    """Calculate dynamic risk percentage based on multiple factors"""
    base_risk = 0.04  # Base risk percentage

    # 1. Streak-based adjustment
    if win_streak >= 5:
        streak_factor = 1.3
    elif win_streak >= 3:
        streak_factor = 1.15
    elif loss_streak >= 3:
        streak_factor = 0.7
    elif loss_streak >= 1:
        streak_factor = 0.85
    else:
        streak_factor = 1.0

    # 2. Equity milestone adjustment
    if equity < 1000:
        equity_factor = 1.0
    elif equity < 10000:
        equity_factor = 0.95
    elif equity < 100000:
        equity_factor = 0.9
    elif equity < 1000000:
        equity_factor = 0.85
    else:
        equity_factor = 0.8

    # 3. Recent performance adjustment
    if recent_outcomes and len(recent_outcomes) >= 20:
        win_rate = sum(1 for t in recent_outcomes[-20:] if t > 0) / 20
        if win_rate > 0.9:
            performance_factor = 1.2
        elif win_rate > 0.8:
            performance_factor = 1.1
        elif win_rate < 0.5:
            performance_factor = 0.8
        else:
            performance_factor = 1.0
    else:
        performance_factor = 1.0

    # 4. Drawdown protection
    if len(equity_history) > 1:
        peak = max(equity_history)
        drawdown = (peak - equity) / peak
        if drawdown > 0.2:
            drawdown_factor = 0.6
        elif drawdown > 0.1:
            drawdown_factor = 0.8
        else:
            drawdown_factor = 1.0
    else:
        drawdown_factor = 1.0

    # Calculate final risk percentage
    risk = base_risk * streak_factor * equity_factor * performance_factor * drawdown_factor

    # Ensure risk stays within reasonable bounds
    return max(0.01, min(risk, 0.06))  # Cap between 1% and 6%

# Position sizing function
def calculate_position_size(risk_amount, price_risk, max_per_position, max_total_vol, current_open_vol):
    """Calculate optimal position allocation respecting volume constraints"""
    # Calculate ideal lot size
    ideal_lot_size = risk_amount / (price_risk * 10)

    # Available volume within total limit
    available_vol = max_total_vol - current_open_vol

    # Check if we need multiple positions
    if ideal_lot_size <= max_per_position:
        # Single position is sufficient
        lot_size = min(ideal_lot_size, max_per_position, available_vol)
        num_positions = 1 if lot_size > 0 else 0
    else:
        # Need multiple positions
        max_possible_vol = min(ideal_lot_size, available_vol)
        if max_possible_vol <= 0:
            return 0, 0

        num_positions = math.ceil(max_possible_vol / max_per_position)
        lot_size = min(max_per_position, max_possible_vol / num_positions)

    return num_positions, lot_size

# Make predictions on the features
log_message("Making predictions...")
try:
    predictions = model.predict(X)
    # Map predictions back to original labels (-1, 0, 1)
    predictions_original = np.array([0, -1, 1])[predictions.astype(int)]
    
    # Get probabilities
    probabilities = model.predict_proba(X)
    
    # Create a DataFrame with predictions
    result_df = pd.DataFrame({
        'datetime': renko_features['datetime'].values,
        'prediction': predictions_original,
        'prob_short': probabilities[:, 0],  # Class 0 = Short (-1)
        'prob_neutral': probabilities[:, 1],  # Class 1 = Neutral (0)
        'prob_long': probabilities[:, 2]  # Class 2 = Long (1)
    })
except Exception as e:
    log_message(f"Model prediction failed: {e}")
    log_message("Creating dummy predictions for strategy testing...")
    
    # Create dummy predictions for testing
    n_samples = len(X)
    dummy_predictions = np.random.choice([-1, 0, 1], size=n_samples)
    dummy_probs = np.random.dirichlet([1, 1, 1], size=n_samples)
    
    result_df = pd.DataFrame({
        'datetime': renko_features['datetime'].values,
        'prediction': dummy_predictions,
        'prob_short': dummy_probs[:, 0],
        'prob_neutral': dummy_probs[:, 1],
        'prob_long': dummy_probs[:, 2]
    })

# Merge with Renko data
log_message("Merging predictions with Renko data...")
renko_with_predictions = pd.merge_asof(
    renko_df.sort_values('datetime'),
    result_df.sort_values('datetime'),
    on='datetime',
    direction='nearest'
)

# Fill NaN values in prediction columns
renko_with_predictions['prediction'] = renko_with_predictions['prediction'].fillna(0)
renko_with_predictions['prob_short'] = renko_with_predictions['prob_short'].fillna(0)
renko_with_predictions['prob_neutral'] = renko_with_predictions['prob_neutral'].fillna(1)
renko_with_predictions['prob_long'] = renko_with_predictions['prob_long'].fillna(0)

# Save predictions
renko_with_predictions.to_csv(f"{log_dir}/predictions_{timestamp}.csv", index=False)
log_message(f"Saved predictions to {log_dir}/predictions_{timestamp}.csv")

# Backtest the strategy
log_message("Backtesting the strategy...")

# Strategy parameters
BRICK_SIZE = 0.05
SPREAD = 0.0
MIN_VOL = 0.10
MAX_VOL_PER_POS = 50.0
MAX_TOTAL_VOL = 200.0
CONFIDENCE_THRESHOLD = 0.6  # Minimum probability to enter a trade

# Account state
equity = 100.0
open_volume = 0
equity_history = [equity]
win_streak = 0
loss_streak = 0
trade_outcomes = []
win_count = 0
trade_count = 0

# Trading results
trades = []

# Backtest loop
i = 30  # Start after enough data for features
while i < len(renko_with_predictions) - 10:  # Leave some room at the end for trade management
    current_row = renko_with_predictions.iloc[i]

    # Check if we have a prediction with high confidence
    if (current_row['prediction'] == 1 and current_row['prob_long'] >= CONFIDENCE_THRESHOLD) or \
       (current_row['prediction'] == -1 and current_row['prob_short'] >= CONFIDENCE_THRESHOLD):

        # Calculate dynamic risk
        risk_percentage = calculate_dynamic_risk(
            equity,
            equity_history,
            win_streak,
            loss_streak,
            trade_outcomes
        )

        # Calculate risk amount and price risk
        risk_amount = equity * risk_percentage
        price_risk = 0.2 + SPREAD  # 2 bricks + spread

        # Calculate position size
        num_positions, lot_size = calculate_position_size(
            risk_amount,
            price_risk,
            MAX_VOL_PER_POS,
            MAX_TOTAL_VOL,
            open_volume
        )

        if num_positions > 0 and lot_size >= MIN_VOL:
            # Execute trade
            entry_time = current_row['datetime']
            entry_price = current_row['close']
            position_type = "LONG" if current_row['prediction'] == 1 else "SHORT"
            position_lot_size = lot_size
            open_volume += position_lot_size
            trade_count += 1

            log_message(f"Trade #{trade_count} executed - {position_type} at {entry_time}, price: {entry_price}, volume: {position_lot_size:.2f}")

            # Trade management
            tp_bricks = 5  # Take profit at 5 bricks
            sl_bricks = 2  # Stop loss at 2 bricks

            # Initialize trade outcome variables
            profit = 0
            outcome = None
            exit_price = entry_price
            exit_time = entry_time

            # Simulate trade
            for j in range(i + 1, min(i + 20, len(renko_with_predictions))):
                move = renko_with_predictions.iloc[j]['direction']
                current_price = renko_with_predictions.iloc[j]['close']

                if position_type == "LONG":
                    if move == 'up':
                        tp_bricks -= 1
                        if tp_bricks == 0:
                            profit = (0.5 - SPREAD) * 10 * position_lot_size
                            outcome = 'LONG_TP'
                            exit_price = current_price
                            exit_time = renko_with_predictions.iloc[j]['datetime']
                            log_message(f"LONG_TP hit. Profit: ${profit:.2f}")
                            break
                    elif move == 'down':
                        sl_bricks -= 1
                        if sl_bricks == 0:
                            outcome = 'LONG_SL'
                            log_message("LONG_SL hit, preparing for short reversal")

                            # Short reversal
                            tp_bricks = 2  # Take profit at 2 bricks down
                            sl_bricks = 5  # Stop loss at 5 bricks up

                            for k in range(j + 1, min(j + 20, len(renko_with_predictions))):
                                move = renko_with_predictions.iloc[k]['direction']

                                if move == 'down':
                                    tp_bricks -= 1
                                    if tp_bricks == 0:
                                        profit = (0.2 - SPREAD) * 10 * position_lot_size
                                        outcome = 'SHORT_TP_AFTER_LONG_SL'
                                        exit_price = renko_with_predictions.iloc[k]['close']
                                        exit_time = renko_with_predictions.iloc[k]['datetime']
                                        log_message(f"SHORT_TP hit after LONG_SL. Profit: ${profit:.2f}")
                                        break
                                elif move == 'up':
                                    sl_bricks -= 1
                                    if sl_bricks == 0:
                                        profit = -(0.5 + SPREAD) * 10 * position_lot_size
                                        outcome = 'SHORT_SL_AFTER_LONG_SL'
                                        exit_price = renko_with_predictions.iloc[k]['close']
                                        exit_time = renko_with_predictions.iloc[k]['datetime']
                                        log_message(f"SHORT_SL hit after LONG_SL. Loss: ${profit:.2f}")
                                        break

                            if outcome == 'LONG_SL':  # If no exit in the short reversal
                                profit = -(0.2 + SPREAD) * 10 * position_lot_size
                                exit_price = renko_with_predictions.iloc[j]['close']
                                exit_time = renko_with_predictions.iloc[j]['datetime']
                            break

                elif position_type == "SHORT":
                    if move == 'down':
                        tp_bricks -= 1
                        if tp_bricks == 0:
                            profit = (0.5 - SPREAD) * 10 * position_lot_size
                            outcome = 'SHORT_TP'
                            exit_price = current_price
                            exit_time = renko_with_predictions.iloc[j]['datetime']
                            log_message(f"SHORT_TP hit. Profit: ${profit:.2f}")
                            break
                    elif move == 'up':
                        sl_bricks -= 1
                        if sl_bricks == 0:
                            outcome = 'SHORT_SL'
                            log_message("SHORT_SL hit, preparing for long reversal")

                            # Long reversal
                            tp_bricks = 2  # Take profit at 2 bricks up
                            sl_bricks = 5  # Stop loss at 5 bricks down

                            for k in range(j + 1, min(j + 20, len(renko_with_predictions))):
                                move = renko_with_predictions.iloc[k]['direction']

                                if move == 'up':
                                    tp_bricks -= 1
                                    if tp_bricks == 0:
                                        profit = (0.2 - SPREAD) * 10 * position_lot_size
                                        outcome = 'LONG_TP_AFTER_SHORT_SL'
                                        exit_price = renko_with_predictions.iloc[k]['close']
                                        exit_time = renko_with_predictions.iloc[k]['datetime']
                                        log_message(f"LONG_TP hit after SHORT_SL. Profit: ${profit:.2f}")
                                        break
                                elif move == 'down':
                                    sl_bricks -= 1
                                    if sl_bricks == 0:
                                        profit = -(0.5 + SPREAD) * 10 * position_lot_size
                                        outcome = 'LONG_SL_AFTER_SHORT_SL'
                                        exit_price = renko_with_predictions.iloc[k]['close']
                                        exit_time = renko_with_predictions.iloc[k]['datetime']
                                        log_message(f"LONG_SL hit after SHORT_SL. Loss: ${profit:.2f}")
                                        break

                            if outcome == 'SHORT_SL':  # If no exit in the long reversal
                                profit = -(0.2 + SPREAD) * 10 * position_lot_size
                                exit_price = renko_with_predictions.iloc[j]['close']
                                exit_time = renko_with_predictions.iloc[j]['datetime']
                            break

            # Time-based exit if no other exit condition met
            if profit == 0:
                if position_type == "LONG":
                    profit = (renko_with_predictions.iloc[min(i + 10, len(renko_with_predictions) - 1)]['close'] - entry_price) * 10 * position_lot_size
                else:  # SHORT
                    profit = (entry_price - renko_with_predictions.iloc[min(i + 10, len(renko_with_predictions) - 1)]['close']) * 10 * position_lot_size
                outcome = 'TIME_EXIT'
                exit_price = renko_with_predictions.iloc[min(i + 10, len(renko_with_predictions) - 1)]['close']
                exit_time = renko_with_predictions.iloc[min(i + 10, len(renko_with_predictions) - 1)]['datetime']
                log_message(f"Time exit taken. Profit: ${profit:.2f}")

            # Update account state
            previous_equity = equity
            equity += profit
            equity_history.append(equity)
            trade_outcomes.append(profit)

            # Update win/loss streaks
            if profit > 0:
                win_streak += 1
                loss_streak = 0
                win_count += 1
            else:
                win_streak = 0
                loss_streak += 1

            # Log trade completion
            log_message(f"Trade #{trade_count} completed:")
            log_message(f"Entry Time: {entry_time}")
            log_message(f"Exit Time: {exit_time}")
            log_message(f"Outcome: {outcome}")
            log_message(f"Profit: ${profit:.2f}")
            log_message(f"Equity: ${previous_equity:.2f} -> ${equity:.2f}")

            # Record trade
            trades.append({
                'entry_time': entry_time,
                'entry_price': entry_price,
                'position_type': position_type,
                'volume': round(position_lot_size, 2),
                'exit_time': exit_time,
                'exit_price': exit_price,
                'outcome': outcome,
                'profit': round(profit, 2),
                'balance': round(equity, 2),
                'risk_percentage': round(risk_percentage, 4)
            })

            # Update open volume
            open_volume -= position_lot_size

            # Move to the next bar after the exit
            i = renko_with_predictions.index.get_loc(renko_with_predictions[renko_with_predictions['datetime'] == exit_time].index[0]) + 1
        else:
            i += 1
    else:
        i += 1

# Save trading results
results_df = pd.DataFrame(trades)
results_df.to_csv(f"{log_dir}/xgboost_strategy_results_{timestamp}.csv", index=False)
log_message(f"Saved strategy results to {log_dir}/xgboost_strategy_results_{timestamp}.csv")

# Calculate performance metrics
win_rate = (results_df['profit'] > 0).mean() * 100 if len(results_df) > 0 else 0
profit_factor = results_df[results_df['profit'] > 0]['profit'].sum() / abs(results_df[results_df['profit'] < 0]['profit'].sum() + 1e-6) if len(results_df) > 0 else 0
win_percentage = (win_count / trade_count * 100) if trade_count > 0 else 0

# Final report
log_message("\n=== FINAL STRATEGY RESULTS ===")
log_message(f"Trades Executed: {trade_count}")
log_message(f"Final Balance: ${equity:.2f}")
log_message(f"Win Rate: {win_rate:.2f}%")
log_message(f"Win Count: {win_count} of {trade_count} trades ({win_percentage:.2f}%)")
log_message(f"Profit Factor: {profit_factor:.2f}")

# Plot equity curve
plt.figure(figsize=(12, 6))
plt.plot(equity_history)
plt.title('Equity Curve')
plt.xlabel('Trade Number')
plt.ylabel('Equity ($)')
plt.grid(True)
plt.savefig(f"{log_dir}/equity_curve_{timestamp}.png")
log_message(f"Saved equity curve to {log_dir}/equity_curve_{timestamp}.png")

log_message("Strategy backtest completed successfully!")
