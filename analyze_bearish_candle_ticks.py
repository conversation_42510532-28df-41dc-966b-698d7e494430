import pandas as pd
import re

def analyze_bearish_candle_ticks(file_path):
    """
    Analyzes tick data to determine the average number of ticks
    within 1-minute bearish candles.

    Args:
        file_path (str): The absolute path to the CSV tick data file.

    Returns:
        float: The average number of ticks in a 1-minute bearish candle,
               or None if no bearish candles are found.
    """
    try:
        # Load the tick data, skipping the first row which seems to be a malformed header
        df = pd.read_csv(file_path, skiprows=1, header=None)

        # Extract time and price from the first column (tuple-like string)
        # The regex looks for numbers within the parentheses, separated by commas.
        # It specifically targets the first two numbers for time and price.
        df[['raw_time', 'price']] = df[0].astype(str).str.extract(r'\((\d+),\s*([\d.]+)')

        # Convert 'raw_time' to datetime (assuming it's a Unix timestamp in seconds)
        df['time'] = pd.to_datetime(df['raw_time'], unit='s')

        # Convert 'price' to numeric
        df['price'] = pd.to_numeric(df['price'])

        # Set 'time' as the index
        df = df.set_index('time')

        # Resample to 1-minute OHLC candles and count ticks
        ohlc_df = df['price'].resample('1min').ohlc()
        tick_counts = df['price'].resample('1min').size()

        # Combine OHLC and tick counts into a single DataFrame
        candle_data = pd.concat([ohlc_df, tick_counts.rename('tick_count')], axis=1)

        # Identify bearish candles (close price < open price)
        bearish_candles = candle_data[candle_data['close'] < candle_data['open']]

        if not bearish_candles.empty:
            average_ticks = bearish_candles['tick_count'].mean()
            print(f"Average number of ticks in a 1-minute bearish candle: {average_ticks:.2f}")
            return average_ticks
        else:
            print("No 1-minute bearish candles found in the data.")
            return None

    except FileNotFoundError:
        print(f"Error: File not found at {file_path}")
        return None
    except Exception as e:
        print(f"An error occurred: {e}")
        return None

# Specify the path to your CSV file
file_path = r"C:\Users\<USER>\META\Boom_1000_Index_7days_20250619_20250626.csv"

# Run the analysis
analyze_bearish_candle_ticks(file_path)