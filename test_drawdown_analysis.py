import pytest
import pandas as pd
import numpy as np
from comprehensive_drawdown_analysis import calculate_high_water_marks_and_drawdowns, calculate_risk_of_ruin


class TestDrawdownAnalysis:
    
    @pytest.fixture
    def synthetic_equity_curve(self):
        """Create a synthetic equity curve with known drawdown characteristics"""
        # Create a synthetic equity curve: start at 10000, go to 15000, drawdown to 12000, then to 18000
        equity_values = [10000, 12000, 15000, 13000, 12000, 14000, 16000, 18000]
        pnl_values = [0] + [equity_values[i] - equity_values[i-1] for i in range(1, len(equity_values))]
        
        return pd.DataFrame({
            'pnl': pnl_values,
            'capital': equity_values,
            'entry_time': pd.date_range('2023-01-01', periods=len(equity_values), freq='D')
        })
    
    @pytest.fixture
    def synthetic_trading_data(self):
        """Create synthetic trading data with known win/loss pattern"""
        return pd.DataFrame({
            'pnl': [100, -50, 200, -75, 150, -100, 300, -25, 50, -125],
            'entry_time': pd.date_range('2023-01-01', periods=10, freq='D')
        })
    
    def test_drawdown_calculation_synthetic_curve(self, synthetic_equity_curve):
        """Test drawdown calculation on synthetic equity curve with known drawdowns"""
        results = calculate_high_water_marks_and_drawdowns(synthetic_equity_curve)
        
        # Maximum drawdown should be 20% (from 15000 to 12000)
        expected_max_drawdown = -20.0  # (12000 - 15000) / 15000 * 100
        assert abs(results['maximum_drawdown'] - expected_max_drawdown) < 0.01
        
        # Verify high water marks are calculated correctly
        expected_hwm = [10000, 12000, 15000, 15000, 15000, 15000, 16000, 18000]
        calculated_hwm = synthetic_equity_curve['high_water_mark'].tolist()
        assert calculated_hwm == expected_hwm
    
    def test_drawdown_zero_case(self):
        """Test case where equity only goes up (no drawdown)"""
        data = pd.DataFrame({
            'pnl': [100, 200, 150, 300],
            'capital': [1100, 1300, 1450, 1750],
            'entry_time': pd.date_range('2023-01-01', periods=4, freq='D')
        })
        
        results = calculate_high_water_marks_and_drawdowns(data)
        
        # Should have zero drawdown
        assert results['maximum_drawdown'] == 0.0
        assert results['max_drawdown_duration'] == 0
        
    def test_drawdown_duration_calculation(self):
        """Test drawdown duration calculation"""
        # Create data with specific drawdown periods
        equity_values = [1000, 1100, 1200, 1000, 900, 800, 1000, 1300, 1400, 1200, 1100]
        pnl_values = [0] + [equity_values[i] - equity_values[i-1] for i in range(1, len(equity_values))]
        
        data = pd.DataFrame({
            'pnl': pnl_values,
            'capital': equity_values,
            'entry_time': pd.date_range('2023-01-01', periods=len(equity_values), freq='D')
        })
        
        results = calculate_high_water_marks_and_drawdowns(data)
        
        # Should have drawdown periods: [4, 2] (from peak 1200 for 4 trades, from peak 1400 for 2 trades)
        assert results['max_drawdown_duration'] >= 4  # Longest drawdown period
        assert results['total_drawdown_periods'] >= 1  # At least one drawdown period
    
    def test_risk_of_ruin_calculation(self, synthetic_trading_data):
        """Test risk of ruin calculation with known probabilities"""
        results = calculate_risk_of_ruin(synthetic_trading_data)
        
        # Verify win probability calculation
        wins = (synthetic_trading_data['pnl'] > 0).sum()
        total_trades = len(synthetic_trading_data)
        expected_win_prob = wins / total_trades
        
        assert abs(results['win_probability'] - expected_win_prob) < 0.001
        assert abs(results['loss_probability'] - (1 - expected_win_prob)) < 0.001
        
        # Risk of ruin should be between 0 and 1
        assert 0 <= results['risk_of_ruin'] <= 1
    
    def test_risk_of_ruin_edge_cases(self):
        """Test risk of ruin with edge cases"""
        # All wins case
        all_wins_data = pd.DataFrame({
            'pnl': [100, 200, 150, 300],
            'entry_time': pd.date_range('2023-01-01', periods=4, freq='D')
        })
        
        results = calculate_risk_of_ruin(all_wins_data)
        assert results['risk_of_ruin'] == 0.0  # No risk if all trades win
        
        # All losses case
        all_losses_data = pd.DataFrame({
            'pnl': [-100, -200, -150, -300],
            'entry_time': pd.date_range('2023-01-01', periods=4, freq='D')
        })
        
        results = calculate_risk_of_ruin(all_losses_data)
        assert results['risk_of_ruin'] == 1.0  # Certain ruin if all trades lose
    
    def test_drawdown_formula_validation(self):
        """Test drawdown formula against manually calculated values"""
        # Create specific equity curve for manual validation
        equity = [10000, 11000, 12000, 10000, 8000, 9000, 13000]
        pnl = [0] + [equity[i] - equity[i-1] for i in range(1, len(equity))]
        
        data = pd.DataFrame({
            'pnl': pnl,
            'capital': equity,
            'entry_time': pd.date_range('2023-01-01', periods=len(equity), freq='D')
        })
        
        results = calculate_high_water_marks_and_drawdowns(data)
        
        # Manual calculation:
        # High water marks: [10000, 11000, 12000, 12000, 12000, 12000, 13000]
        # At index 4: equity = 8000, hwm = 12000, drawdown = (8000-12000)/12000 = -33.33%
        expected_max_drawdown = -33.33333333333333
        
        assert abs(results['maximum_drawdown'] - expected_max_drawdown) < 0.01
        
        # Verify the specific drawdown values in the dataframe
        expected_drawdowns_pct = [0.0, 0.0, 0.0, -16.666666666666668, -33.33333333333333, -25.0, 0.0]
        calculated_drawdowns = data['drawdown_percentage'].tolist()
        
        for expected, calculated in zip(expected_drawdowns_pct, calculated_drawdowns):
            assert abs(expected - calculated) < 0.01


# Additional test functions outside the class for compatibility
def test_empty_dataframe_handling():
    """Test handling of empty dataframes"""
    empty_df = pd.DataFrame(columns=['pnl', 'entry_time'])
    
    results = calculate_high_water_marks_and_drawdowns(empty_df)
    
    # Should handle empty data gracefully
    assert results['maximum_drawdown'] == 0.0 or pd.isna(results['maximum_drawdown'])
    assert results['max_drawdown_duration'] == 0


def test_single_trade_scenario():
    """Test scenario with only one trade"""
    single_trade_df = pd.DataFrame({
        'pnl': [100],
        'entry_time': ['2023-01-01']
    })
    
    results = calculate_high_water_marks_and_drawdowns(single_trade_df)
    
    # With only one trade, there should be no drawdown
    assert results['maximum_drawdown'] == 0.0
    assert results['max_drawdown_duration'] == 0


if __name__ == "__main__":
    # Run a quick test
    pytest.main([__file__, "-v"])
