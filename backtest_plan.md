# Plan for Implementing Numpy Vectorized Backtest

## 1. Data Loading and Preparation:

*   Load the data from `stpRNG_1min.csv` into a numpy array.
*   Extract the `close` prices and the `long` and `short` signals from the existing `smart_labelling_range_filter.py` script.
*   Ensure the data types are appropriate for numerical calculations.

## 2. Backtesting Logic (Vectorized):

*   Implement the backtesting logic using numpy vectorized operations.
*   This will involve calculating the profit/loss for each trade based on the `close` prices and the `long` and `short` signals.
*   Account for the minimum volume (0.20 lots) and maximum volume (50 lots) constraints.
*   Implement the limit per direction (200 lots) constraint.

## 3. Fractional Kelly Algorithm:

*   Implement the 45% fractional Kelly algorithm to determine the optimal position size for each trade.
*   This will involve calculating the win rate and the average win/loss ratio based on the backtesting results.

## 4. Performance Metrics:

*   Calculate and report the following performance metrics:
    *   Total profit/loss
    *   Win rate
    *   Average win/loss ratio
    *   Maximum drawdown
    *   Sharpe ratio

## 5. Code Structure:

*   Create a new python script named `vectorized_backtest.py`.
*   Include the necessary functions for data loading, signal generation, backtesting, and performance evaluation.
*   Ensure the code is well-documented and easy to understand.

## 6. Testing and Validation:

*   <PERSON>oughly test the backtesting script to ensure it is working correctly.
*   Validate the results against a known benchmark or a simpler backtesting implementation.

## Mermaid Diagram:

```mermaid
graph LR
    A[Load Data (stpRNG_1min.csv)] --> B(Extract Signals (smart_labelling_range_filter.py));
    B --> C{Vectorized Backtesting Logic};
    C --> D{Fractional Kelly Algorithm};
    D --> E[Calculate Performance Metrics];
    E --> F[Report Results];