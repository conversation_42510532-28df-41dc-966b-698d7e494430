"""
BOOM 1000 INDEX - FIXED LOT BACKTEST
====================================
Standalone backtest script using the trained UBJ XGBoost model
with fixed 0.20 lot position sizing.

Features:
- Loads pre-trained UBJ model
- Fixed 0.20 lot position sizing
- Comprehensive backtest metrics
- Trade-by-trade analysis
"""

import pandas as pd
import numpy as np
import xgboost as xgb
import pickle
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class KellyBacktester:
    def __init__(self, model_file, scaler_file, features_file, data_file):
        """
        Initialize backtest with pre-trained model files
        
        Args:
            model_file: Path to UBJ model file
            scaler_file: Path to pickle scaler file
            features_file: Path to pickle features file
            data_file: Path to CSV data file
        """
        self.model_file = model_file
        self.scaler_file = scaler_file
        self.features_file = features_file
        self.data_file = data_file
        
        # Trading parameters
        self.starting_capital = 20.0
        self.min_lot_size = 0.20
        self.max_lot_size = 50.0
        self.max_total_lot_volume = 120  # Total lot volume limit across all positions
        self.tick_value = 0.001
        self.tick_size = 0.001
        
        # <PERSON> parameters
        self.kelly_fraction = 0.25  # Use 25% of full Kelly (fractional Kelly)
        self.lookback_window = 100  # Trades to look back for Kelly calculation
        
        # Model components
        self.model = None
        self.scaler = None
        self.feature_names = None
        self.df = None
        
    def load_model_components(self):
        """Load the trained model, scaler, and feature names"""
        print("Loading model components...")
        
        # Load XGBoost model from UBJ
        self.model = xgb.XGBClassifier()
        self.model.load_model(self.model_file)
        print(f"Model loaded: {self.model_file}")
        
        # Load scaler
        with open(self.scaler_file, 'rb') as f:
            self.scaler = pickle.load(f)
        print(f"Scaler loaded: {self.scaler_file}")
        
        # Load feature names
        with open(self.features_file, 'rb') as f:
            self.feature_names = pickle.load(f)
        print(f"Features loaded: {len(self.feature_names)} features")
        
    def load_and_prepare_data(self, precomputed_features_file=None):
        """Load and prepare the trading data"""
        print("Loading and preparing data...")
        
        # Try to load pre-computed features first
        if precomputed_features_file and os.path.exists(precomputed_features_file):
            print(f"Loading pre-computed features from {precomputed_features_file}")
            self.df = pd.read_csv(precomputed_features_file)
            self.df['datetime'] = pd.to_datetime(self.df['timestamp'], unit='s')
            print(f"✅ Loaded {len(self.df)} rows with pre-computed features")
            return
        
        # Parse raw tick data if no pre-computed features
        raw_df = pd.read_csv(self.data_file)
        
        # Create proper dataframe from the CSV
        self.df = raw_df.copy()
        self.df['mid_price'] = (self.df['bid'] + self.df['ask']) / 2
        self.df['spread'] = self.df['ask'] - self.df['bid']
        self.df['datetime'] = pd.to_datetime(self.df['time_msc'])
        
        # Create timestamp from datetime for sorting
        self.df['timestamp'] = self.df['datetime'].astype('int64') // 10**9
        self.df = self.df.sort_values('timestamp').reset_index(drop=True)
        
        print(f"Loaded {len(self.df)} ticks")
        
        # Create all features (same as training script)
        self._create_features()
        
        # Save features for future use
        features_output = f"boom_features_computed_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        self.df.to_csv(features_output, index=False)
        print(f"Features saved to {features_output} for future use")
        
    def _create_features(self):
        """Create the same features used in training"""
        print("Creating features...")
        
        # Basic price features
        self.df['price_change'] = self.df['mid_price'].diff()
        self.df['price_change_pct'] = (self.df['price_change'] / self.df['mid_price'].shift(1)) * 100
        self.df['log_return'] = np.log(self.df['mid_price'] / self.df['mid_price'].shift(1))
        
        # Volatility features
        for window in [5, 10, 20, 50]:
            self.df[f'volatility_{window}'] = self.df['price_change'].rolling(window=window).std()
            self.df[f'rolling_mean_{window}'] = self.df['mid_price'].rolling(window=window).mean()
            self.df[f'rolling_max_{window}'] = self.df['mid_price'].rolling(window=window).max()
            self.df[f'rolling_min_{window}'] = self.df['mid_price'].rolling(window=window).min()
            self.df[f'price_position_{window}'] = (self.df['mid_price'] - self.df[f'rolling_min_{window}']) / (self.df[f'rolling_max_{window}'] - self.df[f'rolling_min_{window}'])
        
        # Momentum features
        for period in [3, 5, 10, 20]:
            self.df[f'momentum_{period}'] = self.df['mid_price'] - self.df['mid_price'].shift(period)
            self.df[f'roc_{period}'] = ((self.df['mid_price'] / self.df['mid_price'].shift(period)) - 1) * 100
        
        # RSI calculation
        for period in [7, 14, 21]:
            delta = self.df['mid_price'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            self.df[f'rsi_{period}'] = 100 - (100 / (1 + rs))
        
        # Spike detection
        self.df['is_spike'] = ((self.df['price_change_pct'] > 0.1) & (self.df['price_change'] > 0)).astype(int)
        self.df['spike_magnitude'] = np.where(self.df['is_spike'] == 1, self.df['price_change'], 0)
        
        # Ticks since last spike
        spike_indices = self.df[self.df['is_spike'] == 1].index
        self.df['ticks_since_spike'] = 0
        for i in range(len(self.df)):
            last_spike = spike_indices[spike_indices < i]
            if len(last_spike) > 0:
                self.df.iloc[i, self.df.columns.get_loc('ticks_since_spike')] = i - last_spike[-1]
            else:
                self.df.iloc[i, self.df.columns.get_loc('ticks_since_spike')] = i
        
        # Double spike detection
        self.df['is_double_spike'] = 0
        for i in range(1, len(spike_indices)):
            if spike_indices[i] - spike_indices[i-1] <= 100:
                self.df.iloc[spike_indices[i-1], self.df.columns.get_loc('is_double_spike')] = 1
                self.df.iloc[spike_indices[i], self.df.columns.get_loc('is_double_spike')] = 1
        
        # Support/Resistance levels
        for window in [20, 50]:
            self.df[f'support_{window}'] = self.df['mid_price'].rolling(window=window).min()
            self.df[f'resistance_{window}'] = self.df['mid_price'].rolling(window=window).max()
            self.df[f'distance_to_support_{window}'] = self.df['mid_price'] - self.df[f'support_{window}']
            self.df[f'distance_to_resistance_{window}'] = self.df[f'resistance_{window}'] - self.df['mid_price']
        
        # Moving average crossovers
        for fast, slow in [(5, 20), (10, 50)]:
            ma_fast = self.df['mid_price'].rolling(window=fast).mean()
            ma_slow = self.df['mid_price'].rolling(window=slow).mean()
            self.df[f'ma_cross_{fast}_{slow}'] = (ma_fast > ma_slow).astype(int)
            self.df[f'ma_distance_{fast}_{slow}'] = ma_fast - ma_slow
        
        # Price acceleration
        self.df['acceleration'] = self.df['price_change'].diff()
        
        # Spread features
        self.df['spread_change'] = self.df['spread'].diff()
        self.df['spread_volatility'] = self.df['spread'].rolling(window=10).std()
        
        # Time features
        self.df['hour'] = self.df['datetime'].dt.hour
        self.df['minute'] = self.df['datetime'].dt.minute
        self.df['second'] = self.df['datetime'].dt.second
        
        # Fractal patterns
        for window in [5, 10]:
            self.df[f'local_max_{window}'] = (self.df['mid_price'] == self.df['mid_price'].rolling(window=window, center=True).max()).astype(int)
            self.df[f'local_min_{window}'] = (self.df['mid_price'] == self.df['mid_price'].rolling(window=window, center=True).min()).astype(int)
        
        print(f"Created {len([col for col in self.df.columns if col in self.feature_names])} matching features")
    
    def calculate_kelly_position_size(self, recent_trades, current_capital, probability):
        """
        Calculate position size using fractional Kelly criterion
        
        Args:
            recent_trades: List of recent trade results
            current_capital: Current account balance
            probability: Model confidence probability
            
        Returns:
            Position size in lots
        """
        if len(recent_trades) < 10:  # Need minimum trades for Kelly
            return self.min_lot_size
        
        # Calculate win rate and average win/loss
        wins = [trade for trade in recent_trades if trade > 0]
        losses = [trade for trade in recent_trades if trade < 0]
        
        if len(wins) == 0 or len(losses) == 0:
            return self.min_lot_size
        
        win_rate = len(wins) / len(recent_trades)
        avg_win = np.mean(wins)
        avg_loss = abs(np.mean(losses))
        
        # Kelly formula: f = (bp - q) / b
        # Where: b = avg_win/avg_loss, p = win_rate, q = 1-win_rate
        b = avg_win / avg_loss
        p = win_rate
        q = 1 - win_rate
        
        kelly_fraction_full = (b * p - q) / b
        
        # Apply fractional Kelly and probability adjustment
        kelly_fraction_adj = kelly_fraction_full * self.kelly_fraction * probability
        
        # Convert to position size
        risk_amount = current_capital * kelly_fraction_adj
        position_size = risk_amount / (avg_loss * self.tick_value)
        
        # Apply constraints
        position_size = max(self.min_lot_size, min(position_size, self.max_lot_size))
        
        return round(position_size, 2)
    
    def run_backtest(self, confidence_threshold=0.6):
        """Simulate trading strategy using the trained model - EXACT COPY from comprehensive script"""
        print(f"SIMULATING TRADING STRATEGY (confidence >= {confidence_threshold})")
        
        if self.model is None:
            print("No model trained yet!")
            return
        
        # Initialize trading variables
        current_capital = self.starting_capital
        trades = []
        recent_trades = []
        position_size = self.min_lot_size  # Fixed lot size
        
        # Prepare all data for prediction  
        feature_data = self.df[self.feature_names].dropna()
        feature_data_scaled = self.scaler.transform(feature_data)
        
        # Get predictions and probabilities
        predictions = self.model.predict(feature_data_scaled)
        probabilities = self.model.predict_proba(feature_data_scaled)[:, 1]
        
        # High-confidence trading signals
        trade_signals = probabilities >= confidence_threshold
        
        clean_df = self.df[['mid_price', 'datetime'] + self.feature_names].dropna()

        for i, (_, row) in enumerate(clean_df.iterrows()):
            if i >= len(trade_signals):
                break
                
            if trade_signals[i] and i < len(clean_df) - 100:  # Ensure we have exit data
                # Entry with 1 tick execution delay (938ms delay simulation)
                entry_idx = min(i + 1, len(clean_df) - 1)  # Next tick due to execution delay
                entry_price = clean_df.iloc[entry_idx]['mid_price']
                entry_time = clean_df.iloc[entry_idx]['datetime']
                
                # Exit after 50 ticks (or find optimal exit)
                exit_idx = min(i + 50, len(clean_df) - 1)
                exit_price = clean_df.iloc[exit_idx]['mid_price']
                exit_time = clean_df.iloc[exit_idx]['datetime']
                
                # P&L for SELL position
                price_change = entry_price - exit_price
                ticks_gained = price_change / 0.001  # tick size
                pnl = ticks_gained * self.tick_value * position_size
                
                current_capital += pnl
                
                trades.append({
                    'entry_time': entry_time,
                    'exit_time': exit_time,
                    'entry_price': entry_price,
                    'exit_price': exit_price,
                    'pnl': pnl,
                    'probability': probabilities[i],
                    'capital': current_capital
                })
        
        if trades:
            trades_df = pd.DataFrame(trades)
            winning_trades = trades_df[trades_df['pnl'] > 0]
            
            print(f"TRADING SIMULATION RESULTS:")
            print(f"Total trades: {len(trades_df)}")
            print(f"Winning trades: {len(winning_trades)} ({len(winning_trades)/len(trades_df)*100:.1f}%)")
            print(f"Starting capital: ${self.starting_capital:.2f}")
            print(f"Final capital: ${current_capital:.2f}")
            print(f"Total return: {(current_capital - self.starting_capital) / self.starting_capital * 100:.1f}%")
            print(f"Average trade: ${trades_df['pnl'].mean():.2f}")
            print(f"Best trade: ${trades_df['pnl'].max():.2f}")
            print(f"Worst trade: ${trades_df['pnl'].min():.2f}")
            
            return trades_df
        else:
            print("No trades generated!")
            return None
    
    def _analyze_results(self, trades, equity_curve, final_capital):
        """Analyze and display backtest results"""
        if not trades:
            print("❌ No trades executed!")
            return None
        
        trades_df = pd.DataFrame(trades)
        equity_df = pd.DataFrame(equity_curve)
        
        # Basic statistics
        total_return = (final_capital - self.starting_capital) / self.starting_capital * 100
        winning_trades = trades_df[trades_df['pnl'] > 0]
        losing_trades = trades_df[trades_df['pnl'] < 0]
        
        win_rate = len(winning_trades) / len(trades_df) * 100
        avg_win = winning_trades['pnl'].mean() if len(winning_trades) > 0 else 0
        avg_loss = abs(losing_trades['pnl'].mean()) if len(losing_trades) > 0 else 0
        profit_factor = (winning_trades['pnl'].sum() / abs(losing_trades['pnl'].sum())) if len(losing_trades) > 0 else float('inf')
        
# Drawdown calculation
        equity_df['peak'] = equity_df['total_equity'].expanding().max()
        equity_df['drawdown'] = (equity_df['total_equity'] - equity_df['peak']) / equity_df['peak'] * 100
        max_drawdown = equity_df['drawdown'].min()

        # Streak analysis
        win_streak = 0
        loss_streak = 0
        max_win_streak = 0
        max_loss_streak = 0
        
        for pnl in trades_df['pnl']:
            if pnl > 0:
                win_streak += 1
                loss_streak = 0
                max_win_streak = max(max_win_streak, win_streak)
            elif pnl < 0:
                loss_streak += 1
                win_streak = 0
                max_loss_streak = max(max_loss_streak, loss_streak)
        
        # Ruin analysis
        critical_balance = self.starting_capital * 0.5  # Ruin threshold at 50% starting capital
        account_ruined = any(trades_df['capital'] < critical_balance)
        
        # Display results
        print(f"FIXED LOT (0.20) BACKTEST RESULTS")
        print("=" * 60)
        print(f"Max Winning Streak: {max_win_streak}")
        print(f"Max Losing Streak: {max_loss_streak}")
        print(f"Account Ruined: {'Yes' if account_ruined else 'No'}")
        print("=" * 60)
        print(f"💰 Starting Capital: ${self.starting_capital:.2f}")
        print(f"💰 Final Capital: ${final_capital:.2f}")
        print(f"📈 Total Return: {total_return:.1f}%")
        print(f"📉 Max Drawdown: {max_drawdown:.1f}%")
        print(f"\n🎯 TRADE STATISTICS:")
        print(f"Total Trades: {len(trades_df)}")
        print(f"Win Rate: {win_rate:.1f}% ({len(winning_trades)}/{len(trades_df)})")
        print(f"Average Win: ${avg_win:.2f}")
        print(f"Average Loss: ${avg_loss:.2f}")
        print(f"Profit Factor: {profit_factor:.2f}")
        print(f"Best Trade: ${trades_df['pnl'].max():.2f}")
        print(f"Worst Trade: ${trades_df['pnl'].min():.2f}")
        print(f"Average Lot Size: {trades_df['lot_size'].mean():.2f}")
        print(f"Max Lot Size Used: {trades_df['lot_size'].max():.2f}")
        print(f"Average Hold Time: {trades_df['hold_ticks'].mean():.1f} ticks")
        
        # Risk metrics
        returns = trades_df['pnl'] / self.starting_capital
        sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252) if returns.std() > 0 else 0
        print(f"\n📊 RISK METRICS:")
        print(f"Sharpe Ratio: {sharpe_ratio:.2f}")
        print(f"Calmar Ratio: {total_return / abs(max_drawdown):.2f}" if max_drawdown != 0 else "Calmar Ratio: N/A")
        
        return {
            'trades_df': trades_df,
            'equity_df': equity_df,
            'final_capital': final_capital,
            'total_return': total_return,
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio
        }

def main():
    """Run the Kelly criterion backtest"""
    print("BOOM 1000 INDEX - FIXED LOT (0.20) BACKTEST")
    print("=" * 60)
    
    # Model files (use the latest ones)
    model_file = "boom_comprehensive_xgboost_target_any_profit_20250627_075549.ubj"
    scaler_file = "boom_comprehensive_scaler_target_any_profit_20250627_075549.pkl"
    features_file = "boom_comprehensive_features_target_any_profit_20250627_075549.pkl"
    data_file = "Boom_1000_Index_7days_20250620_20250627.csv"
    
    # Initialize and run backtest
    backtester = KellyBacktester(model_file, scaler_file, features_file, data_file)
    
    try:
        backtester.load_model_components()
        # Use pre-computed features if available
        precomputed_file = None  # Process raw data instead
        backtester.load_and_prepare_data(precomputed_file)
        results = backtester.run_backtest(confidence_threshold=0.60)
        
        if results is not None:
            print(f"Backtest completed successfully!")
            print(f"Check results above for detailed analysis")
            
            # Save results
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            results.to_csv(f"fixed_lot_backtest_trades_{timestamp}.csv", index=False)
            print(f"💾 Results saved to CSV file")
        
    except Exception as e:
        print(f"Error during backtest: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
