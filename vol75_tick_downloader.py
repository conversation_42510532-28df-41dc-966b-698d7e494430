import MetaTrader5 as mt5
from datetime import datetime, timedelta
import pandas as pd
import pytz

def initialize_mt5(account=5749910, server="Deriv-Demo", password="@Ripper25"):
    if not mt5.initialize():
        print("initialize() failed")
        mt5.shutdown()
        return False
    
    # Login to the account
    authorized = mt5.login(account, password=password, server=server)
    if not authorized:
        print(f"Login failed: account={account}")
        mt5.shutdown()
        return False
        
    return True

def download_ticks(symbol="Volatility 75 (1s) Index", days=5):
    if not initialize_mt5():
        return None
        
    # Set timezone to UTC
    timezone = pytz.timezone("UTC")
    
    # Calculate time range
    utc_now = datetime.now(timezone)
    from_date = utc_now - timedelta(days=days)
    
    # Download ticks
    ticks = mt5.copy_ticks_range(symbol, from_date, utc_now, mt5.COPY_TICKS_ALL)
    
    if ticks is None:
        print(f"Failed to download ticks for {symbol}")
        mt5.shutdown()
        return None
        
    # Convert to DataFrame
    df = pd.DataFrame(ticks)
    df['time'] = pd.to_datetime(df['time'], unit='s')
    
    # Save to CSV
    filename = f"vol75_ticks_{days}days.csv"
    df.to_csv(filename, index=False)
    print(f"Saved {len(df)} ticks to {filename}")
    
    mt5.shutdown()
    return df

if __name__ == "__main__":
    download_ticks()
