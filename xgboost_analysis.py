"""
XGBoost Training Script for Boom 1000 Index - Spike and Double Spike Analysis
Objective: Use raw data to develop a trading strategy with more trades and potential gains.
"""

import pandas as pd
import numpy as np
import xgboost as xgb
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

class BoomXGBoostModel:
    def __init__(self, csv_file):
        self.csv_file = csv_file
        self.df = None
        self.spikes = []
        
    def load_and_prepare_data(self):
        """Load and prepare the data for modeling."""
        print("Loading data...")
        raw_df = pd.read_csv(self.csv_file)
        parsed_data = []
        for _, row in raw_df.iterrows():
            try:
                tuple_str = row['0']
                if tuple_str.startswith('(') and tuple_str.endswith(')'):
                    values = tuple_str[1:-1].split(', ')
                    parsed_data.append({
                        'timestamp': int(values[0]),
                        'bid': float(values[1]),
                        'ask': float(values[2]),
                        'mid_price': (float(values[1]) + float(values[2])) / 2
                    })
            except: continue
        self.df = pd.DataFrame(parsed_data)
        self.df['datetime'] = pd.to_datetime(self.df['timestamp'], unit='s')
        self.df = self.df.sort_values('timestamp').reset_index(drop=True)
        print(f"Loaded {len(self.df)} ticks.")
        
        # Feature extraction
        self.df['price_change'] = self.df['mid_price'].diff()
        self.df['price_change_pct'] = (self.df['price_change'] / self.df['mid_price'].shift(1)) * 100
        self.df['volatility'] = self.df['price_change'].rolling(window=10).std()
        self.df['rolling_mean'] = self.df['mid_price'].rolling(window=10).mean()

        # Label: Favorable tick collection (1 if yes, 0 if no)
        self.df['favorable'] = (self.df['price_change_pct'] > 0.1) & (self.df['price_change'] > 0)
        self.df['favorable'] = self.df['favorable'].shift(-20)  # Shift labels to future

    def train_xgboost_model(self):
        """Train the XGBoost model."""
        print("Training XGBoost model...")
        features = ['price_change', 'price_change_pct', 'volatility', 'rolling_mean']
        X = self.df[features].dropna()
        y = self.df['favorable'].dropna().astype(int)

        # Train-test split
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

        # XGBoost model
        model = xgb.XGBClassifier(use_label_encoder=False, eval_metric='mlogloss')
        model.fit(X_train, y_train)

        # Predictions
        y_pred = model.predict(X_test)

        # Evaluation
        accuracy = accuracy_score(y_test, y_pred)
        precision = precision_score(y_test, y_pred)
        recall = recall_score(y_test, y_pred)
        f1 = f1_score(y_test, y_pred)

        print(f"Accuracy: {accuracy:.2f}")
        print(f"Precision: {precision:.2f}")
        print(f"Recall: {recall:.2f}")
        print(f"F1 Score: {f1:.2f}")

        return model

    def run_analysis(self):
        self.load_and_prepare_data()
        model = self.train_xgboost_model()
        # You can save the model or continue to use it to make predictions

if __name__ == "__main__":
    csv_file = "C:\\Users\\<USER>\\META\\Boom_1000_Index_7days_20250619_20250626.csv"
    analyzer = BoomXGBoostModel(csv_file)
    analyzer.run_analysis()

