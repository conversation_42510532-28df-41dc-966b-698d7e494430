import pandas as pd

def analyze_data(file_path):
    try:
        df = pd.read_csv(file_path)
        print(f"Analyzing data from: {file_path}")
        print(df.describe())
    except FileNotFoundError:
        print(f"Error: File not found at {file_path}")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    file_path = "stpRNG_1min.csv"
    analyze_data(file_path)