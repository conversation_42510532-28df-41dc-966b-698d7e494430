import pandas as pd
import numpy as np
from datetime import datetime, timedelta

print("Loading tick data...")
# Read the tick data
df_ticks = pd.read_csv("C:/Users/<USER>/META/stpRNG_90days_ticks.csv")
df_ticks['datetime'] = pd.to_datetime(df_ticks['datetime'])

# Sort by datetime to ensure chronological order
df_ticks = df_ticks.sort_values('datetime').reset_index(drop=True)

def create_5s_bars(df_ticks):
    print("Creating 5-second bars...")
    # Create 5-second time bins
    df_ticks.set_index('datetime', inplace=True)
    df_5s = pd.DataFrame()
    df_5s['open'] = df_ticks['price'].resample('5s').first()
    df_5s['high'] = df_ticks['price'].resample('5s').max()
    df_5s['low'] = df_ticks['price'].resample('5s').min()
    df_5s['close'] = df_ticks['price'].resample('5s').last()
    df_5s = df_5s.dropna()
    df_5s.reset_index(inplace=True)
    return df_5s

def create_range_bars(df_5s):
    print("Creating 5-second range bars...")
    range_data = []
    current_bar = None
    current_time = None
    
    for idx, row in df_5s.iterrows():
        high, low = row['high'], row['low']
        datetime = row['datetime']
        
        while True:
            if last_brick_direction >= 0 and high >= last_brick_close + brick_size:
                # Upward brick
                new_close = last_brick_close + brick_size
                renko_data.append({
                    'datetime': datetime,
                    'open': last_brick_close,
                    'high': new_close,
                    'low': last_brick_close,
                    'close': new_close,
                    'direction': 'up'
                })
                last_brick_close = new_close
                last_brick_direction = 1
                current_brick_low = last_brick_close
            elif last_brick_direction <= 0 and low <= last_brick_close - brick_size:
                # Downward brick
                new_close = last_brick_close - brick_size
                renko_data.append({
                    'datetime': datetime,
                    'open': last_brick_close,
                    'high': last_brick_close,
                    'low': new_close,
                    'close': new_close,
                    'direction': 'down'
                })
                last_brick_close = new_close
                last_brick_direction = -1
                current_brick_high = last_brick_close
            else:
                break
                
            high = row['high']
            low = row['low']
    
    df_renko = pd.DataFrame(renko_data)
    return df_renko

# Create 5-second bars first
df_5s = create_5s_bars(df_ticks)

# Create Renko bars from 5-second bars
df_renko = create_renko_bars(df_5s, brick_size=0.1)

# Save to CSV
output_file = "stpRNG_renko_5s.csv"
print(f"Saving to {output_file}...")
df_renko.to_csv(output_file, index=False)

print("Done! Summary:")
print(f"Total ticks processed: {len(df_ticks)}")
print(f"Total 5s bars: {len(df_5s)}")
print(f"Total Renko bars: {len(df_renko)}")
print(f"Date range: {df_renko['datetime'].min()} to {df_renko['datetime'].max()}")
print(f"\nFirst few Renko bars:")
print(df_renko.head())
