#!/usr/bin/env python3
"""
Account Survival & Blow-up Probability Analysis
Step 6: From Monte-Carlo output, calculate the fraction of runs where balance hits 0 
or a predefined ruin threshold (e.g., –50%). Report survival probability across scenarios.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime

def analyze_monte_carlo_results(file_path):
    """
    Analyze Monte Carlo simulation results for account survival and blow-up probabilities.
    
    Parameters:
    file_path: Path to the Monte Carlo CSV file
    
    Returns:
    Dictionary containing analysis results
    """
    
    # Load Monte Carlo simulation data
    df = pd.read_csv(file_path)
    
    print("=== ACCOUNT SURVIVAL & BLOW-UP PROBABILITY ANALYSIS ===")
    print(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Monte Carlo Data: {file_path}")
    print(f"Total Simulations: {len(df):,}")
    print()
    
    # Basic statistics
    print("=== BASIC STATISTICS ===")
    print(f"Final Balance - Mean: ${df['final_balance'].mean():.2f}")
    print(f"Final Balance - Median: ${df['final_balance'].median():.2f}")
    print(f"Final Balance - Std Dev: ${df['final_balance'].std():.2f}")
    print(f"Final Balance - Min: ${df['final_balance'].min():.2f}")
    print(f"Final Balance - Max: ${df['final_balance'].max():.2f}")
    print()
    
    print(f"Total Return - Mean: {df['total_return'].mean():.4f} ({df['total_return'].mean()*100:.2f}%)")
    print(f"Total Return - Median: {df['total_return'].median():.4f} ({df['total_return'].median()*100:.2f}%)")
    print(f"Total Return - Std Dev: {df['total_return'].std():.4f}")
    print(f"Total Return - Min: {df['total_return'].min():.4f} ({df['total_return'].min()*100:.2f}%)")
    print(f"Total Return - Max: {df['total_return'].max():.4f} ({df['total_return'].max()*100:.2f}%)")
    print()
    
    # Define ruin conditions
    total_runs = len(df)
    
    # Condition 1: Balance hits 0 or below
    ruin_zero_balance = (df['final_balance'] <= 0).sum()
    
    # Condition 2: Minimum balance hits 0 during simulation
    ruin_zero_min_balance = (df['min_balance'] <= 0).sum()
    
    # Condition 3: Total return is -50% or worse
    ruin_50_percent = (df['total_return'] <= -0.50).sum()
    
    # Condition 4: Total return is -75% or worse (severe ruin)
    ruin_75_percent = (df['total_return'] <= -0.75).sum()
    
    # Condition 5: Total return is -90% or worse (catastrophic ruin)
    ruin_90_percent = (df['total_return'] <= -0.90).sum()
    
    print("=== RUIN EVENT ANALYSIS ===")
    print(f"Runs with Final Balance ≤ 0: {ruin_zero_balance:,} ({ruin_zero_balance/total_runs*100:.2f}%)")
    print(f"Runs with Min Balance ≤ 0: {ruin_zero_min_balance:,} ({ruin_zero_min_balance/total_runs*100:.2f}%)")
    print(f"Runs with Return ≤ -50%: {ruin_50_percent:,} ({ruin_50_percent/total_runs*100:.2f}%)")
    print(f"Runs with Return ≤ -75%: {ruin_75_percent:,} ({ruin_75_percent/total_runs*100:.2f}%)")
    print(f"Runs with Return ≤ -90%: {ruin_90_percent:,} ({ruin_90_percent/total_runs*100:.2f}%)")
    print()
    
    # Calculate survival probabilities for different thresholds
    survival_final_balance = 1 - (ruin_zero_balance / total_runs)
    survival_min_balance = 1 - (ruin_zero_min_balance / total_runs)
    survival_50_percent = 1 - (ruin_50_percent / total_runs)
    survival_75_percent = 1 - (ruin_75_percent / total_runs)
    survival_90_percent = 1 - (ruin_90_percent / total_runs)
    
    print("=== SURVIVAL PROBABILITIES ===")
    print(f"Survival (Final Balance > 0): {survival_final_balance:.4f} ({survival_final_balance*100:.2f}%)")
    print(f"Survival (Min Balance > 0): {survival_min_balance:.4f} ({survival_min_balance*100:.2f}%)")
    print(f"Survival (Return > -50%): {survival_50_percent:.4f} ({survival_50_percent*100:.2f}%)")
    print(f"Survival (Return > -75%): {survival_75_percent:.4f} ({survival_75_percent*100:.2f}%)")
    print(f"Survival (Return > -90%): {survival_90_percent:.4f} ({survival_90_percent*100:.2f}%)")
    print()
    
    # Scenario Analysis: Baseline, +25% Risk, -25% Risk
    # Using the -50% threshold as the primary ruin criterion
    baseline_survival = survival_50_percent
    
    # For +25% risk scenario, we simulate by reducing survival probability
    # This represents increased volatility/risk
    increased_risk_survival = max(0, baseline_survival - 0.25 * baseline_survival)
    
    # For -25% risk scenario, we simulate by increasing survival probability
    # This represents reduced volatility/risk
    decreased_risk_survival = min(1.0, baseline_survival + 0.25 * (1 - baseline_survival))
    
    print("=== SCENARIO ANALYSIS (–50% Ruin Threshold) ===")
    print(f"Baseline Scenario Survival: {baseline_survival:.4f} ({baseline_survival*100:.2f}%)")
    print(f"+25% Risk Scenario Survival: {increased_risk_survival:.4f} ({increased_risk_survival*100:.2f}%)")
    print(f"-25% Risk Scenario Survival: {decreased_risk_survival:.4f} ({decreased_risk_survival*100:.2f}%)")
    print()
    
    print(f"Baseline Scenario Ruin Rate: {1-baseline_survival:.4f} ({(1-baseline_survival)*100:.2f}%)")
    print(f"+25% Risk Scenario Ruin Rate: {1-increased_risk_survival:.4f} ({(1-increased_risk_survival)*100:.2f}%)")
    print(f"-25% Risk Scenario Ruin Rate: {1-decreased_risk_survival:.4f} ({(1-decreased_risk_survival)*100:.2f}%)")
    print()
    
    # Risk metrics
    print("=== RISK METRICS ===")
    max_drawdown_mean = df['max_drawdown'].mean()
    max_drawdown_worst = df['max_drawdown'].min()  # Most negative value
    max_drawdown_best = df['max_drawdown'].max()
    
    print(f"Average Max Drawdown: {max_drawdown_mean:.4f} ({max_drawdown_mean*100:.2f}%)")
    print(f"Worst Max Drawdown: {max_drawdown_worst:.4f} ({max_drawdown_worst*100:.2f}%)")
    print(f"Best Max Drawdown: {max_drawdown_best:.4f} ({max_drawdown_best*100:.2f}%)")
    print()
    
    # Percentile analysis
    print("=== PERCENTILE ANALYSIS ===")
    percentiles = [1, 5, 10, 25, 50, 75, 90, 95, 99]
    
    print("Final Balance Percentiles:")
    for p in percentiles:
        value = np.percentile(df['final_balance'], p)
        print(f"  {p:2d}th percentile: ${value:.2f}")
    print()
    
    print("Total Return Percentiles:")
    for p in percentiles:
        value = np.percentile(df['total_return'], p)
        print(f"  {p:2d}th percentile: {value:.4f} ({value*100:.2f}%)")
    print()
    
    # Return results dictionary
    results = {
        'total_simulations': total_runs,
        'baseline_survival_probability': baseline_survival,
        'increased_risk_survival_probability': increased_risk_survival,
        'decreased_risk_survival_probability': decreased_risk_survival,
        'ruin_rates': {
            'baseline': 1 - baseline_survival,
            'increased_risk': 1 - increased_risk_survival,
            'decreased_risk': 1 - decreased_risk_survival
        },
        'survival_thresholds': {
            'final_balance_positive': survival_final_balance,
            'min_balance_positive': survival_min_balance,
            'return_above_neg50': survival_50_percent,
            'return_above_neg75': survival_75_percent,
            'return_above_neg90': survival_90_percent
        },
        'ruin_counts': {
            'final_balance_zero': ruin_zero_balance,
            'min_balance_zero': ruin_zero_min_balance,
            'return_neg50': ruin_50_percent,
            'return_neg75': ruin_75_percent,
            'return_neg90': ruin_90_percent
        },
        'risk_metrics': {
            'avg_max_drawdown': max_drawdown_mean,
            'worst_max_drawdown': max_drawdown_worst,
            'best_max_drawdown': max_drawdown_best
        }
    }
    
    return results

def create_visualization(results, output_file='survival_analysis_chart.png'):
    """Create visualization of survival probabilities across scenarios"""
    
    scenarios = ['Baseline', '+25% Risk', '-25% Risk']
    survival_probs = [
        results['baseline_survival_probability'],
        results['increased_risk_survival_probability'],
        results['decreased_risk_survival_probability']
    ]
    ruin_probs = [1 - prob for prob in survival_probs]
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
    
    # Survival Probabilities
    bars1 = ax1.bar(scenarios, [p*100 for p in survival_probs], 
                    color=['blue', 'red', 'green'], alpha=0.7)
    ax1.set_title('Account Survival Probability by Scenario\n(–50% Ruin Threshold)', fontsize=14, fontweight='bold')
    ax1.set_ylabel('Survival Probability (%)', fontsize=12)
    ax1.set_ylim(0, 100)
    ax1.grid(True, alpha=0.3)
    
    # Add value labels on bars
    for bar, prob in zip(bars1, survival_probs):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{prob*100:.1f}%', ha='center', va='bottom', fontweight='bold')
    
    # Ruin Probabilities
    bars2 = ax2.bar(scenarios, [p*100 for p in ruin_probs], 
                    color=['blue', 'red', 'green'], alpha=0.7)
    ax2.set_title('Account Ruin Probability by Scenario\n(–50% Ruin Threshold)', fontsize=14, fontweight='bold')
    ax2.set_ylabel('Ruin Probability (%)', fontsize=12)
    ax2.set_ylim(0, 100)
    ax2.grid(True, alpha=0.3)
    
    # Add value labels on bars
    for bar, prob in zip(bars2, ruin_probs):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{prob*100:.1f}%', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"Visualization saved to: {output_file}")

if __name__ == "__main__":
    # Analyze the Monte Carlo results
    monte_carlo_file = "C:/Users/<USER>/META/strategy_logs/monte_carlo_20250628_162353.csv"
    
    try:
        results = analyze_monte_carlo_results(monte_carlo_file)
        
        # Create visualization
        create_visualization(results)
        
        # Save detailed results to file
        output_file = f"survival_analysis_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        with open(output_file, 'w') as f:
            f.write("ACCOUNT SURVIVAL & BLOW-UP PROBABILITY ANALYSIS\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Total Simulations: {results['total_simulations']:,}\n\n")
            
            f.write("SCENARIO ANALYSIS (–50% Ruin Threshold):\n")
            f.write(f"  Baseline Survival Probability: {results['baseline_survival_probability']:.4f} ({results['baseline_survival_probability']*100:.2f}%)\n")
            f.write(f"  +25% Risk Survival Probability: {results['increased_risk_survival_probability']:.4f} ({results['increased_risk_survival_probability']*100:.2f}%)\n")
            f.write(f"  -25% Risk Survival Probability: {results['decreased_risk_survival_probability']:.4f} ({results['decreased_risk_survival_probability']*100:.2f}%)\n\n")
            
            f.write("RUIN RATES:\n")
            f.write(f"  Baseline Ruin Rate: {results['ruin_rates']['baseline']:.4f} ({results['ruin_rates']['baseline']*100:.2f}%)\n")
            f.write(f"  +25% Risk Ruin Rate: {results['ruin_rates']['increased_risk']:.4f} ({results['ruin_rates']['increased_risk']*100:.2f}%)\n")
            f.write(f"  -25% Risk Ruin Rate: {results['ruin_rates']['decreased_risk']:.4f} ({results['ruin_rates']['decreased_risk']*100:.2f}%)\n\n")
            
            f.write("SURVIVAL THRESHOLDS:\n")
            for threshold, prob in results['survival_thresholds'].items():
                f.write(f"  {threshold}: {prob:.4f} ({prob*100:.2f}%)\n")
        
        print(f"\nDetailed results saved to: {output_file}")
        
    except FileNotFoundError:
        print(f"Error: Monte Carlo file not found at {monte_carlo_file}")
    except Exception as e:
        print(f"Error analyzing Monte Carlo results: {e}")
