import pandas as pd
import numpy as np
import xgboost as xgb

from sklearn.metrics import classification_report, confusion_matrix
import os

def train_xgboost_models(file_path, spike_threshold_pct=0.01, min_range_threshold=10.0):
    """
    Trains XGBoost models for spike identification and tradable range prediction.

    Args:
        file_path (str): Path to the tick data CSV file.
        spike_threshold_pct (float): Percentage change in bid price to consider a spike.
        min_range_threshold (float): Minimum range for a minute to be considered tradable.
    """
    print(f"Loading data from {file_path}...")
    try:
        df = pd.read_csv(file_path)
        print("Data loaded successfully.")
    except FileNotFoundError:
        print(f"Error: File not found at {file_path}")
        return
    except Exception as e:
        print(f"Error loading data: {e}")
        return

    # Ensure 'time' column is datetime and sort
    df['time'] = pd.to_datetime(df['time'])
    df = df.sort_values('time').reset_index(drop=True)

    # --- Feature Engineering ---

    # 1. Minute-level OHLCV and Spread
    df['minute'] = df['time'].dt.floor('1min')
    minute_data = df.groupby('minute').agg(
        open=('bid', 'first'),
        high=('bid', 'max'),
        low=('bid', 'min'),
        close=('bid', 'last'),
        volume=('bid', 'size'), # Number of ticks in the minute
        ask_open=('ask', 'first'),
        ask_high=('ask', 'max'),
        ask_low=('ask', 'min'),
        ask_close=('ask', 'last'),
    )

    minute_data['range'] = minute_data['high'] - minute_data['low']
    minute_data['spread'] = minute_data['ask_close'] - minute_data['close']

    # 2. Spike Identification (Tick-level for target, then aggregate to minute)
    df['bid_change_pct'] = df['bid'].pct_change() * 100
    df['is_spike_tick'] = df['bid_change_pct'].abs() > spike_threshold_pct

    print(f"\n--- Spike Debug Info ---")
    print(f"Min bid_change_pct: {df['bid_change_pct'].min():.4f}")
    print(f"Max bid_change_pct: {df['bid_change_pct'].max():.4f}")
    print("bid_change_pct quantiles:")
    print(df['bid_change_pct'].abs().quantile([0.9, 0.95, 0.99, 0.999, 0.9999]))

    for threshold in [0.001, 0.005, 0.01, 0.05, 0.1]:
        num_spikes = (df['bid_change_pct'].abs() > threshold).sum()
        print(f"Number of ticks with abs bid_change_pct > {threshold}%: {num_spikes}")

    df['is_spike_tick'] = df['bid_change_pct'].abs() > spike_threshold_pct

    print(f"Number of ticks identified as spikes (abs > {spike_threshold_pct}%): {df['is_spike_tick'].sum()}")

    # Target: is_spike_current_minute
    # A minute has a spike if any tick within it has a spike
    spike_in_minute = df.groupby('minute')['is_spike_tick'].any().astype(int)
    print(f"Number of minutes with a spike (before fillna): {spike_in_minute.sum()}")
    minute_data['is_spike_current_minute'] = spike_in_minute # Assign directly, then fillna later

    print(f"Number of minutes with a spike in the current minute (target): {minute_data['is_spike_current_minute'].sum()}")
    print(f"--- End Spike Debug Info ---")

    # 3. Tradable Range Target
    minute_data['is_tradable_range'] = (minute_data['range'] > min_range_threshold).astype(int)

    # 4. Lagged Features (using bid close for simplicity)
    for lag in range(1, 6): # Lag features for past 5 minutes
        minute_data[f'close_lag_{lag}'] = minute_data['close'].shift(lag)
        minute_data[f'range_lag_{lag}'] = minute_data['range'].shift(lag)
        minute_data[f'volume_lag_{lag}'] = minute_data['volume'].shift(lag)
        minute_data[f'spread_lag_{lag}'] = minute_data['spread'].shift(lag)

    # 5. Rolling Features
    minute_data['bid_std_5min'] = minute_data['close'].rolling(window=5).std()
    minute_data['range_mean_5min'] = minute_data['range'].rolling(window=5).mean()

    # 6. Time-based Features
    minute_data['hour'] = minute_data.index.hour
    minute_data['day_of_week'] = minute_data.index.dayofweek

    # Fill NaN values created by shifting/rolling (initial rows) with 0
    # This ensures we don't drop all data if the dataset is small or has many NaNs
    minute_data = minute_data.fillna(0).reset_index()
    print(f"Shape of minute_data after NaN handling: {minute_data.shape}")

    # Define features (X) and targets (y)
    features = [
        'open', 'high', 'low', 'close', 'volume', 'range', 'spread',
        'ask_open', 'ask_high', 'ask_low', 'ask_close',
        'hour', 'day_of_week',
    ]
    for lag in range(1, 6):
        features.extend([f'close_lag_{lag}', f'range_lag_{lag}', f'volume_lag_{lag}', f'spread_lag_{lag}'])
    features.extend(['bid_std_5min', 'range_mean_5min'])

    X = minute_data[features]
    y_spike = minute_data['is_spike_current_minute']
    y_range = minute_data['is_tradable_range']

    print(f"\nTotal minute-level samples for training: {len(X)}")
    print(f"Spike occurrences in current minute: {y_spike.sum()} ({y_spike.mean()*100:.2f}%)")
    print(f"Tradable range occurrences: {y_range.sum()} ({y_range.mean()*100:.2f}%)")

    # --- Model Training ---

    # Perform chronological train-test split
    split_index = int(len(X) * 0.8) # 80% for training, 20% for testing

    X_train_spike, X_test_spike = X.iloc[:split_index], X.iloc[split_index:]
    y_train_spike, y_test_spike = y_spike.iloc[:split_index], y_spike.iloc[split_index:]

    X_train_range, X_test_range = X.iloc[:split_index], X.iloc[split_index:]
    y_train_range, y_test_range = y_range.iloc[:split_index], y_range.iloc[split_index:]

    print(f"\nChronological split: Training samples = {len(X_train_spike)}, Testing samples = {len(X_test_spike)}")

    # Model 1: Spike Identification
    print("\n--- Training Spike Identification Model ---")
    spike_model = xgb.XGBClassifier(objective='binary:logistic', eval_metric='logloss', random_state=42)
    spike_model.fit(X_train_spike, y_train_spike)

    y_pred_spike = spike_model.predict(X_test_spike)
    print("Spike Model Classification Report:")
    print(classification_report(y_test_spike, y_pred_spike))
    print("Spike Model Confusion Matrix:")
    print(confusion_matrix(y_test_spike, y_pred_spike))

    # Model 2: Tradable Range Prediction
    print("\n--- Training Tradable Range Prediction Model ---")
    print(f"\nNote: The 'is_tradable_range' target is highly imbalanced (Tradable: {y_range.sum()}, Non-Tradable: {len(y_range) - y_range.sum()}).")

    range_model = xgb.XGBClassifier(objective='binary:logistic', eval_metric='logloss', random_state=42)
    range_model.fit(X_train_range, y_train_range)

    y_pred_range = range_model.predict(X_test_range)
    print("Tradable Range Model Classification Report:")
    print(classification_report(y_test_range, y_pred_range))
    print("Tradable Range Model Confusion Matrix:")
    print(confusion_matrix(y_test_range, y_pred_range))

    # --- Model Saving ---
    spike_model_filename = "xgboost_spike_model.ubj"
    range_model_filename = "xgboost_range_model.ubj"

    spike_model.save_model(spike_model_filename)
    range_model.save_model(range_model_filename)

    print(f"\nModels saved: {spike_model_filename} and {range_model_filename}")

if __name__ == "__main__":
    data_file = "Range_Break_100_7days_20250623_20250630.csv"
    train_xgboost_models(data_file)