[2025-06-24 18:12:12] === XGBOOST TRADING STRATEGY (DIRECT) ===
[2025-06-24 18:12:12] Loading model from xgboost_model_20250521_145606.pkl...
[2025-06-24 18:12:12] Loading Renko data...
[2025-06-24 18:12:12] Renko data shape: (224649, 7)
[2025-06-24 18:12:12] Creating features from Renko data...
[2025-06-24 18:12:13] Features created. Shape: (224630, 21)
[2025-06-24 18:12:13] Feature columns: ['price_change', 'high_low_ratio', 'sma_5', 'price_vs_sma_5', 'sma_10', 'price_vs_sma_10', 'sma_20', 'price_vs_sma_20', 'up_ratio_3', 'up_ratio_5', 'up_ratio_10', 'volatility_5', 'volatility_10']
[2025-06-24 18:12:13] Features shape: (224630, 13), Target shape: (224630,)
[2025-06-24 18:12:13] Making predictions...
[2025-06-24 18:12:13] Model prediction failed: feature_names mismatch: ['price', 'price_change', 'price_pct_change', 'direction', 'up_streak', 'down_streak', 'price_momentum_5', 'price_pct_change_5', 'up_count_5', 'down_count_5', 'volatility_5', 'max_up_streak_5', 'max_down_streak_5', 'price_momentum_10', 'price_pct_change_10', 'up_count_10', 'down_count_10', 'volatility_10', 'max_up_streak_10', 'max_down_streak_10', 'price_momentum_15', 'price_pct_change_15', 'up_count_15', 'down_count_15', 'volatility_15', 'max_up_streak_15', 'max_down_streak_15', 'price_momentum_20', 'price_pct_change_20', 'up_count_20', 'down_count_20', 'volatility_20', 'max_up_streak_20', 'max_down_streak_20', 'price_momentum_30', 'price_pct_change_30', 'up_count_30', 'down_count_30', 'volatility_30', 'max_up_streak_30', 'max_down_streak_30', 'hour', 'minute', 'day_of_week'] ['price_change', 'high_low_ratio', 'sma_5', 'price_vs_sma_5', 'sma_10', 'price_vs_sma_10', 'sma_20', 'price_vs_sma_20', 'up_ratio_3', 'up_ratio_5', 'up_ratio_10', 'volatility_5', 'volatility_10']
expected down_count_10, price_pct_change_15, price_pct_change, max_down_streak_5, volatility_15, down_count_20, max_down_streak_15, up_count_15, max_up_streak_30, direction, price_pct_change_10, price_momentum_15, up_count_10, volatility_30, price_momentum_30, price_pct_change_20, max_down_streak_20, down_count_30, max_up_streak_20, price_pct_change_5, max_up_streak_5, price_momentum_20, max_up_streak_15, day_of_week, price_momentum_5, up_count_20, max_down_streak_30, price_pct_change_30, up_count_5, down_streak, down_count_5, volatility_20, up_count_30, max_up_streak_10, hour, price, down_count_15, max_down_streak_10, price_momentum_10, minute, up_streak in input data
training data did not have the following fields: price_vs_sma_20, up_ratio_3, sma_10, sma_5, high_low_ratio, price_vs_sma_10, up_ratio_10, up_ratio_5, price_vs_sma_5, sma_20
[2025-06-24 18:12:13] Creating dummy predictions for strategy testing...
[2025-06-24 18:12:13] Merging predictions with Renko data...
