import pandas as pd
import numpy as np
import os
from sklearn.model_selection import train_test_split, TimeSeriesSplit
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
import xgboost as xgb
import pickle
from datetime import datetime

# Load OHLC data
DATA_PATH = "stpRNG_1min.csv"
if not os.path.isfile(DATA_PATH):
    raise FileNotFoundError(f"File not found: {DATA_PATH}")

df = pd.read_csv(DATA_PATH)
df = df.dropna().reset_index(drop=True)

print(f"Loaded {len(df)} rows of data")

# Parameters - matching Pine Script defaults
p = 200  # Trend period
atr_p = 3  # ATR Period  
mult = 1.6  # ATR Multiplier
mode = "Type A"  # Signal mode
use_ema_smoother = "No"  # Smooth source with EMA
src_ema_period = 3  # EMA Smoother period

# Source selection
if use_ema_smoother == "Yes":
    df["src"] = df["close"].ewm(span=src_ema_period, adjust=False).mean()
else:
    df["src"] = df["close"]

# Calculate ATR manually
def calculate_atr(df, period):
    df["tr1"] = df["high"] - df["low"]
    df["tr2"] = abs(df["high"] - df["close"].shift(1))
    df["tr3"] = abs(df["low"] - df["close"].shift(1))
    df["tr"] = df[["tr1", "tr2", "tr3"]].max(axis=1)
    df["atr"] = df["tr"].rolling(window=period).mean()
    return df["atr"]

# Q-Trend Calculations
df["h"] = df["src"].rolling(window=p).max()  # Highest of src p-bars back
df["l"] = df["src"].rolling(window=p).min()  # Lowest of src p-bars back
df["d"] = df["h"] - df["l"]
df["m"] = (df["h"] + df["l"]) / 2  # Initial trend line

df["atr"] = calculate_atr(df, atr_p).shift(1)  # ATR shifted by 1
df["epsilon"] = mult * df["atr"]

# Initialize columns
df["trend"] = np.nan
df["last_signal"] = ""
df["signal"] = ""
df["change_up"] = False
df["change_down"] = False

# Process each bar for Q-Trend signals
for i in range(len(df)):
    if i < p:
        df.at[i, "trend"] = df.at[i, "m"] if not pd.isna(df.at[i, "m"]) else np.nan
        df.at[i, "last_signal"] = ""
        continue
    
    src = df.at[i, "src"]
    prev_trend = df.at[i-1, "trend"] if i > 0 and not pd.isna(df.at[i-1, "trend"]) else df.at[i, "m"]
    epsilon = df.at[i, "epsilon"]
    
    if pd.isna(epsilon):
        df.at[i, "trend"] = prev_trend
        df.at[i, "last_signal"] = df.at[i-1, "last_signal"] if i > 0 else ""
        continue
    
    # Type A mode logic (crossover/crossunder)
    if mode == "Type A":
        change_up = src > prev_trend + epsilon
        change_down = src < prev_trend - epsilon
    else:  # Type B mode (cross)
        prev_src = df.at[i-1, "src"] if i > 0 else src
        change_up = (prev_src <= prev_trend + epsilon and src > prev_trend + epsilon) or src > prev_trend + epsilon
        change_down = (prev_src >= prev_trend - epsilon and src < prev_trend - epsilon) or src < prev_trend - epsilon
    
    df.at[i, "change_up"] = change_up
    df.at[i, "change_down"] = change_down
    
    # Strong signals logic
    h = df.at[i, "h"]
    l = df.at[i, "l"]
    d = df.at[i, "d"]
    
    # Check current and previous 4 bars for strong signals
    sb = False
    ss = False
    for j in range(max(0, i-4), i+1):
        if j < len(df):
            open_price = df.at[j, "open"]
            if open_price < l + d / 8 and open_price >= l:
                sb = True
            if open_price > h - d / 8 and open_price <= h:
                ss = True
    
    strong_buy = sb
    strong_sell = ss
    
    # Update trend line
    if change_up or change_down:
        if change_up:
            new_trend = prev_trend + epsilon
        elif change_down:
            new_trend = prev_trend - epsilon
        else:
            new_trend = prev_trend
    else:
        new_trend = prev_trend
    
    df.at[i, "trend"] = new_trend
    
    # Signal logic
    prev_last_signal = df.at[i-1, "last_signal"] if i > 0 else ""
    
    if change_up and prev_last_signal != "B":
        if strong_buy:
            signal = "strong_buy"
        else:
            signal = "buy"
        df.at[i, "last_signal"] = "B"
    elif change_down and prev_last_signal != "S":
        if strong_sell:
            signal = "strong_sell"
        else:
            signal = "sell"
        df.at[i, "last_signal"] = "S"
    else:
        signal = ""
        df.at[i, "last_signal"] = prev_last_signal
    
    df.at[i, "signal"] = signal

# Create additional technical features
def add_technical_features(df):
    # Price-based features
    df['price_vs_trend'] = (df['close'] - df['trend']) / df['trend']
    df['price_vs_high'] = (df['close'] - df['h']) / df['h']
    df['price_vs_low'] = (df['close'] - df['l']) / df['l']
    df['price_position'] = (df['close'] - df['l']) / (df['h'] - df['l'])  # Position in range
    
    # Volatility features
    df['atr_normalized'] = df['atr'] / df['close']
    df['epsilon_normalized'] = df['epsilon'] / df['close']
    df['range_normalized'] = (df['high'] - df['low']) / df['close']
    
    # Momentum features
    for period in [5, 10, 20]:
        df[f'return_{period}'] = df['close'].pct_change(period)
        df[f'volatility_{period}'] = df['close'].rolling(period).std() / df['close']
    
    # Moving averages
    for period in [10, 20, 50]:
        df[f'ma_{period}'] = df['close'].rolling(period).mean()
        df[f'price_vs_ma_{period}'] = (df['close'] - df[f'ma_{period}']) / df[f'ma_{period}']
    
    # RSI-like feature
    delta = df['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    
    # Volume features (if available)
    if 'volume' in df.columns:
        df['volume_ma'] = df['volume'].rolling(20).mean()
        df['volume_ratio'] = df['volume'] / df['volume_ma']
    
    return df

df = add_technical_features(df)

# Create labels for future returns
def create_labels(df, lookahead=10):
    """Create labels based on future price movements"""
    df['future_return'] = df['close'].shift(-lookahead) / df['close'] - 1
    
    # Binary classification: 1 for profitable trades, 0 for unprofitable
    threshold = 0.001  # 0.1% threshold
    df['label'] = (df['future_return'] > threshold).astype(int)
    
    # Multi-class classification
    df['label_multiclass'] = 0  # Neutral
    df.loc[df['future_return'] > threshold, 'label_multiclass'] = 1  # Bullish
    df.loc[df['future_return'] < -threshold, 'label_multiclass'] = -1  # Bearish
    
    return df

df = create_labels(df, lookahead=10)

# Select features for model
feature_columns = [
    # Q-Trend features
    'price_vs_trend', 'price_vs_high', 'price_vs_low', 'price_position',
    'atr_normalized', 'epsilon_normalized', 'range_normalized',
    
    # Momentum features
    'return_5', 'return_10', 'return_20',
    'volatility_5', 'volatility_10', 'volatility_20',
    
    # Moving average features
    'price_vs_ma_10', 'price_vs_ma_20', 'price_vs_ma_50',
    
    # Technical indicators
    'rsi',
    
    # Q-Trend signals (encoded)
    'change_up', 'change_down'
]

# Add volume features if available
if 'volume' in df.columns:
    feature_columns.extend(['volume_ratio'])

# Prepare data for ML
# Remove rows with missing data
valid_idx = df.dropna(subset=feature_columns + ['label']).index
df_clean = df.loc[valid_idx].copy()

print(f"Clean data: {len(df_clean)} rows after removing NaN values")

# Features and labels
X = df_clean[feature_columns]
y_binary = df_clean['label']
y_multiclass = df_clean['label_multiclass']

# Check class distribution
print(f"\nBinary label distribution:")
print(y_binary.value_counts(normalize=True))
print(f"\nMulticlass label distribution:")
print(y_multiclass.value_counts(normalize=True))

# Time series split (important for financial data)
tscv = TimeSeriesSplit(n_splits=5, test_size=int(len(X) * 0.2))

# Train XGBoost model for binary classification
print("\n=== Training Binary Classification Model ===")

# Use time series split for more realistic evaluation
train_idx, test_idx = list(tscv.split(X))[-1]  # Use last split
X_train, X_test = X.iloc[train_idx], X.iloc[test_idx]
y_train, y_test = y_binary.iloc[train_idx], y_binary.iloc[test_idx]

# Train model
xgb_model = xgb.XGBClassifier(
    n_estimators=100,
    max_depth=6,
    learning_rate=0.1,
    subsample=0.8,
    colsample_bytree=0.8,
    random_state=42,
    eval_metric='logloss'
)

xgb_model.fit(X_train, y_train)

# Predictions
y_pred = xgb_model.predict(X_test)
y_pred_proba = xgb_model.predict_proba(X_test)[:, 1]

# Evaluation
accuracy = accuracy_score(y_test, y_pred)
print(f"Accuracy: {accuracy:.4f}")
print(f"\nClassification Report:")
print(classification_report(y_test, y_pred))
print(f"\nConfusion Matrix:")
print(confusion_matrix(y_test, y_pred))

# Feature importance
feature_importance = pd.DataFrame({
    'feature': feature_columns,
    'importance': xgb_model.feature_importances_
}).sort_values('importance', ascending=False)

print(f"\nTop 10 Feature Importances:")
print(feature_importance.head(10))

# Save model
model_filename = f'xgboost_qtrend_model_{datetime.now().strftime("%Y%m%d_%H%M%S")}.pkl'
with open(model_filename, 'wb') as f:
    pickle.dump(xgb_model, f)

print(f"\nModel saved as: {model_filename}")

# Test model predictions on recent data
print(f"\n=== Model Predictions on Test Set ===")
test_predictions = pd.DataFrame({
    'actual': y_test.values,
    'predicted': y_pred,
    'probability': y_pred_proba
})

# Show some examples
print("Sample predictions:")
print(test_predictions.head(20))

# Calculate potential trading performance using model predictions
print(f"\n=== Simulated Trading with Model Predictions ===")

# Use model predictions to make trading decisions
threshold = 0.6  # Probability threshold for entering trades
test_df = df_clean.iloc[test_idx].copy()
test_df['ml_prediction'] = y_pred_proba

# Simple trading strategy using ML predictions
balance = 100_000
risk_per_trade = 0.002
position = 0
trades = []

for i in range(1, len(test_df)):
    current_row = test_df.iloc[i]
    prev_row = test_df.iloc[i-1]
    
    ml_signal = current_row['ml_prediction'] > threshold
    price = current_row['open']
    
    # Exit position if signal changes
    if position != 0:
        # Simple exit after fixed period or opposite signal
        if position == 1 and not ml_signal:  # Exit long
            entry_price = trades[-1]['entry_price']
            pnl = (price - entry_price) / entry_price
            trades[-1]['exit_price'] = price
            trades[-1]['pnl'] = pnl
            trades[-1]['result'] = 'win' if pnl > 0 else 'loss'
            
            if pnl > 0:
                balance *= (1 + risk_per_trade)
            else:
                balance *= (1 - risk_per_trade)
            position = 0
    
    # Enter new position
    if position == 0 and ml_signal:
        position = 1
        trades.append({
            'entry_price': price,
            'type': 'long'
        })

# Calculate ML strategy performance
if trades:
    completed_trades = [t for t in trades if 'result' in t]
    if completed_trades:
        ml_wins = len([t for t in completed_trades if t['result'] == 'win'])
        ml_total = len(completed_trades)
        ml_win_rate = ml_wins / ml_total * 100
        ml_return = (balance - 100_000) / 100_000 * 100
        
        print(f"ML Strategy Results:")
        print(f"Trades: {ml_total}, Win Rate: {ml_win_rate:.2f}%, Return: {ml_return:+.2f}%")
    else:
        print("No completed trades in test period")
else:
    print("No trades generated by ML strategy")

print(f"\nFeature importance saved to analysis")
feature_importance.to_csv('feature_importance.csv', index=False)
