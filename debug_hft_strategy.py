# Debug HFT Impulse Bounce Strategy

import pandas as pd
import numpy as np
import os

# Load data
DATA_PATH = "stpRNG_1min.csv"
df = pd.read_csv(DATA_PATH)
df.dropna(inplace=True)

print(f"Loaded {len(df)} rows of data")

# Parameters (more relaxed for debugging)
BB_PERIOD = 20
BB_STD = 2.0
RSI_PERIOD = 6
OB_THRESHOLD = 0.6  # Lowered from 0.7
EXEC_DELAY = 1

# Create orderbook delta proxy
df['price_change'] = df['close'].diff()
df['volume_ma'] = df['volume'].rolling(20).mean()
df['volume_ratio'] = df['volume'] / df['volume_ma']

# Simple orderbook delta approximation
df['raw_ob_delta'] = np.where(df['price_change'] > 0, 
                              0.5 + (df['volume_ratio'] - 1) * 0.3,  # Bullish
                              0.5 - (df['volume_ratio'] - 1) * 0.3)  # Bearish

# Normalize and smooth
df['orderbook_delta'] = df['raw_ob_delta'].rolling(3).mean().clip(0, 1)
df['orderbook_delta'] = df['orderbook_delta'].fillna(0.5)

# Calculate indicators
df['typical'] = (df['high'] + df['low'] + df['close']) / 3
rolling_mean = df['typical'].rolling(BB_PERIOD).mean()
rolling_std = df['typical'].rolling(BB_PERIOD).std()
df['bb_mid'] = rolling_mean
df['bb_lower'] = rolling_mean - BB_STD * rolling_std
df['bb_upper'] = rolling_mean + BB_STD * rolling_std

# RSI
delta = df['close'].diff()
gain = np.where(delta > 0, delta, 0)
loss = np.where(delta < 0, -delta, 0)
avg_gain = pd.Series(gain).rolling(window=RSI_PERIOD).mean()
avg_loss = pd.Series(loss).rolling(window=RSI_PERIOD).mean()
rs = avg_gain / (avg_loss + 1e-10)
df['rsi'] = 100 - (100 / (1 + rs))

print(f"\n=== INDICATOR DIAGNOSTICS ===")
print(f"RSI range: {df['rsi'].min():.1f} to {df['rsi'].max():.1f}")
print(f"Orderbook delta range: {df['orderbook_delta'].min():.3f} to {df['orderbook_delta'].max():.3f}")
print(f"Volume ratio range: {df['volume_ratio'].min():.2f} to {df['volume_ratio'].max():.2f}")

# Check condition frequencies
valid_start = BB_PERIOD + EXEC_DELAY
valid_data = df.iloc[valid_start:].copy()

print(f"\n=== CONDITION ANALYSIS ===")
print(f"Total valid bars: {len(valid_data)}")

# Long conditions
below_bb_lower = (valid_data['close'] < valid_data['bb_lower']).sum()
high_ob_delta = (valid_data['orderbook_delta'] > OB_THRESHOLD).sum()
low_rsi = (valid_data['rsi'] < 40).sum()
long_conditions = ((valid_data['close'] < valid_data['bb_lower']) & 
                   (valid_data['orderbook_delta'] > OB_THRESHOLD) & 
                   (valid_data['rsi'] < 40)).sum()

print(f"Price below BB lower: {below_bb_lower} ({below_bb_lower/len(valid_data)*100:.1f}%)")
print(f"OB delta > {OB_THRESHOLD}: {high_ob_delta} ({high_ob_delta/len(valid_data)*100:.1f}%)")
print(f"RSI < 40: {low_rsi} ({low_rsi/len(valid_data)*100:.1f}%)")
print(f"All long conditions: {long_conditions} ({long_conditions/len(valid_data)*100:.1f}%)")

# Short conditions
above_bb_upper = (valid_data['close'] > valid_data['bb_upper']).sum()
low_ob_delta = (valid_data['orderbook_delta'] < (1 - OB_THRESHOLD)).sum()
high_rsi = (valid_data['rsi'] > 60).sum()
short_conditions = ((valid_data['close'] > valid_data['bb_upper']) & 
                    (valid_data['orderbook_delta'] < (1 - OB_THRESHOLD)) & 
                    (valid_data['rsi'] > 60)).sum()

print(f"\nPrice above BB upper: {above_bb_upper} ({above_bb_upper/len(valid_data)*100:.1f}%)")
print(f"OB delta < {1-OB_THRESHOLD}: {low_ob_delta} ({low_ob_delta/len(valid_data)*100:.1f}%)")
print(f"RSI > 60: {high_rsi} ({high_rsi/len(valid_data)*100:.1f}%)")
print(f"All short conditions: {short_conditions} ({short_conditions/len(valid_data)*100:.1f}%)")

# Show sample data where conditions are close
print(f"\n=== SAMPLE DATA ANALYSIS ===")
sample_data = valid_data[['close', 'bb_lower', 'bb_upper', 'bb_mid', 'rsi', 'orderbook_delta']].tail(20)
print(sample_data)

# Test with relaxed parameters
print(f"\n=== TESTING RELAXED PARAMETERS ===")

relaxed_params = [
    {"name": "Lower OB threshold", "ob_thresh": 0.55, "rsi_low": 40, "rsi_high": 60},
    {"name": "Relaxed RSI", "ob_thresh": 0.6, "rsi_low": 45, "rsi_high": 55},
    {"name": "Even more relaxed", "ob_thresh": 0.55, "rsi_low": 45, "rsi_high": 55},
    {"name": "Very relaxed", "ob_thresh": 0.52, "rsi_low": 50, "rsi_high": 50},
]

for param_set in relaxed_params:
    ob_thresh = param_set["ob_thresh"]
    rsi_low = param_set["rsi_low"]
    rsi_high = param_set["rsi_high"]
    
    long_cond = ((valid_data['close'] < valid_data['bb_lower']) & 
                 (valid_data['orderbook_delta'] > ob_thresh) & 
                 (valid_data['rsi'] < rsi_low)).sum()
    
    short_cond = ((valid_data['close'] > valid_data['bb_upper']) & 
                  (valid_data['orderbook_delta'] < (1 - ob_thresh)) & 
                  (valid_data['rsi'] > rsi_high)).sum()
    
    total_signals = long_cond + short_cond
    print(f"{param_set['name']:20s}: {total_signals:3d} signals ({long_cond} long, {short_cond} short)")

# Test simple BB bounce strategy without OB filter
print(f"\n=== SIMPLE BB BOUNCE TEST ===")
simple_long = ((valid_data['close'] < valid_data['bb_lower']) & (valid_data['rsi'] < 40)).sum()
simple_short = ((valid_data['close'] > valid_data['bb_upper']) & (valid_data['rsi'] > 60)).sum()
print(f"Simple BB + RSI signals: {simple_long + simple_short} ({simple_long} long, {simple_short} short)")

# Run backtest with relaxed parameters
print(f"\n=== RUNNING RELAXED BACKTEST ===")

# Use most relaxed parameters
OB_THRESHOLD_RELAXED = 0.52
RSI_LOW = 50
RSI_HIGH = 50

balance = 100_000
risk_per_trade = 0.004
position = 0
entry_price = 0
wins = 0
losses = 0
trades = []

for i in range(valid_start, len(df)):
    prior = i - EXEC_DELAY
    
    price = df.at[i, 'open']
    rsi = df.at[prior, 'rsi']
    ob_delta = df.at[prior, 'orderbook_delta']
    close = df.at[prior, 'close']
    bb_l = df.at[prior, 'bb_lower']
    bb_u = df.at[prior, 'bb_upper']
    bb_mid = df.at[prior, 'bb_mid']
    
    if pd.isna(rsi) or pd.isna(ob_delta) or pd.isna(bb_l) or pd.isna(bb_u):
        continue
    
    if position == 0:
        # Long entry - relaxed conditions
        if close < bb_l and ob_delta > OB_THRESHOLD_RELAXED and rsi < RSI_LOW:
            position = 1
            entry_price = price
            
        # Short entry - relaxed conditions
        elif close > bb_u and ob_delta < (1 - OB_THRESHOLD_RELAXED) and rsi > RSI_HIGH:
            position = -1
            entry_price = price
    
    else:
        # Exit at BB middle or stop loss
        target = bb_mid
        exit_condition = False
        
        if position == 1 and (price >= target or price < entry_price * 0.998):
            exit_condition = True
        elif position == -1 and (price <= target or price > entry_price * 1.002):
            exit_condition = True
        
        if exit_condition:
            pnl_points = (price - entry_price) if position == 1 else (entry_price - price)
            pnl_pct = pnl_points / entry_price
            
            trades.append({
                'entry_price': entry_price,
                'exit_price': price,
                'position': 'long' if position == 1 else 'short',
                'pnl_pct': pnl_pct * 100,
                'result': 'win' if pnl_points > 0 else 'loss'
            })
            
            balance *= (1 + risk_per_trade) if pnl_points > 0 else (1 - risk_per_trade)
            
            if pnl_points > 0:
                wins += 1
            else:
                losses += 1
            position = 0

total_trades = wins + losses
win_rate = wins / total_trades * 100 if total_trades > 0 else 0
total_return = (balance - 100_000) / 100_000 * 100

print(f"\nRelaxed HFT Strategy Results:")
print(f"Total trades: {total_trades}")
print(f"Win rate: {win_rate:.2f}%")
print(f"Total return: {total_return:+.2f}%")

if trades:
    trades_df = pd.DataFrame(trades)
    print(f"\nSample trades:")
    print(trades_df.head(10))
