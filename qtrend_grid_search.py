import pandas as pd
import numpy as np
from itertools import product

# Load data
df = pd.read_csv("C:/Users/<USER>/META/stpRNG_renko_0_1_extended.csv").dropna().reset_index(drop=True)

# Search ranges
trend_period_range = [50, 55, 60]
atr_period_range = [9, 10, 11]
atr_mult_range = [1.1, 1.2, 1.3]

results = []

print("Starting grid search optimization...")
print(f"Total combinations to test: {len(trend_period_range) * len(atr_period_range) * len(atr_mult_range)}")

# Begin search
for trend_period, atr_period, atr_mult in product(trend_period_range, atr_period_range, atr_mult_range):
    print(f"\nTesting: Period={trend_period}, ATR Period={atr_period}, ATR Mult={atr_mult}")
    
    df["src"] = df["close"]
    df["highest"] = df["src"].rolling(window=trend_period).max()
    df["lowest"] = df["src"].rolling(window=trend_period).min()
    df["d"] = df["highest"] - df["lowest"]
    df["mid"] = (df["highest"] + df["lowest"]) / 2
    
    tr = pd.Series(np.maximum.reduce([
        df["high"] - df["low"],
        abs(df["high"] - df["close"].shift(1)),
        abs(df["low"] - df["close"].shift(1))
    ]))
    df["atr"] = tr.rolling(window=atr_period).mean().shift(1)
    df["epsilon"] = atr_mult * df["atr"]

    trend = np.full(len(df), np.nan, dtype=np.float64)
    signal = [""] * len(df)

    for i in range(len(df)):
        if i < trend_period:
            continue

        src = df.at[i, "src"]
        mid = df.at[i-1, "mid"]
        eps = df.at[i, "epsilon"]
        change_up = src > mid + eps
        change_down = src < mid - eps

        open_ = df.at[i, "open"]
        h = df.at[i, "highest"]
        l = df.at[i, "lowest"]
        d = df.at[i, "d"]

        sb = l <= open_ < l + d / 8
        ss = h - d / 8 < open_ <= h
        strong_buy = sb or df.at[i-1, "open"] < df.at[i-1, "lowest"] + df.at[i-1, "d"] / 8
        strong_sell = ss or df.at[i-1, "open"] > df.at[i-1, "highest"] - df.at[i-1, "d"] / 8

        if np.isnan(trend[i-1]) if i > 0 else True:
            trend[i] = df.at[i, "mid"]
        elif change_up:
            trend[i] = trend[i-1] + eps
            signal[i] = "strong_buy" if strong_buy else "buy"
        elif change_down:
            trend[i] = trend[i-1] - eps
            signal[i] = "strong_sell" if strong_sell else "sell"
        else:
            trend[i] = trend[i-1]
            signal[i] = signal[i-1] if i > 0 else ""

    # Backtest
    capital = 100_000
    balance = capital
    position = 0
    entry_price = 0
    wins = 0
    losses = 0
    risk_per_trade = 0.004

    for i in range(1, len(df)):
        sig = signal[i]
        price = df.at[i, "open"]

        if position > 0 and sig.startswith("sell"):
            if price > entry_price:
                wins += 1
                balance *= 1 + risk_per_trade
            else:
                losses += 1
                balance *= 1 - risk_per_trade
            position = 0

        elif position < 0 and sig.startswith("buy"):
            if price < entry_price:
                wins += 1
                balance *= 1 + risk_per_trade
            else:
                losses += 1
                balance *= 1 - risk_per_trade
            position = 0

        if sig == "buy" or sig == "strong_buy":
            position = 1
            entry_price = price
        elif sig == "sell" or sig == "strong_sell":
            position = -1
            entry_price = price

    total_trades = wins + losses
    win_rate = wins / total_trades * 100 if total_trades else 0
    results.append((trend_period, atr_period, atr_mult, win_rate, total_trades, balance))
    print(f"Win Rate: {win_rate:.2f}%, Total Trades: {total_trades}, Final Balance: {balance:.2f}")

# Sort by win rate and show top results
results.sort(key=lambda x: x[3], reverse=True)
print("\nTop 5 Parameter Combinations:")
print("Period | ATR Period | ATR Mult | Win Rate | Trades | Final Balance")
print("-" * 65)
for r in results[:5]:
    print(f"{r[0]:6d} | {r[1]:10d} | {r[2]:8.1f} | {r[3]:8.2f}% | {r[4]:6d} | {r[5]:12.2f}")
