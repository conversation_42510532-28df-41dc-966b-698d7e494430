"""
Enhanced Trade Data Collection Structure
=========================================

This module provides enhanced data collection structures for backtesting that store
per-trade results in a comprehensive Pandas DataFrame. This enables advanced analytics
like streak analysis, drawdown calculations, and Monte Carlo re-sampling without
re-running the backtest logic.

Key Features:
- Comprehensive per-trade result tracking
- Cumulative balance and equity curve data
- Streak and drawdown calculation capabilities
- Monte Carlo simulation ready data structure
- Performance metrics computation
"""

import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict

@dataclass
class TradeResult:
    """Data class for individual trade results"""
    trade_id: int
    entry_time: datetime
    exit_time: datetime
    direction: str  # 'LONG' or 'SHORT'
    entry_price: float
    exit_price: float
    volume: float
    gross_pnl: float
    commission: float
    net_pnl: float
    cumulative_balance: float
    trade_duration: int  # in ticks or time units
    max_favorable_excursion: float  # MFE
    max_adverse_excursion: float    # MAE
    strategy_signal: str
    confidence_score: float
    market_conditions: Dict
    
class EnhancedTradeTracker:
    """
    Enhanced trade tracking system that stores comprehensive per-trade data
    suitable for advanced analytics including streaks, drawdowns, and <PERSON> <PERSON> analysis.
    """
    
    def __init__(self, initial_balance: float = 10000.0):
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        self.trade_counter = 0
        
        # Comprehensive trade data storage
        self.trades_data = []
        self.equity_curve = [initial_balance]
        self.timestamp_curve = [datetime.now()]
        
        # Streak tracking
        self.current_win_streak = 0
        self.current_loss_streak = 0
        self.max_win_streak = 0
        self.max_loss_streak = 0
        
        # Drawdown tracking
        self.peak_balance = initial_balance
        self.current_drawdown = 0.0
        self.max_drawdown = 0.0
        self.drawdown_duration = 0
        self.max_drawdown_duration = 0
        self.last_peak_time = datetime.now()
        
        # Advanced metrics
        self.consecutive_wins = 0
        self.consecutive_losses = 0
        self.win_streaks = []
        self.loss_streaks = []
        
    def record_trade(self, 
                    entry_time: datetime,
                    exit_time: datetime,
                    direction: str,
                    entry_price: float,
                    exit_price: float,
                    volume: float,
                    commission: float = 0.0,
                    strategy_signal: str = "",
                    confidence_score: float = 0.0,
                    market_conditions: Dict = None,
                    mfe: float = 0.0,
                    mae: float = 0.0) -> None:
        """
        Record a completed trade with comprehensive data collection.
        
        Args:
            entry_time: Trade entry timestamp
            exit_time: Trade exit timestamp  
            direction: 'LONG' or 'SHORT'
            entry_price: Entry price
            exit_price: Exit price
            volume: Position size
            commission: Total commission paid
            strategy_signal: Signal that triggered the trade
            confidence_score: Confidence level of the signal (0-1)
            market_conditions: Dict of market state indicators
            mfe: Maximum Favorable Excursion during trade
            mae: Maximum Adverse Excursion during trade
        """
        
        self.trade_counter += 1
        
        # Calculate P&L
        if direction.upper() == 'LONG':
            gross_pnl = (exit_price - entry_price) * volume
        elif direction.upper() == 'SHORT':
            gross_pnl = (entry_price - exit_price) * volume
        else:
            raise ValueError(f"Invalid direction: {direction}. Must be 'LONG' or 'SHORT'")
            
        net_pnl = gross_pnl - commission
        
        # Update balance
        previous_balance = self.current_balance
        self.current_balance += net_pnl
        
        # Update equity curve
        self.equity_curve.append(self.current_balance)
        self.timestamp_curve.append(exit_time)
        
        # Update streaks
        self._update_streaks(net_pnl)
        
        # Update drawdown
        self._update_drawdown(exit_time)
        
        # Calculate trade duration
        trade_duration = int((exit_time - entry_time).total_seconds())
        
        # Create trade record
        trade_result = TradeResult(
            trade_id=self.trade_counter,
            entry_time=entry_time,
            exit_time=exit_time,
            direction=direction.upper(),
            entry_price=entry_price,
            exit_price=exit_price,
            volume=volume,
            gross_pnl=gross_pnl,
            commission=commission,
            net_pnl=net_pnl,
            cumulative_balance=self.current_balance,
            trade_duration=trade_duration,
            max_favorable_excursion=mfe,
            max_adverse_excursion=mae,
            strategy_signal=strategy_signal,
            confidence_score=confidence_score,
            market_conditions=market_conditions or {}
        )
        
        self.trades_data.append(trade_result)
        
    def _update_streaks(self, pnl: float) -> None:
        """Update win/loss streak tracking"""
        if pnl > 0:  # Winning trade
            self.consecutive_wins += 1
            if self.consecutive_losses > 0:
                self.loss_streaks.append(self.consecutive_losses)
                self.consecutive_losses = 0
            self.current_win_streak += 1
            self.current_loss_streak = 0
            self.max_win_streak = max(self.max_win_streak, self.current_win_streak)
        elif pnl < 0:  # Losing trade
            self.consecutive_losses += 1
            if self.consecutive_wins > 0:
                self.win_streaks.append(self.consecutive_wins)
                self.consecutive_wins = 0
            self.current_loss_streak += 1
            self.current_win_streak = 0
            self.max_loss_streak = max(self.max_loss_streak, self.current_loss_streak)
        # Break even trades don't affect streaks
        
    def _update_drawdown(self, timestamp: datetime) -> None:
        """Update drawdown calculations"""
        if self.current_balance > self.peak_balance:
            # New peak reached
            if self.current_drawdown > 0:
                self.max_drawdown_duration = max(self.max_drawdown_duration, self.drawdown_duration)
                self.drawdown_duration = 0
            self.peak_balance = self.current_balance
            self.current_drawdown = 0.0
            self.last_peak_time = timestamp
        else:
            # In drawdown
            self.current_drawdown = (self.peak_balance - self.current_balance) / self.peak_balance
            self.max_drawdown = max(self.max_drawdown, self.current_drawdown)
            self.drawdown_duration += 1
            
    def get_trades_dataframe(self) -> pd.DataFrame:
        """
        Convert trade data to a comprehensive Pandas DataFrame suitable for analysis.
        
        Returns:
            DataFrame with all trade data plus calculated metrics
        """
        if not self.trades_data:
            return pd.DataFrame()
            
        # Convert trade results to dictionary format
        trades_dict = []
        for trade in self.trades_data:
            trade_dict = {
                'trade_id': trade.trade_id,
                'entry_time': trade.entry_time,
                'exit_time': trade.exit_time,
                'direction': trade.direction,
                'entry_price': trade.entry_price,
                'exit_price': trade.exit_price,
                'volume': trade.volume,
                'gross_pnl': trade.gross_pnl,
                'commission': trade.commission,
                'net_pnl': trade.net_pnl,
                'cumulative_balance': trade.cumulative_balance,
                'trade_duration_seconds': trade.trade_duration,
                'max_favorable_excursion': trade.max_favorable_excursion,
                'max_adverse_excursion': trade.max_adverse_excursion,
                'strategy_signal': trade.strategy_signal,
                'confidence_score': trade.confidence_score
            }
            
            # Add market conditions as separate columns
            if trade.market_conditions:
                for key, value in trade.market_conditions.items():
                    trade_dict[f'market_{key}'] = value
                    
            trades_dict.append(trade_dict)
            
        df = pd.DataFrame(trades_dict)
        
        # Add calculated columns for analysis
        df['is_winner'] = df['net_pnl'] > 0
        df['is_loser'] = df['net_pnl'] < 0
        df['returns'] = df['net_pnl'] / df['cumulative_balance'].shift(1).fillna(self.initial_balance)
        df['cumulative_returns'] = (1 + df['returns']).cumprod() - 1
        
        # Add rolling statistics
        df['rolling_win_rate_10'] = df['is_winner'].rolling(10, min_periods=1).mean()
        df['rolling_avg_pnl_10'] = df['net_pnl'].rolling(10, min_periods=1).mean()
        
        # Add streak information
        df['win_streak'] = self._calculate_streak_series(df['is_winner'])
        df['loss_streak'] = self._calculate_streak_series(df['is_loser'])
        
        # Add drawdown series
        df['running_max_balance'] = df['cumulative_balance'].expanding().max()
        df['drawdown'] = (df['running_max_balance'] - df['cumulative_balance']) / df['running_max_balance']
        
        return df
    
    def _calculate_streak_series(self, boolean_series: pd.Series) -> pd.Series:
        """Calculate consecutive streak values for a boolean series"""
        streaks = []
        current_streak = 0
        
        for value in boolean_series:
            if value:
                current_streak += 1
            else:
                current_streak = 0
            streaks.append(current_streak)
            
        return pd.Series(streaks, index=boolean_series.index)
    
    def get_performance_metrics(self) -> Dict:
        """
        Calculate comprehensive performance metrics.
        
        Returns:
            Dictionary of performance statistics
        """
        if not self.trades_data:
            return {}
            
        df = self.get_trades_dataframe()
        
        total_trades = len(df)
        winning_trades = (df['net_pnl'] > 0).sum()
        losing_trades = (df['net_pnl'] < 0).sum()
        breakeven_trades = (df['net_pnl'] == 0).sum()
        
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        total_profit = df[df['net_pnl'] > 0]['net_pnl'].sum()
        total_loss = abs(df[df['net_pnl'] < 0]['net_pnl'].sum())
        
        profit_factor = total_profit / total_loss if total_loss > 0 else float('inf')
        
        avg_win = df[df['net_pnl'] > 0]['net_pnl'].mean() if winning_trades > 0 else 0
        avg_loss = df[df['net_pnl'] < 0]['net_pnl'].mean() if losing_trades > 0 else 0
        
        expectancy = (win_rate * avg_win) + ((1 - win_rate) * avg_loss)
        
        # Risk metrics
        returns = df['returns'].dropna()
        sharpe_ratio = returns.mean() / returns.std() if returns.std() > 0 else 0
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'breakeven_trades': breakeven_trades,
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'total_pnl': df['net_pnl'].sum(),
            'total_return': (self.current_balance - self.initial_balance) / self.initial_balance,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'expectancy': expectancy,
            'max_win_streak': self.max_win_streak,
            'max_loss_streak': self.max_loss_streak,
            'max_drawdown': self.max_drawdown,
            'max_drawdown_duration': self.max_drawdown_duration,
            'current_drawdown': self.current_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'final_balance': self.current_balance
        }
    
    def get_monte_carlo_data(self) -> pd.DataFrame:
        """
        Prepare data for Monte Carlo simulation by providing trade returns
        that can be randomly sampled and reordered.
        
        Returns:
            DataFrame with returns and metadata suitable for Monte Carlo analysis
        """
        df = self.get_trades_dataframe()
        
        if df.empty:
            return pd.DataFrame()
            
        mc_data = df[['trade_id', 'net_pnl', 'returns', 'direction', 
                     'strategy_signal', 'confidence_score']].copy()
        
        # Add additional columns useful for Monte Carlo
        mc_data['trade_rank'] = range(1, len(mc_data) + 1)
        mc_data['pnl_rank'] = mc_data['net_pnl'].rank()
        mc_data['return_rank'] = mc_data['returns'].rank()
        
        return mc_data
    
    def simulate_monte_carlo(self, num_simulations: int = 1000) -> pd.DataFrame:
        """
        Run Monte Carlo simulation by randomly reordering trade sequence.
        
        Args:
            num_simulations: Number of simulation runs
            
        Returns:
            DataFrame with simulation results
        """
        mc_data = self.get_monte_carlo_data()
        
        if mc_data.empty:
            return pd.DataFrame()
            
        simulation_results = []
        
        for sim in range(num_simulations):
            # Randomly shuffle trade returns
            shuffled_returns = mc_data['net_pnl'].sample(frac=1).reset_index(drop=True)
            
            # Calculate cumulative balance for this simulation
            sim_balance = self.initial_balance
            sim_equity_curve = [sim_balance]
            sim_peak = sim_balance
            sim_max_dd = 0.0
            
            for pnl in shuffled_returns:
                sim_balance += pnl
                sim_equity_curve.append(sim_balance)
                
                # Track drawdown
                if sim_balance > sim_peak:
                    sim_peak = sim_balance
                else:
                    dd = (sim_peak - sim_balance) / sim_peak
                    sim_max_dd = max(sim_max_dd, dd)
            
            # Calculate simulation metrics
            final_return = (sim_balance - self.initial_balance) / self.initial_balance
            
            simulation_results.append({
                'simulation': sim + 1,
                'final_balance': sim_balance,
                'total_return': final_return,
                'max_drawdown': sim_max_dd,
                'min_balance': min(sim_equity_curve),
                'max_balance': max(sim_equity_curve)
            })
        
        return pd.DataFrame(simulation_results)

def demonstrate_enhanced_tracking():
    """Demonstration of the enhanced trade tracking system"""
    
    print("=== Enhanced Trade Data Collection Demo ===\n")
    
    # Initialize tracker
    tracker = EnhancedTradeTracker(initial_balance=10000.0)
    
    # Simulate some trades with comprehensive data
    trades_to_simulate = [
        {
            'entry_time': datetime(2024, 1, 1, 9, 30),
            'exit_time': datetime(2024, 1, 1, 11, 15),
            'direction': 'LONG',
            'entry_price': 100.0,
            'exit_price': 102.5,
            'volume': 100,
            'commission': 2.0,
            'strategy_signal': 'breakout_momentum',
            'confidence_score': 0.85,
            'market_conditions': {'volatility': 0.15, 'trend': 'up'},
            'mfe': 3.0,
            'mae': -0.5
        },
        {
            'entry_time': datetime(2024, 1, 1, 14, 0),
            'exit_time': datetime(2024, 1, 1, 15, 30),
            'direction': 'SHORT',
            'entry_price': 102.0,
            'exit_price': 103.5,
            'volume': 100,
            'commission': 2.0,
            'strategy_signal': 'reversal_pattern',
            'confidence_score': 0.65,
            'market_conditions': {'volatility': 0.20, 'trend': 'down'},
            'mfe': 1.0,
            'mae': -2.0
        },
        {
            'entry_time': datetime(2024, 1, 2, 10, 0),
            'exit_time': datetime(2024, 1, 2, 12, 0),
            'direction': 'LONG',
            'entry_price': 101.0,
            'exit_price': 104.0,
            'volume': 150,
            'commission': 3.0,
            'strategy_signal': 'trend_following',
            'confidence_score': 0.90,
            'market_conditions': {'volatility': 0.12, 'trend': 'strong_up'},
            'mfe': 4.5,
            'mae': -0.8
        }
    ]
    
    # Record the trades
    for trade_data in trades_to_simulate:
        tracker.record_trade(**trade_data)
    
    # Get comprehensive DataFrame
    trades_df = tracker.get_trades_dataframe()
    print("Trades DataFrame columns:")
    print(trades_df.columns.tolist())
    print(f"\nDataFrame shape: {trades_df.shape}")
    print("\nSample data:")
    print(trades_df[['trade_id', 'direction', 'net_pnl', 'cumulative_balance', 'is_winner']].to_string())
    
    # Get performance metrics
    metrics = tracker.get_performance_metrics()
    print(f"\nPerformance Metrics:")
    for key, value in metrics.items():
        if isinstance(value, float):
            print(f"{key}: {value:.4f}")
        else:
            print(f"{key}: {value}")
    
    # Demonstrate Monte Carlo data preparation
    mc_data = tracker.get_monte_carlo_data()
    print(f"\nMonte Carlo Data Shape: {mc_data.shape}")
    print("Monte Carlo columns:", mc_data.columns.tolist())
    
    # Run small Monte Carlo simulation
    print("\nRunning Monte Carlo simulation...")
    mc_results = tracker.simulate_monte_carlo(num_simulations=100)
    print(f"Monte Carlo Results Shape: {mc_results.shape}")
    print("\nMonte Carlo Summary:")
    print(mc_results[['total_return', 'max_drawdown']].describe())
    
    print("\n=== Demo Complete ===")

if __name__ == "__main__":
    demonstrate_enhanced_tracking()
