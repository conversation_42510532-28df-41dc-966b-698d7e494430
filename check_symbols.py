"""
Check available symbols and find Boom 1000 Index
"""
import MetaTrader5 as mt5

# Initialize MT5
if not mt5.initialize():
    print(f"initialize() failed, error code = {mt5.last_error()}")
    quit()

print("MT5 connection established")

# Get all symbols
symbols = mt5.symbols_get()
if symbols is None:
    print("No symbols found")
    mt5.shutdown()
    quit()

# Convert to list and search for Boom 1000 Index.0
symbol_names = [symbol.name for symbol in symbols]
if 'Boom 1000 Index.0' in symbol_names:
    for symbol in symbols:
    print(symbol.name)

mt5.shutdown()
