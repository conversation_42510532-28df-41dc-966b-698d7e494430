"""
Boom 1000 Index Spike Analysis with TDI
Analyzes tick data to find spikes and calculate:
1. Average ticks before spikes
2. TDI for favorable tick collection after spikes
3. Double spike detection
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import re
import ast

class BoomSpikeAnalyzer:
    def __init__(self, csv_file):
        self.csv_file = csv_file
        self.df = None
        self.spikes = []
        self.double_spikes = []
        
    def load_and_parse_data(self):
        """Load and parse the tick data from CSV"""
        print("Loading tick data...")
        
        # Read the raw CSV
        raw_df = pd.read_csv(self.csv_file)
        
        # Parse the tick tuple data
        parsed_data = []
        
        for _, row in raw_df.iterrows():
            try:
                # Extract the tuple string
                tuple_str = row['0']
                # Parse the tuple
                if tuple_str.startswith('(') and tuple_str.endswith(')'):
                    # Remove parentheses and split by comma
                    values = tuple_str[1:-1].split(', ')
                    
                    tick_data = {
                        'timestamp': int(values[0]),
                        'bid': float(values[1]),
                        'ask': float(values[2]),
                        'last': float(values[3]),
                        'volume': int(values[4]),
                        'timestamp_msc': int(values[5]),
                        'flags': int(values[6]),
                        'volume_real': float(values[7]),
                        'symbol': row['symbol']
                    }
                    parsed_data.append(tick_data)
                    
            except Exception as e:
                print(f"Error parsing row: {e}")
                continue
        
        # Convert to DataFrame
        self.df = pd.DataFrame(parsed_data)
        
        # Convert timestamps
        self.df['datetime'] = pd.to_datetime(self.df['timestamp'], unit='s')
        self.df['datetime_msc'] = pd.to_datetime(self.df['timestamp_msc'], unit='ms')
        
        # Calculate mid price
        self.df['mid_price'] = (self.df['bid'] + self.df['ask']) / 2
        
        # Sort by timestamp
        self.df = self.df.sort_values('timestamp_msc').reset_index(drop=True)
        
        print(f"Loaded {len(self.df)} ticks")
        print(f"Date range: {self.df['datetime'].min()} to {self.df['datetime'].max()}")
        
        return self.df
    
    def detect_spikes(self, spike_threshold_pct=0.5):
        """
        Detect spikes in Boom 1000 Index
        A spike is typically a sudden large upward movement
        """
        print(f"Detecting spikes with threshold {spike_threshold_pct}%...")
        
        # Calculate price changes
        self.df['price_change'] = self.df['mid_price'].diff()
        self.df['price_change_pct'] = (self.df['price_change'] / self.df['mid_price'].shift(1)) * 100
        
        # Define spike conditions
        # For Boom 1000, we look for sudden upward movements
        spike_condition = (
            (self.df['price_change_pct'] > spike_threshold_pct) &
            (self.df['price_change'] > 0)  # Upward movement
        )
        
        spike_indices = self.df[spike_condition].index.tolist()
        
        # Filter spikes to avoid duplicates too close together (within 10 ticks)
        filtered_spikes = []
        for spike_idx in spike_indices:
            # Check if this spike is at least 10 ticks away from the last one
            if not filtered_spikes or spike_idx - filtered_spikes[-1] >= 10:
                filtered_spikes.append(spike_idx)
        
        self.spikes = filtered_spikes
        
        # Mark spikes in dataframe
        self.df['is_spike'] = False
        self.df.loc[self.spikes, 'is_spike'] = True
        
        print(f"Found {len(self.spikes)} spikes")
        
        # Detect double spikes (spikes within 100 ticks of each other)
        self.detect_double_spikes()
        
        return self.spikes
    
    def detect_double_spikes(self):
        """Detect double spikes - spikes that occur within 100 ticks of each other"""
        double_spikes = []
        
        for i in range(len(self.spikes) - 1):
            current_spike = self.spikes[i]
            next_spike = self.spikes[i + 1]
            
            # If next spike is within 100 ticks
            if next_spike - current_spike <= 100:
                double_spikes.append((current_spike, next_spike))
        
        self.double_spikes = double_spikes
        print(f"Found {len(self.double_spikes)} double spike pairs")
        
        return self.double_spikes
    
    def calculate_ticks_before_spikes(self):
        """Calculate average ticks before spikes occur"""
        if not self.spikes:
            print("No spikes detected. Run detect_spikes() first.")
            return None
        
        ticks_before_spikes = []
        
        for spike_idx in self.spikes:
            # Find the previous spike
            previous_spikes = [s for s in self.spikes if s < spike_idx]
            
            if previous_spikes:
                prev_spike_idx = max(previous_spikes)
                ticks_between = spike_idx - prev_spike_idx
            else:
                # If it's the first spike, count from the beginning
                ticks_between = spike_idx
            
            ticks_before_spikes.append(ticks_between)
        
        avg_ticks_before_spike = np.mean(ticks_before_spikes)
        median_ticks_before_spike = np.median(ticks_before_spikes)
        
        print(f"\\nSpike Statistics:")
        print(f"Average ticks before spike: {avg_ticks_before_spike:.1f}")
        print(f"Median ticks before spike: {median_ticks_before_spike:.1f}")
        print(f"Min ticks before spike: {min(ticks_before_spikes)}")
        print(f"Max ticks before spike: {max(ticks_before_spikes)}")
        
        return {
            'average': avg_ticks_before_spike,
            'median': median_ticks_before_spike,
            'all_values': ticks_before_spikes,
            'min': min(ticks_before_spikes),
            'max': max(ticks_before_spikes)
        }
    
    def calculate_tdi(self, period=13):
        """
        Calculate Traders Dynamic Index (TDI)
        TDI combines RSI with moving averages and Bollinger Bands
        """
        print(f"Calculating TDI with period {period}...")
        
        # Calculate RSI
        delta = self.df['mid_price'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        # TDI components
        self.df['rsi'] = rsi
        self.df['rsi_ma'] = rsi.rolling(window=period).mean()  # RSI Moving Average
        self.df['rsi_signal'] = rsi.rolling(window=7).mean()   # Signal line
        
        # Bollinger Bands on RSI
        rsi_std = rsi.rolling(window=period).std()
        self.df['rsi_upper_bb'] = self.df['rsi_ma'] + (2 * rsi_std)
        self.df['rsi_lower_bb'] = self.df['rsi_ma'] - (2 * rsi_std)
        
        return self.df[['rsi', 'rsi_ma', 'rsi_signal', 'rsi_upper_bb', 'rsi_lower_bb']]
    
    def analyze_post_spike_opportunities(self, lookback_ticks=50):
        """
        Analyze favorable tick collection opportunities after spikes using TDI
        """
        if not self.spikes:
            print("No spikes detected. Run detect_spikes() first.")
            return None
        
        if 'rsi' not in self.df.columns:
            self.calculate_tdi()
        
        post_spike_analysis = []
        
        for spike_idx in self.spikes:
            # Look at the next lookback_ticks after the spike
            end_idx = min(spike_idx + lookback_ticks, len(self.df) - 1)
            post_spike_data = self.df.iloc[spike_idx:end_idx + 1].copy()
            
            if len(post_spike_data) < 10:  # Need minimum data
                continue
            
            # Analyze TDI conditions for favorable entry
            favorable_conditions = (
                (post_spike_data['rsi'] < 30) |  # Oversold condition
                (post_spike_data['rsi'] > 70) |  # Overbought condition
                (post_spike_data['rsi_signal'] < post_spike_data['rsi_ma'])  # Signal crossover
            )
            
            favorable_ticks = post_spike_data[favorable_conditions]
            
            spike_info = {
                'spike_idx': spike_idx,
                'spike_time': self.df.iloc[spike_idx]['datetime'],
                'spike_price': self.df.iloc[spike_idx]['mid_price'],
                'spike_change_pct': self.df.iloc[spike_idx]['price_change_pct'],
                'post_spike_ticks': len(post_spike_data),
                'favorable_ticks': len(favorable_ticks),
                'favorable_percentage': (len(favorable_ticks) / len(post_spike_data)) * 100,
                'is_double_spike': any(spike_idx in pair for pair in self.double_spikes)
            }
            
            post_spike_analysis.append(spike_info)
        
        # Calculate overall statistics
        if post_spike_analysis:
            avg_favorable_pct = np.mean([s['favorable_percentage'] for s in post_spike_analysis])
            print(f"\\nPost-Spike TDI Analysis:")
            print(f"Average favorable tick percentage after spikes: {avg_favorable_pct:.1f}%")
            
            # 100% winning opportunities
            perfect_opportunities = [s for s in post_spike_analysis if s['favorable_percentage'] == 100.0]
            print(f"100% favorable opportunities: {len(perfect_opportunities)} out of {len(post_spike_analysis)} spikes")
            
            if perfect_opportunities:
                print("\\n100% Favorable Spikes:")
                for opp in perfect_opportunities[:5]:  # Show first 5
                    print(f"  - {opp['spike_time']}: {opp['spike_change_pct']:.2f}% spike, Price: {opp['spike_price']:.3f}")
        
        return post_spike_analysis
    
    def generate_report(self):
        """Generate comprehensive analysis report"""
        print("\\n" + "="*80)
        print("BOOM 1000 INDEX SPIKE ANALYSIS REPORT")
        print("="*80)
        
        # Data summary
        print(f"\\nDATA SUMMARY:")
        print(f"Total ticks analyzed: {len(self.df):,}")
        print(f"Time period: {self.df['datetime'].min()} to {self.df['datetime'].max()}")
        print(f"Duration: {(self.df['datetime'].max() - self.df['datetime'].min()).total_seconds() / 3600:.1f} hours")
        
        # Spike analysis
        if self.spikes:
            ticks_stats = self.calculate_ticks_before_spikes()
            post_spike_stats = self.analyze_post_spike_opportunities()
            
            print(f"\\nSPIKE DETECTION:")
            print(f"Total spikes found: {len(self.spikes)}")
            print(f"Double spikes: {len(self.double_spikes)}")
            print(f"Average spike frequency: {len(self.df) / len(self.spikes):.1f} ticks per spike")
            
            print(f"\\nTICKS BEFORE SPIKES:")
            print(f"Average: {ticks_stats['average']:.1f} ticks")
            print(f"Median: {ticks_stats['median']:.1f} ticks")
            print(f"Range: {ticks_stats['min']} - {ticks_stats['max']} ticks")
            
            if post_spike_stats:
                favorable_opportunities = [s for s in post_spike_stats if s['favorable_percentage'] >= 80]
                perfect_opportunities = [s for s in post_spike_stats if s['favorable_percentage'] == 100.0]
                
                print(f"\\nTDI POST-SPIKE ANALYSIS:")
                print(f"Spikes with 80%+ favorable ticks: {len(favorable_opportunities)}")
                print(f"Spikes with 100% favorable ticks: {len(perfect_opportunities)}")
                print(f"Success rate for 100% winning trades: {len(perfect_opportunities)/len(post_spike_stats)*100:.1f}%")
        
        print("\\n" + "="*80)
        
        return {
            'total_ticks': len(self.df),
            'total_spikes': len(self.spikes),
            'double_spikes': len(self.double_spikes),
            'avg_ticks_before_spike': ticks_stats['average'] if self.spikes else 0,
            'perfect_opportunities': len(perfect_opportunities) if self.spikes else 0
        }

def main():
    """Main analysis function"""
    # Initialize analyzer
    csv_file = "C:\\Users\\<USER>\\META\\Boom_1000_Index_7days_20250619_20250626.csv"
    analyzer = BoomSpikeAnalyzer(csv_file)
    
    # Load and parse data
    df = analyzer.load_and_parse_data()
    
    # Detect spikes
    spikes = analyzer.detect_spikes(spike_threshold_pct=0.3)  # Adjusted threshold
    
    # Calculate TDI
    tdi_data = analyzer.calculate_tdi()
    
    # Generate comprehensive report
    report = analyzer.generate_report()
    
    return analyzer, report

if __name__ == "__main__":
    analyzer, report = main()
