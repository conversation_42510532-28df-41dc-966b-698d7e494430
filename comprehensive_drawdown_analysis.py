import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import os

def load_trade_data(filename=None):
    """Load trade data from existing results file"""
    if filename is None:
        # Try to find the most recent trade results file
        log_files = [f for f in os.listdir('.') if f.startswith('fixed_lot_backtest_trades_') and f.endswith('.csv')]
        if log_files:
            filename = max(log_files, key=os.path.getctime)  # Most recent file
        else:
            print("No trade data file found. Please provide filename.")
            return None
    
    if os.path.exists(filename):
        df = pd.read_csv(filename)
        print(f"Loaded {len(df)} trades from {filename}")
        return df
    else:
        print(f"File {filename} not found.")
        return None

def calculate_high_water_marks_and_drawdowns(trades_df):
    """
    Calculate high-water marks of equity and derive drawdown metrics
    
    Returns:
    - per_trade_drawdown: Drawdown after each trade
    - maximum_drawdown: Largest drawdown experienced
    - drawdown_duration: Duration of drawdown periods
    """
    print("🏔️  Calculating High-Water Marks and Drawdowns...")
    
    # Ensure we have cumulative equity calculation
    if 'capital' in trades_df.columns:
        equity_series = trades_df['capital'].copy()
    else:
        # Calculate equity from PnL if capital column doesn't exist
        initial_capital = 100000  # Assume starting capital
        equity_series = initial_capital + trades_df['pnl'].cumsum()
    
    # Calculate high-water marks (running maximum of equity)
    high_water_marks = equity_series.cummax()
    
    # Calculate drawdowns (current equity - high water mark)
    drawdown_absolute = equity_series - high_water_marks
    drawdown_percentage = (drawdown_absolute / high_water_marks) * 100
    
    # Per-trade drawdown (drawdown after each individual trade)
    per_trade_drawdown = drawdown_percentage.copy()
    
    # Maximum drawdown
    maximum_drawdown = drawdown_percentage.min()
    
    # Drawdown duration calculation
    # Find periods where we're in drawdown (equity < high water mark)
    in_drawdown = drawdown_absolute < 0
    
    # Calculate consecutive drawdown periods
    drawdown_periods = []
    current_period = 0
    
    for is_dd in in_drawdown:
        if is_dd:
            current_period += 1
        else:
            if current_period > 0:
                drawdown_periods.append(current_period)
            current_period = 0
    
    # Add final period if we end in drawdown
    if current_period > 0:
        drawdown_periods.append(current_period)
    
    # Calculate drawdown duration statistics
    if drawdown_periods:
        max_drawdown_duration = max(drawdown_periods)
        avg_drawdown_duration = np.mean(drawdown_periods)
        total_drawdown_periods = len(drawdown_periods)
    else:
        max_drawdown_duration = 0
        avg_drawdown_duration = 0
        total_drawdown_periods = 0
    
    # Add columns to dataframe
    trades_df['equity'] = equity_series
    trades_df['high_water_mark'] = high_water_marks
    trades_df['drawdown_absolute'] = drawdown_absolute
    trades_df['drawdown_percentage'] = drawdown_percentage
    
    results = {
        'per_trade_drawdown': per_trade_drawdown,
        'maximum_drawdown': maximum_drawdown,
        'max_drawdown_duration': max_drawdown_duration,
        'avg_drawdown_duration': avg_drawdown_duration,
        'total_drawdown_periods': total_drawdown_periods,
        'drawdown_periods': drawdown_periods
    }
    
    return results

def calculate_risk_of_ruin(trades_df, capital=None, unit_risk=None):
    """
    Calculate risk-of-ruin probability using the formula:
    P(ruin) = ((q/p)^(capital/unit))/(1-((q/p)^(capital/unit)))
    where p = win probability, q = 1-p
    """
    print("💀 Calculating Risk-of-Ruin Probability...")
    
    # Calculate win probability from trade data
    wins = (trades_df['pnl'] > 0).sum()
    total_trades = len(trades_df)
    p = wins / total_trades  # Win probability
    q = 1 - p  # Loss probability
    
    # Determine capital and unit risk
    if capital is None:
        if 'capital' in trades_df.columns:
            capital = trades_df['capital'].iloc[0]  # Initial capital
        else:
            capital = 100000  # Default assumption
    
    if unit_risk is None:
        # Use average loss as unit risk
        losses = trades_df[trades_df['pnl'] < 0]['pnl']
        if len(losses) > 0:
            unit_risk = abs(losses.mean())
        else:
            unit_risk = abs(trades_df['pnl'].mean())  # Fallback
    
    # Risk of ruin calculation
    if p == 0:  # All trades are losses
        risk_of_ruin = 1.0
    elif q == 0:  # All trades are wins
        risk_of_ruin = 0.0
    else:
        # Apply the formula
        ratio = q / p
        exponent = capital / unit_risk
        
        if ratio == 1:
            # Special case when p = q = 0.5
            risk_of_ruin = 1.0
        else:
            try:
                risk_of_ruin = (ratio ** exponent) / (1 - (ratio ** exponent))
                # Ensure result is between 0 and 1
                risk_of_ruin = min(max(risk_of_ruin, 0), 1)
            except (OverflowError, ZeroDivisionError):
                # Handle extreme cases
                if ratio > 1:
                    risk_of_ruin = 1.0
                else:
                    risk_of_ruin = 0.0
    
    results = {
        'risk_of_ruin': risk_of_ruin,
        'win_probability': p,
        'loss_probability': q,
        'capital': capital,
        'unit_risk': unit_risk,
        'total_trades': total_trades,
        'wins': wins,
        'losses': total_trades - wins
    }
    
    return results

def validate_with_reference_examples():
    """
    Validate risk-of-ruin calculations with known reference examples
    """
    print("✅ Validating Risk-of-Ruin Calculations with Reference Examples...")
    
    reference_examples = [
        {
            'name': 'Fair Coin (50/50)',
            'p': 0.5,
            'capital': 100,
            'unit_risk': 1,
            'expected_ror': 1.0  # With fair odds, RoR = 1
        },
        {
            'name': 'Slight Edge (60/40)',
            'p': 0.6,
            'capital': 100,
            'unit_risk': 1,
            'expected_ror': 0.0625  # (0.4/0.6)^100 ≈ 0.0625
        },
        {
            'name': 'Strong Edge (70/30)',
            'p': 0.7,
            'capital': 100,
            'unit_risk': 1,
            'expected_ror': 0.0000006  # Very low RoR
        }
    ]
    
    validation_results = []
    
    for example in reference_examples:
        p = example['p']
        q = 1 - p
        capital = example['capital']
        unit_risk = example['unit_risk']
        
        # Calculate using our formula
        if p == q:
            calculated_ror = 1.0
        else:
            ratio = q / p
            exponent = capital / unit_risk
            calculated_ror = (ratio ** exponent) / (1 - (ratio ** exponent))
        
        validation_results.append({
            'name': example['name'],
            'calculated_ror': calculated_ror,
            'expected_ror': example.get('expected_ror', 'N/A'),
            'parameters': f"p={p}, capital={capital}, unit={unit_risk}"
        })
        
        print(f"  {example['name']}: Calculated RoR = {calculated_ror:.6f}")
    
    return validation_results

def generate_comprehensive_report(trades_df, drawdown_results, ror_results, validation_results):
    """Generate comprehensive drawdown and risk-of-ruin report"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_filename = f"drawdown_ror_analysis_{timestamp}.txt"
    
    with open(report_filename, 'w') as f:
        f.write("=" * 70 + "\n")
        f.write("COMPREHENSIVE DRAWDOWN & RISK-OF-RUIN ANALYSIS REPORT\n")
        f.write("=" * 70 + "\n\n")
        
        # Dataset summary
        f.write(f"DATASET SUMMARY:\n")
        f.write(f"Total Trades Analyzed: {len(trades_df):,}\n")
        f.write(f"Date Range: {trades_df['entry_time'].min()} to {trades_df['entry_time'].max()}\n")
        f.write(f"Total PnL: ${trades_df['pnl'].sum():,.2f}\n\n")
        
        # Drawdown Analysis
        f.write("DRAWDOWN ANALYSIS:\n")
        f.write("-" * 30 + "\n")
        f.write(f"Maximum Drawdown: {drawdown_results['maximum_drawdown']:.2f}%\n")
        f.write(f"Maximum Drawdown Duration: {drawdown_results['max_drawdown_duration']} trades\n")
        f.write(f"Average Drawdown Duration: {drawdown_results['avg_drawdown_duration']:.1f} trades\n")
        f.write(f"Total Drawdown Periods: {drawdown_results['total_drawdown_periods']}\n\n")
        
        # Risk of Ruin Analysis
        f.write("RISK-OF-RUIN ANALYSIS:\n")
        f.write("-" * 30 + "\n")
        f.write(f"Risk of Ruin Probability: {ror_results['risk_of_ruin']:.6f} ({ror_results['risk_of_ruin']*100:.4f}%)\n")
        f.write(f"Win Probability (p): {ror_results['win_probability']:.4f}\n")
        f.write(f"Loss Probability (q): {ror_results['loss_probability']:.4f}\n")
        f.write(f"Capital: ${ror_results['capital']:,.2f}\n")
        f.write(f"Unit Risk: ${ror_results['unit_risk']:.2f}\n")
        f.write(f"Wins: {ror_results['wins']:,} / {ror_results['total_trades']:,}\n\n")
        
        # Validation Results
        f.write("VALIDATION WITH REFERENCE EXAMPLES:\n")
        f.write("-" * 40 + "\n")
        for result in validation_results:
            f.write(f"{result['name']}: {result['calculated_ror']:.6f}\n")
            f.write(f"  Parameters: {result['parameters']}\n")
        f.write("\n")
        
        # Formula Used
        f.write("FORMULA USED:\n")
        f.write("-" * 15 + "\n")
        f.write("P(ruin) = ((q/p)^(capital/unit)) / (1-((q/p)^(capital/unit)))\n")
        f.write("where:\n")
        f.write("  p = probability of winning a trade\n")
        f.write("  q = probability of losing a trade = 1-p\n")
        f.write("  capital = total trading capital\n")
        f.write("  unit = risk per trade (average loss amount)\n\n")
    
    print(f"📄 Comprehensive report saved to: {report_filename}")
    return report_filename

def plot_equity_and_drawdown(trades_df, drawdown_results):
    """Create visualization of equity curve and drawdown"""
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
    
    # Plot equity curve with high-water marks
    ax1.plot(trades_df.index, trades_df['equity'], label='Equity', linewidth=1)
    ax1.plot(trades_df.index, trades_df['high_water_mark'], label='High Water Mark', 
             linewidth=2, color='green', alpha=0.7)
    ax1.set_title('Equity Curve with High-Water Marks')
    ax1.set_ylabel('Equity ($)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Plot drawdown
    ax2.fill_between(trades_df.index, trades_df['drawdown_percentage'], 0, 
                     color='red', alpha=0.6, label='Drawdown')
    ax2.set_title('Drawdown Over Time')
    ax2.set_xlabel('Trade Number')
    ax2.set_ylabel('Drawdown (%)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    plot_filename = f"equity_drawdown_analysis_{timestamp}.png"
    plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
    print(f"📊 Equity and drawdown plots saved to: {plot_filename}")
    plt.show()
    
    return plot_filename

def main():
    """Main analysis function"""
    print("🚀 STARTING COMPREHENSIVE DRAWDOWN & RISK-OF-RUIN ANALYSIS")
    print("=" * 70)
    
    # Step 1: Load trade data
    trades_df = load_trade_data()
    if trades_df is None:
        print("❌ Could not load trade data. Exiting.")
        return
    
    # Step 2: Calculate drawdowns
    drawdown_results = calculate_high_water_marks_and_drawdowns(trades_df)
    
    # Step 3: Calculate risk of ruin
    ror_results = calculate_risk_of_ruin(trades_df)
    
    # Step 4: Validate with reference examples
    validation_results = validate_with_reference_examples()
    
    # Step 5: Generate comprehensive report
    report_file = generate_comprehensive_report(trades_df, drawdown_results, ror_results, validation_results)
    
    # Step 6: Create visualizations
    plot_file = plot_equity_and_drawdown(trades_df, drawdown_results)
    
    # Step 7: Summary output
    print("\n" + "=" * 70)
    print("📊 ANALYSIS COMPLETE - KEY RESULTS:")
    print("=" * 70)
    print(f"📉 Maximum Drawdown: {drawdown_results['maximum_drawdown']:.2f}%")
    print(f"⏱️  Max Drawdown Duration: {drawdown_results['max_drawdown_duration']} trades")
    print(f"💀 Risk of Ruin: {ror_results['risk_of_ruin']:.6f} ({ror_results['risk_of_ruin']*100:.4f}%)")
    print(f"🎯 Win Rate: {ror_results['win_probability']*100:.2f}%")
    print(f"📄 Report: {report_file}")
    print(f"📊 Charts: {plot_file}")
    print("=" * 70)
    
    return {
        'drawdown_results': drawdown_results,
        'ror_results': ror_results,
        'validation_results': validation_results,
        'trades_df': trades_df
    }

if __name__ == "__main__":
    results = main()
