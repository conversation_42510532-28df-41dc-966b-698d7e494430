#!/usr/bin/env python3
"""
Apply Streak Analysis to Real Trading Results

This script demonstrates the implementation of Step 3: Win/Loss Streak Analysis
by applying the streak analysis functions to actual backtest trading results.
"""

import pandas as pd
import numpy as np
from streak_analysis import (
    analyze_win_loss_streaks, 
    generate_streak_report,
    find_longest_streaks_details,
    create_streak_histogram_data
)
import glob
import os

def find_latest_results_file():
    """Find the most recent backtest results file."""
    pattern = "fixed_lot_backtest_trades_*.csv"
    files = glob.glob(pattern)
    if not files:
        # Try kelly backtest files
        pattern = "kelly_backtest_trades_*.csv"
        files = glob.glob(pattern)
    
    if files:
        # Sort by modification time and return the latest
        latest_file = max(files, key=os.path.getmtime)
        return latest_file
    else:
        return None

def apply_streak_analysis_to_results():
    """Apply comprehensive streak analysis to the latest trading results."""
    
    # Find the latest results file
    results_file = find_latest_results_file()
    
    if not results_file:
        print("❌ No backtest results files found!")
        return
    
    print(f"📊 Analyzing streak patterns in: {results_file}")
    print("=" * 60)
    
    # Load the trading results
    try:
        df = pd.read_csv(results_file)
        print(f"✅ Loaded {len(df)} trades from results file")
    except Exception as e:
        print(f"❌ Error loading file: {e}")
        return
    
    # Check columns in the dataframe
    print(f"📋 Available columns: {list(df.columns)}")
    
    # Determine win/loss columns
    win_col = None
    loss_col = None
    
    if 'is_winner' in df.columns:
        win_col = 'is_winner'
    elif 'winner' in df.columns:
        win_col = 'winner'
    elif 'win' in df.columns:
        win_col = 'win'
    
    if 'is_loser' in df.columns:
        loss_col = 'is_loser'
    elif 'loser' in df.columns:
        loss_col = 'loser'
    elif 'loss' in df.columns:
        loss_col = 'loss'
    
    # If no explicit win/loss columns, create them from PnL
    if not win_col and 'pnl' in df.columns:
        print("📈 Creating win/loss indicators from PnL column...")
        df['is_winner'] = df['pnl'] > 0
        df['is_loser'] = df['pnl'] <= 0
        win_col = 'is_winner'
        loss_col = 'is_loser'
    elif not win_col and 'net_pnl' in df.columns:
        print("📈 Creating win/loss indicators from net_pnl column...")
        df['is_winner'] = df['net_pnl'] > 0
        df['is_loser'] = df['net_pnl'] <= 0
        win_col = 'is_winner'
        loss_col = 'is_loser'
    
    if not win_col:
        print("❌ Could not find or create win/loss columns!")
        return
    
    print(f"🎯 Using columns: {win_col} (wins), {loss_col} (losses)")
    print()
    
    # STEP 3 IMPLEMENTATION: WIN/LOSS STREAK ANALYSIS
    print("🚀 STEP 3: IMPLEMENTING WIN/LOSS STREAK ANALYSIS")
    print("=" * 60)
    
    # 1. Generate comprehensive streak report
    print("1️⃣ Generating comprehensive streak analysis report...")
    streak_report = generate_streak_report(df, win_col, loss_col)
    
    # Display the summary
    print(streak_report['summary_text'])
    
    # 2. Display frequency distributions (histograms)
    print("2️⃣ FREQUENCY DISTRIBUTION OF STREAK LENGTHS:")
    print("-" * 50)
    histogram_df = streak_report['histogram_df']
    
    if not histogram_df.empty:
        # Separate win and loss histograms
        win_hist = histogram_df[histogram_df['streak_type'] == 'win']
        loss_hist = histogram_df[histogram_df['streak_type'] == 'loss']
        
        print("WIN STREAK FREQUENCY DISTRIBUTION:")
        if not win_hist.empty:
            for _, row in win_hist.iterrows():
                print(f"  Length {row['streak_length']:2d}: {row['frequency']:4d} streaks ({row['percentage']:5.1f}%)")
        
        print("\nLOSS STREAK FREQUENCY DISTRIBUTION:")
        if not loss_hist.empty:
            for _, row in loss_hist.iterrows():
                print(f"  Length {row['streak_length']:2d}: {row['frequency']:4d} streaks ({row['percentage']:5.1f}%)")
    
    print()
    
    # 3. Find and display longest streak details
    print("3️⃣ LONGEST STREAK DETAILS:")
    print("-" * 50)
    longest_details = find_longest_streaks_details(df, win_col, loss_col)
    
    # Display longest win streak details
    win_details = longest_details['longest_win_streak_details']
    if not win_details.empty:
        print(f"LONGEST WIN STREAK ({len(win_details)} trades):")
        relevant_cols = ['trade_id'] if 'trade_id' in win_details.columns else []
        if 'entry_time' in win_details.columns:
            relevant_cols.append('entry_time')
        if 'pnl' in win_details.columns:
            relevant_cols.append('pnl')
        elif 'net_pnl' in win_details.columns:
            relevant_cols.append('net_pnl')
        relevant_cols.extend([win_col, 'streak_position'])
        
        print(win_details[relevant_cols].head(10))
        if len(win_details) > 10:
            print(f"... and {len(win_details) - 10} more trades")
    
    print()
    
    # Display longest loss streak details
    loss_details = longest_details['longest_loss_streak_details']
    if not loss_details.empty:
        print(f"LONGEST LOSS STREAK ({len(loss_details)} trades):")
        relevant_cols = ['trade_id'] if 'trade_id' in loss_details.columns else []
        if 'entry_time' in loss_details.columns:
            relevant_cols.append('entry_time')
        if 'pnl' in loss_details.columns:
            relevant_cols.append('pnl')
        elif 'net_pnl' in loss_details.columns:
            relevant_cols.append('net_pnl')
        relevant_cols.extend([loss_col, 'streak_position'])
        
        print(loss_details[relevant_cols].head(10))
        if len(loss_details) > 10:
            print(f"... and {len(loss_details) - 10} more trades")
    
    print()
    
    # 4. Save detailed analysis to CSV
    output_filename = f"streak_analysis_results_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    # Combine all streak data for export
    detailed_analysis = streak_report['detailed_analysis']
    streak_summary = detailed_analysis['streak_summary_df']
    
    if not streak_summary.empty:
        streak_summary.to_csv(output_filename, index=False)
        print(f"💾 Detailed streak analysis saved to: {output_filename}")
    
    # 5. Generate summary dictionary for reporting
    results_dict = {
        'longest_win_streak': streak_report['longest_win_streak'],
        'longest_loss_streak': streak_report['longest_loss_streak'],
        'total_streaks': streak_report['total_streaks'],
        'average_win_streak': streak_report['average_win_streak'],
        'average_loss_streak': streak_report['average_loss_streak'],
        'win_rate': streak_report['win_rate'],
        'loss_rate': streak_report['loss_rate'],
        'win_streak_distribution': streak_report['win_streak_distribution'],
        'loss_streak_distribution': streak_report['loss_streak_distribution']
    }
    
    print("📋 STEP 3 COMPLETE - RETURNING ANALYSIS RESULTS:")
    print("-" * 50)
    print("Results Dictionary Structure:")
    for key, value in results_dict.items():
        if isinstance(value, dict):
            print(f"  {key}: {dict(value)} (histogram data)")
        else:
            print(f"  {key}: {value}")
    
    print()
    print("✅ Step 3: Win/Loss Streak Analysis - COMPLETED SUCCESSFULLY!")
    print("📊 All streak patterns detected, analyzed, and ready for report usage")
    
    return results_dict, streak_report['histogram_df'], streak_summary

if __name__ == "__main__":
    apply_streak_analysis_to_results()
