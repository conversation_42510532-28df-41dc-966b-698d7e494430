import pandas as pd
import numpy as np
import os

# Load OHLC data
DATA_PATH = "stpRNG_1min.csv"
if not os.path.isfile(DATA_PATH):
    raise FileNotFoundError(f"File not found: {DATA_PATH}")

df = pd.read_csv(DATA_PATH)
df = df.dropna().reset_index(drop=True)

print(f"Loaded {len(df)} rows of data")

# Parameters from Pine Script
src = 'close'  # OHLC Type
i_fastEMA = 12  # Fast EMA
i_slowEMA = 25  # Slow EMA
i_defEMA = 25   # Consolidated EMA
i_bothEMAs = True  # Show Both EMAs

# Stoch RSI parameters
smoothK = 3
smoothD = 3
lengthRSI = 14
lengthStoch = 14
bandno0 = 80  # Upper Band
bandno2 = 50  # Middle Band
bandno1 = 20  # Lower Band

# Moving Average Filter
maTypeChoice = 'EMA'  # 'EMA', 'WMA', 'SMA', 'None'
maSrc = 'close'
maLen = 200

def calculate_ema(series, period):
    """Calculate Exponential Moving Average"""
    return series.ewm(span=period, adjust=False).mean()

def calculate_sma(series, period):
    """Calculate Simple Moving Average"""
    return series.rolling(window=period).mean()

def calculate_wma(series, period):
    """Calculate Weighted Moving Average"""
    weights = np.arange(1, period + 1)
    return series.rolling(window=period).apply(lambda x: np.dot(x, weights) / weights.sum(), raw=True)

def calculate_rsi(series, period):
    """Calculate RSI"""
    delta = series.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))

def calculate_stoch(high, low, close, period):
    """Calculate Stochastic oscillator"""
    lowest_low = low.rolling(window=period).min()
    highest_high = high.rolling(window=period).max()
    k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
    return k_percent

# Calculate EMAs
df['v_fastEMA'] = calculate_ema(df[src], i_fastEMA)
df['v_slowEMA'] = calculate_ema(df[src], i_slowEMA)
df['v_biasEMA'] = calculate_ema(df[src], i_defEMA)

# EMA Color logic
df['emaColor'] = np.where(df['v_fastEMA'] > df['v_slowEMA'], 'green', 
                 np.where(df['v_fastEMA'] < df['v_slowEMA'], 'red', 'orange'))

# Buy/Sell conditions
df['buy'] = df['v_fastEMA'] > df['v_slowEMA']
df['sell'] = df['v_fastEMA'] < df['v_slowEMA']

# Initialize counters
df['countBuy'] = 0
df['countSell'] = 0

# Calculate counters (Pine Script logic)
for i in range(1, len(df)):
    if df.at[i, 'buy']:
        df.at[i, 'countBuy'] = df.at[i-1, 'countBuy'] + 1
        df.at[i, 'countSell'] = 0
    elif df.at[i, 'sell']:
        df.at[i, 'countSell'] = df.at[i-1, 'countSell'] + 1
        df.at[i, 'countBuy'] = 0
    else:
        df.at[i, 'countBuy'] = df.at[i-1, 'countBuy']
        df.at[i, 'countSell'] = df.at[i-1, 'countSell']

# Signal conditions
df['buy_prev'] = df['buy'].shift(1).fillna(False)
df['sell_prev'] = df['sell'].shift(1).fillna(False)

df['buysignal'] = ((df['countBuy'] < 2) & 
                   (df['countBuy'] > 0) & 
                   (df['countSell'] < 1) & 
                   df['buy'] & 
                   ~df['buy_prev'])

df['sellsignal'] = ((df['countSell'] > 0) & 
                    (df['countSell'] < 2) & 
                    (df['countBuy'] < 1) & 
                    df['sell'] & 
                    ~df['sell_prev'])

# Bull/Bear conditions
df['bull'] = df['countBuy'] > 1
df['bear'] = df['countSell'] > 1

# Stochastic RSI calculation
df['rsi1'] = calculate_rsi(df[src], lengthRSI)

# For Stochastic, we need to use RSI as high, low, and close
df['k_raw'] = calculate_stoch(df['rsi1'], df['rsi1'], df['rsi1'], lengthStoch)
df['k'] = calculate_sma(df['k_raw'], smoothK)
df['d'] = calculate_sma(df['k'], smoothD)

# Moving Average Filter
if maTypeChoice == 'EMA':
    df['maValue'] = calculate_ema(df[maSrc], maLen)
elif maTypeChoice == 'WMA':
    df['maValue'] = calculate_wma(df[maSrc], maLen)
elif maTypeChoice == 'SMA':
    df['maValue'] = calculate_sma(df[maSrc], maLen)
else:
    df['maValue'] = 0

# Crossover checks
df['crossupCHECK'] = (maTypeChoice == 'None') | ((df['open'] > df['maValue']) & (maTypeChoice != 'None'))
df['crossdownCHECK'] = (maTypeChoice == 'None') | ((df['open'] < df['maValue']) & (maTypeChoice != 'None'))

# Stoch RSI crossovers
df['k_cross_over_d'] = (df['k'] > df['d']) & (df['k'].shift(1) <= df['d'].shift(1))
df['k_cross_under_d'] = (df['k'] < df['d']) & (df['k'].shift(1) >= df['d'].shift(1))

# Stoch RSI alerts
df['crossupalert'] = (df['crossupCHECK'] & 
                      df['k_cross_over_d'] & 
                      ((df['k'] < bandno2) | (df['d'] < bandno2)))

df['crossdownalert'] = (df['crossdownCHECK'] & 
                        df['k_cross_under_d'] & 
                        ((df['k'] > bandno2) | (df['d'] > bandno2)))

df['crossupOSalert'] = (df['crossupCHECK'] & 
                        df['k_cross_over_d'] & 
                        ((df['k'] < bandno1) | (df['d'] < bandno1)))

df['crossdownOBalert'] = (df['crossdownCHECK'] & 
                          df['k_cross_under_d'] & 
                          ((df['k'] > bandno0) | (df['d'] > bandno0)))

# Combined signals (EMA + Stoch RSI filter)
df['final_buy_signal'] = df['buysignal'] & df['crossupalert']
df['final_sell_signal'] = df['sellsignal'] & df['crossdownalert']

print(f"\n=== SIGNAL ANALYSIS ===")
print(f"EMA Buy signals: {df['buysignal'].sum()}")
print(f"EMA Sell signals: {df['sellsignal'].sum()}")
print(f"Stoch RSI crossup alerts: {df['crossupalert'].sum()}")
print(f"Stoch RSI crossdown alerts: {df['crossdownalert'].sum()}")
print(f"Final Buy signals (EMA + Stoch): {df['final_buy_signal'].sum()}")
print(f"Final Sell signals (EMA + Stoch): {df['final_sell_signal'].sum()}")

# Backtest function
def run_backtest(df, buy_signal_col, sell_signal_col, strategy_name):
    """Run backtest with given buy/sell signals"""
    capital = 100_000
    risk_per_trade = 0.004
    balance = capital
    position = 0
    entry_price = 0
    entry_index = 0
    trades = []
    
    for i in range(1, len(df)):
        current_price = df.at[i, 'open']
        buy_signal = df.at[i, buy_signal_col]
        sell_signal = df.at[i, sell_signal_col]
        
        # Exit positions
        if position > 0 and sell_signal:  # Exit long on sell signal
            pnl = (current_price - entry_price) / entry_price
            duration = i - entry_index
            
            trades.append({
                'entry_index': entry_index,
                'exit_index': i,
                'entry_price': entry_price,
                'exit_price': current_price,
                'position': 'long',
                'duration': duration,
                'pnl_pct': pnl * 100,
                'result': 'win' if pnl > 0 else 'loss'
            })
            
            if pnl > 0:
                balance *= (1 + risk_per_trade)
            else:
                balance *= (1 - risk_per_trade)
            
            position = 0
            
        elif position < 0 and buy_signal:  # Exit short on buy signal
            pnl = (entry_price - current_price) / entry_price
            duration = i - entry_index
            
            trades.append({
                'entry_index': entry_index,
                'exit_index': i,
                'entry_price': entry_price,
                'exit_price': current_price,
                'position': 'short',
                'duration': duration,
                'pnl_pct': pnl * 100,
                'result': 'win' if pnl > 0 else 'loss'
            })
            
            if pnl > 0:
                balance *= (1 + risk_per_trade)
            else:
                balance *= (1 - risk_per_trade)
            
            position = 0
        
        # Enter new positions
        if position == 0:
            if buy_signal:
                position = 1
                entry_price = current_price
                entry_index = i
            elif sell_signal:
                position = -1
                entry_price = current_price
                entry_index = i
    
    # Calculate results
    total_trades = len(trades)
    wins = len([t for t in trades if t['result'] == 'win'])
    losses = total_trades - wins
    win_rate = wins / total_trades * 100 if total_trades > 0 else 0
    total_return = (balance - capital) / capital * 100
    
    return {
        'strategy': strategy_name,
        'total_trades': total_trades,
        'wins': wins,
        'losses': losses,
        'win_rate': win_rate,
        'final_balance': balance,
        'total_return': total_return,
        'trades': trades
    }

# Run different strategy variations
print(f"\n=== BACKTEST RESULTS ===")

# Strategy 1: EMA signals only
ema_results = run_backtest(df, 'buysignal', 'sellsignal', 'EMA Only')
print(f"\n{ema_results['strategy']}:")
print(f"  Trades: {ema_results['total_trades']}")
print(f"  Win Rate: {ema_results['win_rate']:.2f}%")
print(f"  Return: {ema_results['total_return']:+.2f}%")

# Strategy 2: EMA + Stoch RSI filter
combined_results = run_backtest(df, 'final_buy_signal', 'final_sell_signal', 'EMA + Stoch RSI')
print(f"\n{combined_results['strategy']}:")
print(f"  Trades: {combined_results['total_trades']}")
print(f"  Win Rate: {combined_results['win_rate']:.2f}%")
print(f"  Return: {combined_results['total_return']:+.2f}%")

# Strategy 3: Bull/Bear trend following
df['bull_buy'] = df['bull'] & ~df['bull'].shift(1).fillna(False)
df['bear_sell'] = df['bear'] & ~df['bear'].shift(1).fillna(False)

trend_results = run_backtest(df, 'bull_buy', 'bear_sell', 'Bull/Bear Trend')
print(f"\n{trend_results['strategy']}:")
print(f"  Trades: {trend_results['total_trades']}")
print(f"  Win Rate: {trend_results['win_rate']:.2f}%")
print(f"  Return: {trend_results['total_return']:+.2f}%")

# Strategy 4: Stoch RSI alerts only
stoch_results = run_backtest(df, 'crossupalert', 'crossdownalert', 'Stoch RSI Only')
print(f"\n{stoch_results['strategy']}:")
print(f"  Trades: {stoch_results['total_trades']}")
print(f"  Win Rate: {stoch_results['win_rate']:.2f}%")
print(f"  Return: {stoch_results['total_return']:+.2f}%")

# Find best strategy
all_results = [ema_results, combined_results, trend_results, stoch_results]
best_strategy = max(all_results, key=lambda x: x['total_return'])

print(f"\n=== BEST STRATEGY ===")
print(f"Best: {best_strategy['strategy']}")
print(f"Return: {best_strategy['total_return']:+.2f}%")
print(f"Trades: {best_strategy['total_trades']}")
print(f"Win Rate: {best_strategy['win_rate']:.2f}%")

# Save results
results_df = pd.DataFrame([
    {
        'Strategy': r['strategy'],
        'Trades': r['total_trades'],
        'Win_Rate': r['win_rate'],
        'Return': r['total_return']
    }
    for r in all_results
])

results_df.to_csv('trader_xo_results.csv', index=False)
print(f"\nResults saved to 'trader_xo_results.csv'")

# Save best strategy trades
if best_strategy['trades']:
    trades_df = pd.DataFrame(best_strategy['trades'])
    trades_df.to_csv('trader_xo_best_trades.csv', index=False)
    print(f"Best strategy trades saved to 'trader_xo_best_trades.csv'")

# Compare with previous Q-Trend results
print(f"\n=== COMPARISON WITH Q-TREND ===")
print(f"Q-Trend (previous):      375 trades, +30.34% return, 58.93% win rate")
print(f"Trader XO (best):       {best_strategy['total_trades']} trades, {best_strategy['total_return']:+.2f}% return, {best_strategy['win_rate']:.2f}% win rate")

# Show recent signals for verification
print(f"\n=== RECENT SIGNALS (last 20 bars) ===")
recent_data = df[['buysignal', 'sellsignal', 'final_buy_signal', 'final_sell_signal', 'bull', 'bear']].tail(20)
signal_summary = recent_data.any()
print("Signal activity in last 20 bars:")
for col, active in signal_summary.items():
    if active:
        print(f"  {col}: YES")
