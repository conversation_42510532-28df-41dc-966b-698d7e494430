import pandas as pd
import numpy as np
from traders_dynamic_index import calculate_tdi

def analyze_boom_data(file_path):
    df = pd.read_csv(file_path)
    df['time'] = pd.to_datetime(df['time'])
    df = df.set_index('time')

    # Calculate TDI
    df_with_tdi = calculate_tdi(df.copy())

    print("--- Boom_1000_Index Data with TDI (Tail) ---")
    print(df_with_tdi.tail())

if __name__ == "__main__":
    csv_file_path = r"C:\Users\<USER>\META\Boom_1000_Index_1-minute_90days_25920_bars.csv"
    analyze_boom_data(csv_file_path)
