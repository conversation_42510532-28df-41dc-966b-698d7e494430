"""
Enhanced MT5 Tick Data Downloader for Boom 1000 Index.0
With better error handling and troubleshootingrun it
"""

import MetaTrader5 as mt5
import pandas as pd
from datetime import datetime, timedelta
import pytz
import os

def download_boom_ticks():
    """Download Boom 1000 Index.0 tick data with troubleshooting"""
    
    # Initialize MT5
    if not mt5.initialize():
        print(f"initialize() failed, error code = {mt5.last_error()}")
        return None
    
    print("MT5 connection established")
    
    try:
        symbol = "Boom 1000 Index.0"
        
        # Check if symbol is available
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info is None:
            print(f"Symbol {symbol} not found")
            return None
        
        print(f"Symbol {symbol} found: {symbol_info}")
        
        # Try to get current tick first
        current_tick = mt5.symbol_info_tick(symbol)
        if current_tick is None:
            print(f"Failed to get current tick for {symbol}")
            return None
        
        print(f"Current tick: time={current_tick.time}, bid={current_tick.bid}, ask={current_tick.ask}")
        
        # Set timezone to UTC
        timezone = pytz.timezone("Etc/UTC")
        
        # Try a smaller time range first - last 1 hour
        date_to = datetime.now(timezone)
        date_from = date_to - timedelta(hours=1)
        
        print(f"Downloading ticks from {date_from} to {date_to}")
        
        # Try with smaller count first
        ticks = mt5.copy_ticks_range(symbol, date_from, date_to, mt5.COPY_TICKS_ALL)
        
        if ticks is None:
            print(f"No ticks received, error: {mt5.last_error()}")
            
            # Try with count-based approach instead
            print("Trying count-based approach...")
            ticks = mt5.copy_ticks_from(symbol, date_from, 1000, mt5.COPY_TICKS_ALL)
            
            if ticks is None:
                print(f"Count-based approach also failed, error: {mt5.last_error()}")
                return None
        
        print(f"Successfully downloaded {len(ticks)} ticks")
        
        # Convert to DataFrame
        ticks_df = pd.DataFrame(ticks)
        
        # Convert time columns
        ticks_df['time'] = pd.to_datetime(ticks_df['time'], unit='s')
        ticks_df['time_msc'] = pd.to_datetime(ticks_df['time_msc'], unit='ms')
        
        # Add symbol column
        ticks_df['symbol'] = symbol
        
        # Save to CSV
        filename = f"Boom_1000_Index_ticks_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        ticks_df.to_csv(filename, index=False)
        print(f"Data saved to {filename}")
        
        print("Sample data:")
        print(ticks_df.head())
        print(f"\nData shape: {ticks_df.shape}")
        print(f"Date range: {ticks_df['time'].min()} to {ticks_df['time'].max()}")
        
        return ticks_df
        
    except Exception as e:
        print(f"Error during download: {e}")
        return None
    
    finally:
        mt5.shutdown()
        print("MT5 connection closed")

if __name__ == "__main__":
    ticks_data = download_boom_ticks()
