"""
FIXED XGBoost Analysis for Boom 1000 Index - NO LOOK-AHEAD BIAS
Proper time-series training with only historical data
"""

import pandas as pd
import numpy as np
import xgboost as xgb
from sklearn.metrics import accuracy_score, classification_report
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

class ProperBoomAnalyzer:
    def __init__(self, csv_file):
        self.csv_file = csv_file
        self.df = None
        self.model = None
        self.scaler = StandardScaler()
        
    def load_and_parse_data(self):
        """Load and parse tick data"""
        print("Loading data for PROPER analysis (no look-ahead)...")
        raw_df = pd.read_csv(self.csv_file)
        parsed_data = []
        
        for _, row in raw_df.iterrows():
            try:
                tuple_str = row['0']
                if tuple_str.startswith('(') and tuple_str.endswith(')'):
                    values = tuple_str[1:-1].split(', ')
                    parsed_data.append({
                        'timestamp': int(values[0]),
                        'bid': float(values[1]),
                        'ask': float(values[2]),
                        'mid_price': (float(values[1]) + float(values[2])) / 2,
                        'spread': float(values[2]) - float(values[1])
                    })
            except: continue
        
        self.df = pd.DataFrame(parsed_data)
        self.df['datetime'] = pd.to_datetime(self.df['timestamp'], unit='s')
        self.df = self.df.sort_values('timestamp').reset_index(drop=True)
        print(f"Loaded {len(self.df)} ticks")
        return self.df
    
    def extract_historical_features(self):
        """Extract features using ONLY historical data (no future leakage)"""
        print("Extracting features with NO future data...")
        
        # Basic historical price features
        self.df['price_change'] = self.df['mid_price'].diff()
        self.df['price_change_pct'] = (self.df['price_change'] / self.df['mid_price'].shift(1)) * 100
        self.df['log_return'] = np.log(self.df['mid_price'] / self.df['mid_price'].shift(1))
        
        # Historical volatility (looking backward only)
        for window in [5, 10, 20, 50]:
            self.df[f'volatility_{window}'] = self.df['price_change'].rolling(window=window).std()
            self.df[f'rolling_mean_{window}'] = self.df['mid_price'].rolling(window=window).mean()
            self.df[f'rolling_max_{window}'] = self.df['mid_price'].rolling(window=window).max()
            self.df[f'rolling_min_{window}'] = self.df['mid_price'].rolling(window=window).min()
            
            # Position in recent range
            range_size = self.df[f'rolling_max_{window}'] - self.df[f'rolling_min_{window}']
            self.df[f'price_position_{window}'] = np.where(
                range_size > 0,
                (self.df['mid_price'] - self.df[f'rolling_min_{window}']) / range_size,
                0.5
            )
        
        # Historical momentum (looking backward only)
        for period in [3, 5, 10, 20]:
            self.df[f'momentum_{period}'] = self.df['mid_price'] - self.df['mid_price'].shift(period)
            self.df[f'roc_{period}'] = ((self.df['mid_price'] / self.df['mid_price'].shift(period)) - 1) * 100
        
        # Historical RSI (looking backward only)
        for period in [7, 14, 21]:
            delta = self.df['mid_price'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            self.df[f'rsi_{period}'] = 100 - (100 / (1 + rs))
        
        # Historical spike detection
        self.df['is_spike'] = ((self.df['price_change_pct'] > 0.1) & (self.df['price_change'] > 0)).astype(int)
        self.df['spike_magnitude'] = np.where(self.df['is_spike'] == 1, self.df['price_change'], 0)
        
        # Ticks since last spike (backward looking only)
        self.df['ticks_since_spike'] = 0
        last_spike_idx = -1
        for i in range(len(self.df)):
            if self.df.iloc[i]['is_spike'] == 1:
                last_spike_idx = i
            if last_spike_idx >= 0:
                self.df.iloc[i, self.df.columns.get_loc('ticks_since_spike')] = i - last_spike_idx
            else:
                self.df.iloc[i, self.df.columns.get_loc('ticks_since_spike')] = i
        
        # Historical support/resistance (backward looking only)
        for window in [20, 50]:
            self.df[f'support_{window}'] = self.df['mid_price'].rolling(window=window).min()
            self.df[f'resistance_{window}'] = self.df['mid_price'].rolling(window=window).max()
            self.df[f'distance_to_support_{window}'] = self.df['mid_price'] - self.df[f'support_{window}']
            self.df[f'distance_to_resistance_{window}'] = self.df[f'resistance_{window}'] - self.df['mid_price']
        
        # Moving average crossovers (backward looking)
        for fast, slow in [(5, 20), (10, 50)]:
            ma_fast = self.df['mid_price'].rolling(window=fast).mean()
            ma_slow = self.df['mid_price'].rolling(window=slow).mean()
            self.df[f'ma_cross_{fast}_{slow}'] = (ma_fast > ma_slow).astype(int)
            self.df[f'ma_distance_{fast}_{slow}'] = ma_fast - ma_slow
        
        # Price acceleration
        self.df['acceleration'] = self.df['price_change'].diff()
        
        # Time-based features (these are fine - no future data)
        self.df['hour'] = self.df['datetime'].dt.hour
        self.df['minute'] = self.df['datetime'].dt.minute
        self.df['second'] = self.df['datetime'].dt.second
        
        print(f"Created {len([col for col in self.df.columns if col not in ['timestamp', 'bid', 'ask', 'mid_price', 'datetime', 'spread']])} historical features")
    
    def create_proper_labels(self):
        """Create labels by looking at ACTUAL future outcomes (for training only)"""
        print("Creating proper labels...")
        
        # We can ONLY do this for training data
        # In real-time trading, we won't know future outcomes
        
        # Store current prices for future comparison
        future_prices = []
        for i in range(len(self.df)):
            # Look ahead 50 ticks to see actual outcome
            future_idx = min(i + 50, len(self.df) - 1)
            future_price = self.df.iloc[future_idx]['mid_price']
            current_price = self.df.iloc[i]['mid_price']
            
            # For SELL position: profit if future price is lower
            price_drop = (current_price - future_price) / current_price
            
            # Label as 1 if we can make at least 0.01% profit in next 50 ticks
            profitable = price_drop > 0.0001  # 0.01% minimum profit
            future_prices.append(profitable)
        
        self.df['target'] = future_prices
        
        # For the last 50 ticks, we can't know the future, so mark as invalid
        self.df.iloc[-50:, self.df.columns.get_loc('target')] = np.nan
        
        positive_rate = self.df['target'].mean() * 100
        print(f"Target distribution: {positive_rate:.1f}% profitable opportunities")
        
        return positive_rate > 10  # Only proceed if we have reasonable opportunities
    
    def proper_time_series_split(self, train_pct=0.7):
        """Proper time series split - NO random shuffling"""
        print("Creating proper time-series train/test split...")
        
        # Remove rows with NaN values
        feature_cols = [col for col in self.df.columns if col not in [
            'timestamp', 'bid', 'ask', 'mid_price', 'datetime', 'spread', 'target'
        ]]
        
        complete_data = self.df[feature_cols + ['target']].dropna()
        
        # Time-based split (first 70% for training, last 30% for testing)
        split_idx = int(len(complete_data) * train_pct)
        
        train_data = complete_data.iloc[:split_idx]
        test_data = complete_data.iloc[split_idx:]
        
        X_train = train_data[feature_cols]
        y_train = train_data['target']
        X_test = test_data[feature_cols]
        y_test = test_data['target']
        
        print(f"Training samples: {len(X_train)}")
        print(f"Testing samples: {len(X_test)}")
        print(f"Features: {len(feature_cols)}")
        
        return X_train, X_test, y_train, y_test, feature_cols
    
    def train_proper_model(self):
        """Train model with proper time-series methodology"""
        print("Training model with proper methodology...")
        
        X_train, X_test, y_train, y_test, feature_cols = self.proper_time_series_split()
        
        # Scale features
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # Train XGBoost
        self.model = xgb.XGBClassifier(
            n_estimators=100,      # Fewer trees to prevent overfitting
            max_depth=4,           # Shallower trees
            learning_rate=0.1,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42,
            eval_metric='logloss'
        )
        
        self.model.fit(X_train_scaled, y_train)
        
        # Evaluate on test set
        y_pred = self.model.predict(X_test_scaled)
        y_prob = self.model.predict_proba(X_test_scaled)[:, 1]
        
        accuracy = accuracy_score(y_test, y_pred)
        
        print(f"\n📊 PROPER MODEL PERFORMANCE:")
        print(f"Accuracy: {accuracy:.3f}")
        print(f"\nClassification Report:")
        print(classification_report(y_test, y_pred))
        
        # Feature importance
        feature_importance = pd.DataFrame({
            'feature': feature_cols,
            'importance': self.model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        print(f"\n🔥 TOP 10 FEATURES:")
        for i, (_, row) in enumerate(feature_importance.head(10).iterrows()):
            print(f"{i+1:2d}. {row['feature']:<25} {row['importance']:.4f}")
        
        return accuracy, feature_importance
    
    def realistic_backtest(self):
        """Realistic backtest with proper forward-testing"""
        print("\n💰 REALISTIC BACKTEST (forward-testing only)...")
        
        if self.model is None:
            print("No model trained!")
            return
        
        # Use only the test period for backtesting
        _, X_test, _, y_test, feature_cols = self.proper_time_series_split()
        
        # Get predictions for test period
        X_test_scaled = self.scaler.transform(X_test)
        probabilities = self.model.predict_proba(X_test_scaled)[:, 1]
        
        # Conservative trading: only trade when confidence > 70%
        confidence_threshold = 0.7
        high_conf_signals = probabilities > confidence_threshold
        
        # Simulate realistic trading
        starting_capital = 20
        position_size = 0.2  # Conservative position size (minimum allowed)
        tick_value = 0.001
        current_capital = starting_capital
        trades = []
        
        test_start_idx = int(len(self.df) * 0.7)
        
        for i, signal in enumerate(high_conf_signals):
            if signal and i < len(high_conf_signals) - 50:  # Ensure we have exit data
                actual_idx = test_start_idx + i
                
                # Entry
                entry_price = self.df.iloc[actual_idx]['mid_price']
                entry_time = self.df.iloc[actual_idx]['datetime']
                
                # Exit after 50 ticks
                exit_idx = min(actual_idx + 50, len(self.df) - 1)
                exit_price = self.df.iloc[exit_idx]['mid_price']
                exit_time = self.df.iloc[exit_idx]['datetime']
                
                # P&L for SELL position
                price_change = entry_price - exit_price
                ticks_gained = price_change / 0.001
                pnl = ticks_gained * tick_value * position_size
                
                current_capital += pnl
                
                trades.append({
                    'entry_time': entry_time,
                    'entry_price': entry_price,
                    'exit_price': exit_price,
                    'pnl': pnl,
                    'probability': probabilities[i],
                    'capital': current_capital
                })
        
        if trades:
            trades_df = pd.DataFrame(trades)
            winning_trades = trades_df[trades_df['pnl'] > 0]
            
            print(f"\n📈 REALISTIC BACKTEST RESULTS:")
            print(f"Total trades: {len(trades_df)}")
            print(f"Winning trades: {len(winning_trades)} ({len(winning_trades)/len(trades_df)*100:.1f}%)")
            print(f"Starting capital: ${starting_capital:.2f}")
            print(f"Final capital: ${current_capital:.2f}")
            print(f"Total return: {(current_capital - starting_capital) / starting_capital * 100:.1f}%")
            print(f"Average trade: ${trades_df['pnl'].mean():.4f}")
            print(f"Best trade: ${trades_df['pnl'].max():.4f}")
            print(f"Worst trade: ${trades_df['pnl'].min():.4f}")
            
            return trades_df
        else:
            print("No high-confidence trades generated!")
            return None

def main():
    """Run proper XGBoost analysis"""
    print("🔧 PROPER BOOM 1000 INDEX ANALYSIS - NO LOOK-AHEAD BIAS")
    print("="*60)
    
    csv_file = "C:\\Users\\<USER>\\META\\Boom_1000_Index_7days_20250619_20250626.csv"
    analyzer = ProperBoomAnalyzer(csv_file)
    
    # Load and process data properly
    analyzer.load_and_parse_data()
    analyzer.extract_historical_features()
    
    # Create labels (only for training purposes)
    if analyzer.create_proper_labels():
        # Train with proper methodology
        accuracy, importance = analyzer.train_proper_model()
        
        # Realistic backtest
        analyzer.realistic_backtest()
    else:
        print("Insufficient profitable opportunities in data!")
    
    return analyzer

if __name__ == "__main__":
    analyzer = main()
