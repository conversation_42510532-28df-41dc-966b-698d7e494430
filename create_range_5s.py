import pandas as pd
import numpy as np
from datetime import datetime, timedelta

print("Loading tick data...")
# Read the tick data
df_ticks = pd.read_csv("C:/Users/<USER>/META/stpRNG_90days_ticks.csv")
df_ticks['datetime'] = pd.to_datetime(df_ticks['datetime'])

# Sort by datetime to ensure chronological order
df_ticks = df_ticks.sort_values('datetime').reset_index(drop=True)

def create_5s_bars(df_ticks):
    print("Creating 5-second range bars...")
    # Set datetime as index for resampling
    df_ticks.set_index('datetime', inplace=True)
    
    # Create 5-second OHLC bars
    df_5s = pd.DataFrame()
    df_5s['open'] = df_ticks['price'].resample('5s').first()
    df_5s['high'] = df_ticks['price'].resample('5s').max()
    df_5s['low'] = df_ticks['price'].resample('5s').min()
    df_5s['close'] = df_ticks['price'].resample('5s').last()
    
    # Calculate range size and add other useful columns
    df_5s = df_5s.dropna()  # Remove bars with no trades
    df_5s.reset_index(inplace=True)
    df_5s['range'] = df_5s['high'] - df_5s['low']
    df_5s['body'] = abs(df_5s['close'] - df_5s['open'])
    df_5s['direction'] = np.where(df_5s['close'] >= df_5s['open'], 'up', 'down')
    df_5s['upper_wick'] = df_5s['high'] - np.maximum(df_5s['open'], df_5s['close'])
    df_5s['lower_wick'] = np.minimum(df_5s['open'], df_5s['close']) - df_5s['low']
    
    return df_5s

# Create 5-second range bars
df_range = create_5s_bars(df_ticks)

# Save to CSV
output_file = "stpRNG_range_5s.csv"
print(f"\nSaving to {output_file}...")
df_range.to_csv(output_file, index=False)

print("\nDone! Summary:")
print(f"Total ticks processed: {len(df_ticks)}")
print(f"Total 5s range bars: {len(df_range)}")
print(f"Date range: {df_range['datetime'].min()} to {df_range['datetime'].max()}")
print("\nRange Statistics:")
print(f"Average range size: {df_range['range'].mean():.3f}")
print(f"Max range size: {df_range['range'].max():.3f}")
print(f"Min range size: {df_range['range'].min():.3f}")
print(f"Up bars: {(df_range['direction'] == 'up').sum()}")
print(f"Down bars: {(df_range['direction'] == 'down').sum()}")
print(f"\nFirst few range bars:")
print(df_range.head())
