import numpy as np
import pandas as pd
from typing import List, Dict
from dataclasses import dataclass
from sklearn.preprocessing import StandardScaler
import logging
import math

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class Settings:
    source: str = 'close'
    neighbors_count: int = 8
    max_bars_back: int = 1000
    feature_count: int = 4
    tp_points: float = 10.0  # Take profit in points
    position_size: float = 0.1  # Mini lot size
    point_value: float = 0.10  # $0.10 per point for mini lot

@dataclass
class Trade:
    entry_time: pd.Timestamp
    entry_price: float
    position_size: float
    direction: int  # 1 for long, -1 for short
    tp_price: float
    pnl: float = 0.0
    status: str = 'open'
    exit_time: pd.Timestamp = None
    exit_price: float = None

class LorentzianClassifier:
    def __init__(self, settings: Settings = Settings()):
        self.settings = settings
        self.scaler = StandardScaler()
        self.active_trade = None
        self.closed_trades = []
        
    def normalize_feature(self, feature: np.array) -> np.array:
        """Normalize a feature series to range [0, 1]"""
        return self.scaler.fit_transform(feature.reshape(-1, 1)).flatten()

    def calculate_rsi(self, prices: np.array, period: int = 14) -> np.array:
        """Calculate RSI using numpy"""
        deltas = np.diff(prices)
        seed = deltas[:period+1]
        up = seed[seed >= 0].sum()/period
        down = -seed[seed < 0].sum()/period
        rs = up/down if down != 0 else 0
        rsi = np.zeros_like(prices)
        rsi[period] = 100. - 100./(1.+rs)

        for i in range(period+1, len(prices)):
            delta = deltas[i-1]
            if delta > 0:
                upval = delta
                downval = 0.
            else:
                upval = 0.
                downval = -delta

            up = (up*(period-1) + upval)/period
            down = (down*(period-1) + downval)/period
            rs = up/down if down != 0 else 0
            rsi[i] = 100. - 100./(1.+rs)

        return rsi    def calculate_wt(self, hlc3: np.array, n1: int = 10, n2: int = 21) -> np.array:
        """Calculate Wave Trend using numpy"""
        ema1 = pd.Series(hlc3).ewm(span=n1).mean().values
        ema2 = pd.Series(hlc3).ewm(span=n2).mean().values
        return ema1 - ema2

    def get_lorentzian_distance(self, current_features: List[float], historical_features: List[List[float]]) -> List[float]:
        """Calculate Lorentzian distance between current and historical features"""
        distances = []
        for hist_feature_set in historical_features:
            distance = sum(math.log(1 + abs(curr - hist)) 
                         for curr, hist in zip(current_features, hist_feature_set))
            distances.append(distance)
        return distances

    def predict(self, features: List[float], historical_features: List[List[float]], 
                historical_labels: List[int]) -> int:
        """Make prediction using K-nearest neighbors with Lorentzian distance"""
        if len(historical_features) < self.settings.neighbors_count:
            return 0  # Not enough historical data
            
        distances = self.get_lorentzian_distance(features, historical_features)
        k = min(self.settings.neighbors_count, len(historical_features))
        nearest_indices = np.argsort(distances)[:k]
        nearest_labels = [historical_labels[i] for i in nearest_indices]
        
        if not nearest_labels:
            return 0
            
        return max(set(nearest_labels), key=nearest_labels.count)

    def process_trade(self, row: pd.Series, trade: Trade) -> float:
        """Process active trade and return PnL if closed"""
        if trade is None:
            return 0.0

        pnl = 0.0
        # Check if take profit is hit
        if trade.direction == 1:  # Long trade
            if row['high'] >= trade.tp_price:
                pnl = (trade.tp_price - trade.entry_price) * trade.position_size / self.settings.point_value
                trade.status = 'closed'
                trade.exit_price = trade.tp_price
                trade.exit_time = row.name
                trade.pnl = pnl
        else:  # Short trade
            if row['low'] <= trade.tp_price:
                pnl = (trade.entry_price - trade.tp_price) * trade.position_size / self.settings.point_value
                trade.status = 'closed'
                trade.exit_price = trade.tp_price
                trade.exit_time = row.name
                trade.pnl = pnl

        return pnl

    def backtest(self, df: pd.DataFrame) -> pd.DataFrame:
        """Run backtest with proper position management and P&L calculation"""
        # Calculate features
        df['hlc3'] = (df['high'] + df['low'] + df['close']) / 3
        df['rsi'] = self.normalize_feature(self.calculate_rsi(df['close'].values))
        df['wt'] = self.normalize_feature(self.calculate_wt(df['hlc3'].values))
        df['dir_change'] = (df['direction'] == 'up').astype(float)
        df['dir_change'] = self.normalize_feature(df['dir_change'].values)

        # Initialize results
        features_list = []
        labels = []
        df['pnl'] = 0.0
        df['cumulative_pnl'] = 0.0
        df['position'] = 0
        
        self.active_trade = None
        self.closed_trades = []

        lookback = self.settings.max_bars_back
        
        for i in range(lookback, len(df)):
            current_row = df.iloc[i]
            
            # Process any active trade first
            if self.active_trade is not None:
                pnl = self.process_trade(current_row, self.active_trade)
                df.loc[current_row.name, 'pnl'] = pnl
                if self.active_trade.status == 'closed':
                    self.closed_trades.append(self.active_trade)
                    self.active_trade = None

            # Only generate new signals if we don't have an active trade
            if self.active_trade is None:
                current_features = [
                    df['rsi'].iloc[i],
                    df['wt'].iloc[i],
                    df['dir_change'].iloc[i]
                ]
                
                # Generate label based on direction
                if df['direction'].iloc[i] == 'up':
                    label = 1
                elif df['direction'].iloc[i] == 'down':
                    label = -1
                else:
                    label = 0
                
                features_list.append(current_features)
                labels.append(label)
                
                if i > lookback:
                    prediction = self.predict(
                        current_features,
                        features_list[:-1],
                        labels[:-1]
                    )
                    
                    # Open new trade if we get a signal
                    if prediction != 0:
                        entry_price = current_row['close']
                        tp_price = entry_price + (prediction * self.settings.tp_points)
                        
                        self.active_trade = Trade(
                            entry_time=current_row.name,
                            entry_price=entry_price,
                            position_size=self.settings.position_size,
                            direction=prediction,
                            tp_price=tp_price
                        )
                        
                        df.loc[current_row.name, 'position'] = prediction

            # Update cumulative P&L
            df.loc[current_row.name, 'cumulative_pnl'] = (
                df['pnl'].iloc[:i+1].sum()
            )

        return df

def main():
    # Read the CSV data
    df = pd.read_csv('stpRNG_renko_0_1_extended.csv', parse_dates=['datetime'])
    df.set_index('datetime', inplace=True)
    
    # Initialize classifier with trading settings
    classifier = LorentzianClassifier(
        settings=Settings(
            neighbors_count=8,
            max_bars_back=1000,
            feature_count=4,
            tp_points=10.0,  # 10 point take profit
            position_size=0.1,  # Mini lot
            point_value=0.10  # $0.10 per point
        )
    )
    
    print("Starting backtest...")
    # Run backtest
    results = classifier.backtest(df)
    
    # Calculate and print performance metrics
    total_pnl = results['pnl'].sum()
    win_trades = len([t for t in classifier.closed_trades if t.pnl > 0])
    total_trades = len(classifier.closed_trades)
    win_rate = (win_trades / total_trades * 100) if total_trades > 0 else 0
    
    print("\nBacktest Results:")
    print(f"Total P&L: ${total_pnl:.2f}")
    print(f"Total Trades: {total_trades}")
    print(f"Win Rate: {win_rate:.2f}%")
    print(f"Average P&L per Trade: ${(total_pnl/total_trades if total_trades > 0 else 0):.2f}")
    
    # Save detailed results
    results.to_csv('lorentzian_backtest_results.csv')
    print("\nSaved detailed results to 'lorentzian_backtest_results.csv'")
    
    # Save trade log
    trades_df = pd.DataFrame([
        {
            'entry_time': t.entry_time,
            'exit_time': t.exit_time,
            'entry_price': t.entry_price,
            'exit_price': t.exit_price,
            'direction': t.direction,
            'position_size': t.position_size,
            'pnl': t.pnl
        }
        for t in classifier.closed_trades
    ])
    trades_df.to_csv('trade_log.csv')
    print("Saved trade log to 'trade_log.csv'")

if __name__ == "__main__":
    main()
