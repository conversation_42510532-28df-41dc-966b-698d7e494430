import pandas as pd
import numpy as np

def vectorized_backtest(file_path, min_volume=0.20, max_volume=50, limit_per_direction=200, kelly_fraction=0.45, initial_capital=10):
    """
    Implements a numpy vectorized backtest with fractional Kelly sizing and volume constraints.
    """
    try:
        # Load data
        df = pd.read_csv(file_path)
        close_prices = df['close'].to_numpy()

        # Load tick data
        tick_data_file = "stpRNG_90days_ticks.csv"
        tick_df = pd.read_csv(tick_data_file)

        # Convert datetime to pandas datetime objects
        df['datetime'] = pd.to_datetime(df['datetime'])
        tick_df['datetime'] = pd.to_datetime(tick_df['datetime'])

        # Extract long and short signals (assuming they are already calculated in the dataframe)
        from smart_labelling_range_filter import smart_labelling_range_filter
        df = smart_labelling_range_filter(df, price_col='close')
        long_signals = df['long'].to_numpy()
        short_signals = df['short'].to_numpy()

        # Ensure signals are boolean
        long_signals = long_signals.astype(bool)
        short_signals = short_signals.astype(bool)

        # Convert datetime to pandas datetime objects
        df['datetime'] = pd.to_datetime(df['datetime'])
        tick_df['datetime'] = pd.to_datetime(tick_df['datetime'])

        # Backtesting logic (to be implemented)
        returns = np.zeros_like(close_prices)
        position = 0  # 1 for long, -1 for short, 0 for no position
        entry_price = 0.0

        for i in range(1, len(close_prices)):
            # Get the current 1-minute bar's datetime
            current_datetime = df['datetime'][i]

            if long_signals[i] and position == 0:
                # Find the tick price at the middle of the 1-minute bar
                mid_bar_time = current_datetime + pd.Timedelta(minutes=0.5)
                # Find the first tick price after the middle of the bar
                tick_price = tick_df[tick_df['datetime'] > mid_bar_time]['price'].iloc[0] if not tick_df[tick_df['datetime'] > mid_bar_time].empty else close_prices[i]
                position = 1
                entry_price = tick_price
    
            elif short_signals[i] and position == 0:
                # Find the tick price at the middle of the 1-minute bar
                mid_bar_time = current_datetime + pd.Timedelta(minutes=0.5)
                # Find the first tick price after the middle of the bar
                tick_price = tick_df[tick_df['datetime'] > mid_bar_time]['price'].iloc[0] if not tick_df[tick_df['datetime'] > mid_bar_time].empty else close_prices[i]
                position = -1
                entry_price = tick_price
    
            elif long_signals[i] and position == -1:
                # Close short position and open long position
                mid_bar_time = current_datetime + pd.Timedelta(minutes=0.5)
                # Find the first tick price after the middle of the bar
                tick_price = tick_df[tick_df['datetime'] > mid_bar_time]['price'].iloc[0] if not tick_df[tick_df['datetime'] > mid_bar_time].empty else close_prices[i]
                returns[i] = position * (tick_price - entry_price)
                position = 1
                entry_price = tick_price
            elif short_signals[i] and position == 1:
                # Close long position and open short position
                mid_bar_time = current_datetime + pd.Timedelta(minutes=0.5)
                # Find the first tick price after the middle of the bar
                tick_price = tick_df[tick_df['datetime'] > mid_bar_time]['price'].iloc[0] if not tick_df[tick_df['datetime'] > mid_bar_time].empty else close_prices[i]
                returns[i] = position * (tick_price - entry_price)
                position = -1
                entry_price = tick_price
            elif i == len(close_prices) - 1 and position != 0:
                # Close any open positions at the end of the dataset
                mid_bar_time = current_datetime + pd.Timedelta(minutes=0.5)
                # Find the first tick price after the middle of the bar
                tick_price = tick_df[tick_df['datetime'] > mid_bar_time]['price'].iloc[0] if not tick_df[tick_df['datetime'] > mid_bar_time].empty else close_prices[i]
                returns[i] = position * (tick_price - entry_price)
                position = 0

        # Calculate total returns
        total_returns = np.cumsum(returns)

        # Calculate win rate and average win/loss ratio
        wins = returns[returns > 0]
        losses = returns[returns < 0]
        win_rate = len(wins) / len(returns[returns != 0]) if len(returns[returns != 0]) > 0 else 0
        avg_win = np.mean(wins) if len(wins) > 0 else 0
        avg_loss = np.mean(losses) if len(losses) > 0 else 0

        # Calculate win/loss ratio
        win_loss_ratio = abs(avg_win / avg_loss) if avg_loss != 0 else 0

        # Calculate Kelly fraction
        kelly_fraction = 0.45 * ((win_rate * win_loss_ratio - (1 - win_rate)) / win_loss_ratio) if win_loss_ratio != 0 else 0

        print(f"Win rate: {win_rate:.2f}")
        print(f"Win/loss ratio: {win_loss_ratio:.2f}")
        print(f"Kelly fraction: {kelly_fraction:.2f}")

        # Implement volume constraints and initial capital
        capital = initial_capital
        position_size = np.zeros_like(close_prices, dtype=float)
        total_positions = 0.0

        for i in range(1, len(close_prices)):
            # Calculate raw position size based on Kelly fraction
            raw_position_size = capital * kelly_fraction

            # Apply volume constraints
            position_size[i] = np.clip(raw_position_size, min_volume, max_volume)

            # Limit per direction
            if position > 0:
                position_size[i] = min(position_size[i], limit_per_direction - total_positions)
            elif position < 0:
                position_size[i] = min(position_size[i], limit_per_direction + total_positions)

            # Update capital based on trade outcome
            capital += returns[i] * position_size[i]

            # Update total positions
            total_positions += returns[i] * position_size[i]

        print(f"Final capital: {capital:.2f}")

        # Ensure signals are boolean
        long_signals = long_signals.astype(bool)
        short_signals = short_signals.astype(bool)

        # Calculate returns based on signals
        returns = np.zeros_like(close_prices)
        position = 0  # 1 for long, -1 for short, 0 for no position
        
        for i in range(1, len(close_prices)):
            if long_signals[i] and position == 0:
                position = 1
                entry_price = close_prices[i]
            elif short_signals[i] and position == 0:
                position = -1
                entry_price = close_prices[i]
            elif long_signals[i] and position == -1:
                # Close short position and open long position
                returns[i] = position * (close_prices[i] - entry_price)
                position = 1
                entry_price = close_prices[i]
            elif short_signals[i] and position == 1:
                # Close long position and open short position
                returns[i] = position * (close_prices[i] - entry_price)
                position = -1
                entry_price = close_prices[i]
            elif i == len(close_prices) - 1:
                # Close any open positions at the end of the dataset
                returns[i] = position * (close_prices[i] - entry_price)
                position = 0
            
            if position != 0:
                returns[i] = position * (close_prices[i] - entry_price)

        # Calculate total returns
        total_returns = np.cumsum(returns)

        # Calculate win rate and average win/loss ratio
        wins = returns[returns > 0]
        losses = returns[returns < 0]
        win_rate = len(wins) / len(returns[returns != 0]) if len(returns[returns != 0]) > 0 else 0
        avg_win = np.mean(wins) if len(wins) > 0 else 0
        avg_loss = np.mean(losses) if len(losses) > 0 else 0
        
        # Calculate win/loss ratio
        win_loss_ratio = abs(avg_win / avg_loss) if avg_loss != 0 else 0

        # Calculate Kelly fraction
        kelly_fraction = 0.45 * ((win_rate * win_loss_ratio - (1 - win_rate)) / win_loss_ratio) if win_loss_ratio != 0 else 0

        print(f"Win rate: {win_rate:.2f}")
        print(f"Win/loss ratio: {win_loss_ratio:.2f}")
        print(f"Kelly fraction: {kelly_fraction:.2f}")

        # Implement volume constraints and initial capital
        capital = initial_capital
        position_size = np.zeros_like(close_prices, dtype=float)
        total_positions = 0.0

        for i in range(1, len(close_prices)):
            # Calculate raw position size based on Kelly fraction
            raw_position_size = capital * kelly_fraction

            # Apply volume constraints
            position_size[i] = np.clip(raw_position_size, min_volume, max_volume)

            # Limit per direction
            if position > 0:
                position_size[i] = min(position_size[i], limit_per_direction - total_positions)
            elif position < 0:
                position_size[i] = min(position_size[i], limit_per_direction + total_positions)

            # Update capital based on trade outcome
            capital += returns[i] * position_size[i]

            # Update total positions
            total_positions += returns[i] * position_size[i]

        print(f"Final capital: {capital:.2f}")

        # Calculate Maximum Drawdown
        capital_values = np.zeros_like(close_prices, dtype=float)
        capital_values[0] = initial_capital
        for i in range(1, len(close_prices)):
            capital_values[i] = capital_values[i-1] + returns[i] * position_size[i]

        peak = np.maximum.accumulate(capital_values)
        drawdown = (capital_values - peak) / peak
        max_drawdown = np.min(drawdown)

        # Calculate Sharpe Ratio (assuming risk-free rate is 0)
        sharpe_ratio = np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0

        print(f"Maximum Drawdown: {max_drawdown:.2f}")
        print(f"Sharpe Ratio: {sharpe_ratio:.2f}")

        print("Backtest completed successfully.")

    except FileNotFoundError:
        print(f"Error: File not found at {file_path}")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    file_path = "stpRNG_1min.csv"
    vectorized_backtest(file_path, min_volume=0.20, max_volume=50, limit_per_direction=200, kelly_fraction=0.45, initial_capital=10)