import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

def calculate_ema(df, column, period):
    return df[column].ewm(span=period, adjust=False).mean()

def resample_data(df, timeframe):
    df['datetime'] = pd.to_datetime(df['datetime'])
    df = df.set_index('datetime')
    ohlc = df['price'].resample(timeframe.lower()).ohlc()
    return ohlc.dropna()

def is_peak_formation_high(df, index, window=3):
    # Check if the current high is a local peak (higher than surrounding bars)
    if index < window or index >= len(df) - window:
        return False
    
    current_high = df['high'].iloc[index]
    
    # Check if current_high is the highest in the window
    if current_high == df['high'].iloc[index-window : index+window+1].max():
        return True
            
    return False

def is_peak_formation_low(df, index, window=3):
    # Check if the current low is a local trough (lower than surrounding bars)
    if index < window or index >= len(df) - window:
        return False
    
    current_low = df['low'].iloc[index]
    
    # Check if current_low is the lowest in the window
    if current_low == df['low'].iloc[index-window : index+window+1].min():
        return True
            
    return False

def is_ema_bounce(df, index, ema_column, tolerance=0.001, min_move=0.0005):
    # EMA bounce: price approaches EMA and then moves away from it
    # Tolerance: how close price needs to be to EMA
    # Min_move: minimum movement away from EMA to qualify as a bounce
    
    if index < 1: # Need at least one previous bar for comparison
        return None

    current_close = df['close'].iloc[index]
    previous_close = df['close'].iloc[index - 1]
    current_ema = df[ema_column].iloc[index]
    previous_ema = df[ema_column].iloc[index - 1]

    # Check if price was near EMA
    price_near_ema = abs(previous_close - previous_ema) / previous_ema < tolerance

    if price_near_ema:
        # Bullish bounce: price was near EMA and moved above it significantly
        if current_close > current_ema and (current_close - previous_close) / previous_close > min_move:
            return 'buy'
        # Bearish bounce: price was near EMA and moved below it significantly
        elif current_close < current_ema and (previous_close - current_close) / previous_close > min_move:
            return 'sell'
            
    return None

def market_reset_strategy(file_path):
    df_ticks = pd.read_csv(file_path)

    # Resample to M15 and H1
    df_m15 = resample_data(df_ticks, '15min')
    df_h1 = resample_data(df_ticks, '1H')

    # Calculate EMAs for M15
    df_m15['ema5'] = calculate_ema(df_m15, 'close', 5)
    df_m15['ema13'] = calculate_ema(df_m15, 'close', 13)
    df_m15['ema50'] = calculate_ema(df_m15, 'close', 50)
    df_m15['ema200'] = calculate_ema(df_m15, 'close', 200)
    df_m15['ema800'] = calculate_ema(df_m15, 'close', 800)

    # Calculate EMAs for H1
    df_h1['ema5'] = calculate_ema(df_h1, 'close', 5)
    df_h1['ema13'] = calculate_ema(df_h1, 'close', 13)
    df_h1['ema50'] = calculate_ema(df_h1, 'close', 50)
    df_h1['ema200'] = calculate_ema(df_h1, 'close', 200)
    df_h1['ema800'] = calculate_ema(df_h1, 'close', 800)

    # --- Strategy Logic ---
    trades = []
    in_position = False
    position_type = None # 'buy' or 'sell'
    entry_price = 0
    
    for i in range(len(df_m15)):
        m15_row = df_m15.iloc[i]
        
        # Find corresponding H1 row
        h1_index_for_m15 = df_h1.index.get_indexer([m15_row.name], method='nearest')[0]
        h1_row = df_h1.iloc[h1_index_for_m15]

        # Simplified Peak Detection
        is_pfh_m15 = is_peak_formation_high(df_m15, i)
        is_pfl_m15 = is_peak_formation_low(df_m15, i)
        # if is_pfh_m15: print(f"PFH detected at {m15_row.name}")
        # if is_pfl_m15: print(f"PFL detected at {m15_row.name}")

        # Simplified EMA Bounces
        ema200_bounce_m15 = is_ema_bounce(df_m15, i, 'ema200')
        ema800_bounce_m15 = is_ema_bounce(df_m15, i, 'ema800')
        ema50_bounce_h1 = is_ema_bounce(df_h1, h1_index_for_m15, 'ema50')
        # if ema200_bounce_m15: print(f"M15 EMA200 bounce ({ema200_bounce_m15}) at {m15_row.name}")
        # if ema800_bounce_m15: print(f"M15 EMA800 bounce ({ema800_bounce_m15}) at {m15_row.name}")
        # if ema50_bounce_h1: print(f"H1 EMA50 bounce ({ema50_bounce_h1}) at {m15_row.name}")

        # Check for "Reset" Condition (simplified: peak near EMA200/800 + EMA bounce)
        reset_buy_condition = False
        reset_sell_condition = False

        if is_pfl_m15 and (ema200_bounce_m15 == 'buy' or ema800_bounce_m15 == 'buy'):
            # Inter-timeframe confirmation: H1 50 EMA bounce equivalent to M15 200 EMA bounce
            if ema50_bounce_h1 == 'buy': # This is a very simplified check for equivalency
                reset_buy_condition = True
                # print(f"Reset BUY condition met at {m15_row.name}")
        
        if is_pfh_m15 and (ema200_bounce_m15 == 'sell' or ema800_bounce_m15 == 'sell'):
            # Inter-timeframe confirmation
            if ema50_bounce_h1 == 'sell': # Very simplified check
                reset_sell_condition = True
                # print(f"Reset SELL condition met at {m15_row.name}")

        # Entry Logic
        if not in_position:
            if reset_buy_condition and m15_row['close'] > m15_row['ema13']:
                in_position = True
                position_type = 'buy'
                entry_price = m15_row['close']
                trades.append({'time': m15_row.name, 'type': 'buy', 'entry_price': entry_price})
                # print(f"BUY signal at {m15_row.name}, Price: {entry_price}")
            elif reset_sell_condition and m15_row['close'] < m15_row['ema13']:
                in_position = True
                position_type = 'sell'
                entry_price = m15_row['close']
                trades.append({'time': m15_row.name, 'type': 'sell', 'entry_price': entry_price})
                # print(f"SELL signal at {m15_row.name}, Price: {entry_price}")
        else:
            # Exit Logic (simplified: reverse EMA13 crossover or fixed stop/take profit)
            # For now, let's just exit on reverse EMA13 crossover
            if position_type == 'buy' and m15_row['close'] < m15_row['ema13']:
                profit = m15_row['close'] - entry_price
                trades[-1]['exit_price'] = m15_row['close']
                trades[-1]['profit'] = profit
                in_position = False
                position_type = None
                # print(f"EXIT BUY at {m15_row.name}, Price: {m15_row['close']}, Profit: {profit}")
            elif position_type == 'sell' and m15_row['close'] > m15_row['ema13']:
                profit = entry_price - m15_row['close']
                trades[-1]['exit_price'] = m15_row['close']
                trades[-1]['profit'] = profit
                in_position = False
                position_type = None
                # print(f"EXIT SELL at {m15_row.name}, Price: {m15_row['close']}, Profit: {profit}")
    
    # Convert trades to DataFrame for analysis
    trades_df = pd.DataFrame(trades)
    if 'profit' in trades_df.columns:
        total_profit = trades_df['profit'].sum()
        num_trades = len(trades_df)
        winning_trades = trades_df[trades_df['profit'] > 0]
        num_winning_trades = len(winning_trades)
        win_rate = num_winning_trades / num_trades if num_trades > 0 else 0

        print(f"Total Trades: {num_trades}")
        print(f"Winning Trades: {num_winning_trades}")
        print(f"Win Rate: {win_rate:.2%}")
        print(f"Total Profit: {total_profit:.2f}")
    else:
        print("No completed trades to analyze.")

    return trades_df

if __name__ == "__main__":
    csv_file_path = r"C:\Users\<USER>\META\stpRNG_90days_ticks.csv"
    trade_results = market_reset_strategy(csv_file_path)
    print("\n--- Trade Results ---")
    print(trade_results.head())
    print("\n--- Trade Summary ---")
    print(trade_results.describe())
