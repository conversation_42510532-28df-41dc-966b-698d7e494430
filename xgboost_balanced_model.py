import pandas as pd
import numpy as np
import os
from sklearn.model_selection import train_test_split, TimeSeriesSplit
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score, roc_auc_score, precision_recall_curve
from imblearn.over_sampling import SMOTE
from imblearn.under_sampling import RandomUnderSampler
from imblearn.combine import SMOTETomek
import xgboost as xgb
import pickle
from datetime import datetime
import matplotlib.pyplot as plt

# Load OHLC data
DATA_PATH = "stpRNG_1min.csv"
if not os.path.isfile(DATA_PATH):
    raise FileNotFoundError(f"File not found: {DATA_PATH}")

df = pd.read_csv(DATA_PATH)
df = df.dropna().reset_index(drop=True)

print(f"Loaded {len(df)} rows of data")

# Parameters - matching Pine Script defaults
p = 200  # Trend period
atr_p = 3  # ATR Period  
mult = 1.6  # ATR Multiplier
mode = "Type A"  # Signal mode
use_ema_smoother = "No"  # Smooth source with EMA
src_ema_period = 3  # EMA Smoother period

# Source selection
if use_ema_smoother == "Yes":
    df["src"] = df["close"].ewm(span=src_ema_period, adjust=False).mean()
else:
    df["src"] = df["close"]

# Calculate ATR manually
def calculate_atr(df, period):
    df["tr1"] = df["high"] - df["low"]
    df["tr2"] = abs(df["high"] - df["close"].shift(1))
    df["tr3"] = abs(df["low"] - df["close"].shift(1))
    df["tr"] = df[["tr1", "tr2", "tr3"]].max(axis=1)
    df["atr"] = df["tr"].rolling(window=period).mean()
    return df["atr"]

# Q-Trend Calculations
df["h"] = df["src"].rolling(window=p).max()  # Highest of src p-bars back
df["l"] = df["src"].rolling(window=p).min()  # Lowest of src p-bars back
df["d"] = df["h"] - df["l"]
df["m"] = (df["h"] + df["l"]) / 2  # Initial trend line

df["atr"] = calculate_atr(df, atr_p).shift(1)  # ATR shifted by 1
df["epsilon"] = mult * df["atr"]

# Initialize columns
df["trend"] = np.nan
df["last_signal"] = ""
df["signal"] = ""
df["change_up"] = False
df["change_down"] = False

# Process each bar for Q-Trend signals
for i in range(len(df)):
    if i < p:
        df.at[i, "trend"] = df.at[i, "m"] if not pd.isna(df.at[i, "m"]) else np.nan
        df.at[i, "last_signal"] = ""
        continue
    
    src = df.at[i, "src"]
    prev_trend = df.at[i-1, "trend"] if i > 0 and not pd.isna(df.at[i-1, "trend"]) else df.at[i, "m"]
    epsilon = df.at[i, "epsilon"]
    
    if pd.isna(epsilon):
        df.at[i, "trend"] = prev_trend
        df.at[i, "last_signal"] = df.at[i-1, "last_signal"] if i > 0 else ""
        continue
    
    # Type A mode logic (crossover/crossunder)
    change_up = src > prev_trend + epsilon
    change_down = src < prev_trend - epsilon
    
    df.at[i, "change_up"] = change_up
    df.at[i, "change_down"] = change_down
    
    # Strong signals logic
    h = df.at[i, "h"]
    l = df.at[i, "l"]
    d = df.at[i, "d"]
    
    # Check current and previous 4 bars for strong signals
    sb = False
    ss = False
    for j in range(max(0, i-4), i+1):
        if j < len(df):
            open_price = df.at[j, "open"]
            if open_price < l + d / 8 and open_price >= l:
                sb = True
            if open_price > h - d / 8 and open_price <= h:
                ss = True
    
    strong_buy = sb
    strong_sell = ss
    
    # Update trend line
    if change_up or change_down:
        if change_up:
            new_trend = prev_trend + epsilon
        elif change_down:
            new_trend = prev_trend - epsilon
        else:
            new_trend = prev_trend
    else:
        new_trend = prev_trend
    
    df.at[i, "trend"] = new_trend
    
    # Signal logic
    prev_last_signal = df.at[i-1, "last_signal"] if i > 0 else ""
    
    if change_up and prev_last_signal != "B":
        if strong_buy:
            signal = "strong_buy"
        else:
            signal = "buy"
        df.at[i, "last_signal"] = "B"
    elif change_down and prev_last_signal != "S":
        if strong_sell:
            signal = "strong_sell"
        else:
            signal = "sell"
        df.at[i, "last_signal"] = "S"
    else:
        signal = ""
        df.at[i, "last_signal"] = prev_last_signal
    
    df.at[i, "signal"] = signal

# Create additional technical features
def add_technical_features(df):
    # Price-based features
    df['price_vs_trend'] = (df['close'] - df['trend']) / df['trend']
    df['price_vs_high'] = (df['close'] - df['h']) / df['h']
    df['price_vs_low'] = (df['close'] - df['l']) / df['l']
    df['price_position'] = (df['close'] - df['l']) / (df['h'] - df['l'])  # Position in range
    
    # Volatility features
    df['atr_normalized'] = df['atr'] / df['close']
    df['epsilon_normalized'] = df['epsilon'] / df['close']
    df['range_normalized'] = (df['high'] - df['low']) / df['close']
    
    # Momentum features
    for period in [5, 10, 20]:
        df[f'return_{period}'] = df['close'].pct_change(period)
        df[f'volatility_{period}'] = df['close'].rolling(period).std() / df['close']
    
    # Moving averages
    for period in [10, 20, 50]:
        df[f'ma_{period}'] = df['close'].rolling(period).mean()
        df[f'price_vs_ma_{period}'] = (df['close'] - df[f'ma_{period}']) / df[f'ma_{period}']
    
    # RSI-like feature
    delta = df['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    
    # Volume features (if available)
    if 'volume' in df.columns:
        df['volume_ma'] = df['volume'].rolling(20).mean()
        df['volume_ratio'] = df['volume'] / df['volume_ma']
    
    return df

df = add_technical_features(df)

# STRATEGY 1: Use Q-Trend signals as labels (more balanced)
def create_qtrend_labels(df):
    """Use Q-Trend signals as ground truth"""
    df['qtrend_label'] = 0  # Neutral
    df.loc[df['signal'].isin(['buy', 'strong_buy']), 'qtrend_label'] = 1  # Buy signal
    df.loc[df['signal'].isin(['sell', 'strong_sell']), 'qtrend_label'] = -1  # Sell signal
    
    # Binary version: 1 for any signal, 0 for no signal
    df['qtrend_binary'] = (df['signal'] != "").astype(int)
    
    return df

# STRATEGY 2: Different threshold for future returns
def create_improved_labels(df, lookahead=5, threshold=0.0005):
    """Create labels with smaller threshold and shorter lookahead"""
    df['future_return'] = df['close'].shift(-lookahead) / df['close'] - 1
    
    # Smaller threshold for more balanced classes
    df['label_improved'] = (df['future_return'] > threshold).astype(int)
    
    # Ternary classification with smaller thresholds
    df['label_ternary'] = 0  # Neutral
    df.loc[df['future_return'] > threshold, 'label_ternary'] = 1  # Bullish
    df.loc[df['future_return'] < -threshold, 'label_ternary'] = -1  # Bearish
    
    return df

df = create_qtrend_labels(df)
df = create_improved_labels(df, lookahead=5, threshold=0.0005)

# Select features for model
feature_columns = [
    # Q-Trend features
    'price_vs_trend', 'price_vs_high', 'price_vs_low', 'price_position',
    'atr_normalized', 'epsilon_normalized', 'range_normalized',
    
    # Momentum features
    'return_5', 'return_10', 'return_20',
    'volatility_5', 'volatility_10', 'volatility_20',
    
    # Moving average features
    'price_vs_ma_10', 'price_vs_ma_20', 'price_vs_ma_50',
    
    # Technical indicators
    'rsi',
    
    # Q-Trend signals (encoded)
    'change_up', 'change_down'
]

# Add volume features if available
if 'volume' in df.columns:
    feature_columns.extend(['volume_ratio'])

# Prepare data for ML
valid_idx = df.dropna(subset=feature_columns + ['qtrend_binary', 'label_improved']).index
df_clean = df.loc[valid_idx].copy()

print(f"Clean data: {len(df_clean)} rows after removing NaN values")

X = df_clean[feature_columns]

# Compare different labeling strategies
print(f"\n=== LABELING STRATEGY COMPARISON ===")

# Strategy 1: Q-Trend signals as labels
y_qtrend = df_clean['qtrend_binary']
print(f"Q-Trend binary label distribution:")
print(y_qtrend.value_counts(normalize=True))

# Strategy 2: Improved future returns
y_improved = df_clean['label_improved']
print(f"\nImproved label distribution (threshold=0.05%):")
print(y_improved.value_counts(normalize=True))

# Time series split
tscv = TimeSeriesSplit(n_splits=5, test_size=int(len(X) * 0.2))
train_idx, test_idx = list(tscv.split(X))[-1]

def train_and_evaluate_model(X, y, label_name, use_balancing=True):
    """Train and evaluate XGBoost model with different balancing techniques"""
    print(f"\n=== {label_name} ===")
    
    X_train, X_test = X.iloc[train_idx], X.iloc[test_idx]
    y_train, y_test = y.iloc[train_idx], y.iloc[test_idx]
    
    results = {}
    
    # Method 1: Class weights
    print(f"\n--- Method 1: Class Weights ---")
    scale_pos_weight = (y_train == 0).sum() / (y_train == 1).sum()
    
    xgb_weighted = xgb.XGBClassifier(
        n_estimators=100,
        max_depth=6,
        learning_rate=0.1,
        subsample=0.8,
        colsample_bytree=0.8,
        scale_pos_weight=scale_pos_weight,
        random_state=42,
        eval_metric='logloss'
    )
    
    xgb_weighted.fit(X_train, y_train)
    y_pred_weighted = xgb_weighted.predict(X_test)
    y_pred_proba_weighted = xgb_weighted.predict_proba(X_test)[:, 1]
    
    print(f"Accuracy: {accuracy_score(y_test, y_pred_weighted):.4f}")
    print(f"AUC-ROC: {roc_auc_score(y_test, y_pred_proba_weighted):.4f}")
    print(classification_report(y_test, y_pred_weighted))
    
    results['weighted'] = {
        'model': xgb_weighted,
        'predictions': y_pred_weighted,
        'probabilities': y_pred_proba_weighted,
        'accuracy': accuracy_score(y_test, y_pred_weighted),
        'auc': roc_auc_score(y_test, y_pred_proba_weighted)
    }
    
    if use_balancing:
        # Method 2: SMOTE
        print(f"\n--- Method 2: SMOTE ---")
        try:
            smote = SMOTE(random_state=42)
            X_train_smote, y_train_smote = smote.fit_resample(X_train, y_train)
            
            xgb_smote = xgb.XGBClassifier(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                eval_metric='logloss'
            )
            
            xgb_smote.fit(X_train_smote, y_train_smote)
            y_pred_smote = xgb_smote.predict(X_test)
            y_pred_proba_smote = xgb_smote.predict_proba(X_test)[:, 1]
            
            print(f"Accuracy: {accuracy_score(y_test, y_pred_smote):.4f}")
            print(f"AUC-ROC: {roc_auc_score(y_test, y_pred_proba_smote):.4f}")
            print(classification_report(y_test, y_pred_smote))
            
            results['smote'] = {
                'model': xgb_smote,
                'predictions': y_pred_smote,
                'probabilities': y_pred_proba_smote,
                'accuracy': accuracy_score(y_test, y_pred_smote),
                'auc': roc_auc_score(y_test, y_pred_proba_smote)
            }
        except Exception as e:
            print(f"SMOTE failed: {e}")
        
        # Method 3: Combined SMOTE + Tomek
        print(f"\n--- Method 3: SMOTETomek ---")
        try:
            smote_tomek = SMOTETomek(random_state=42)
            X_train_combined, y_train_combined = smote_tomek.fit_resample(X_train, y_train)
            
            xgb_combined = xgb.XGBClassifier(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                eval_metric='logloss'
            )
            
            xgb_combined.fit(X_train_combined, y_train_combined)
            y_pred_combined = xgb_combined.predict(X_test)
            y_pred_proba_combined = xgb_combined.predict_proba(X_test)[:, 1]
            
            print(f"Accuracy: {accuracy_score(y_test, y_pred_combined):.4f}")
            print(f"AUC-ROC: {roc_auc_score(y_test, y_pred_proba_combined):.4f}")
            print(classification_report(y_test, y_pred_combined))
            
            results['combined'] = {
                'model': xgb_combined,
                'predictions': y_pred_combined,
                'probabilities': y_pred_proba_combined,
                'accuracy': accuracy_score(y_test, y_pred_combined),
                'auc': roc_auc_score(y_test, y_pred_proba_combined)
            }
        except Exception as e:
            print(f"SMOTETomek failed: {e}")
    
    return results, y_test

# Test Q-Trend signals as labels
qtrend_results, y_test_qtrend = train_and_evaluate_model(X, y_qtrend, "Q-TREND SIGNALS AS LABELS")

# Test improved future returns
improved_results, y_test_improved = train_and_evaluate_model(X, y_improved, "IMPROVED FUTURE RETURNS")

# Find best performing model
print(f"\n=== BEST MODEL SELECTION ===")

all_results = {
    'qtrend_weighted': qtrend_results.get('weighted', {}),
    'qtrend_smote': qtrend_results.get('smote', {}),
    'qtrend_combined': qtrend_results.get('combined', {}),
    'improved_weighted': improved_results.get('weighted', {}),
    'improved_smote': improved_results.get('smote', {}),
    'improved_combined': improved_results.get('combined', {})
}

best_model_name = ""
best_auc = 0

for name, result in all_results.items():
    if result and 'auc' in result:
        print(f"{name}: AUC = {result['auc']:.4f}, Accuracy = {result['accuracy']:.4f}")
        if result['auc'] > best_auc:
            best_auc = result['auc']
            best_model_name = name

print(f"\nBest model: {best_model_name} with AUC = {best_auc:.4f}")

# Save best model
if best_model_name and all_results[best_model_name]:
    best_model = all_results[best_model_name]['model']
    model_filename = f'xgboost_best_model_{best_model_name}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.pkl'
    with open(model_filename, 'wb') as f:
        pickle.dump(best_model, f)
    print(f"Best model saved as: {model_filename}")

print(f"\n=== SUMMARY ===")
print(f"Successfully addressed class imbalance using:")
print(f"1. Q-Trend signals as labels (more balanced)")
print(f"2. Class weights in XGBoost")
print(f"3. SMOTE oversampling")
print(f"4. SMOTETomek combined approach")
print(f"5. Smaller threshold for future returns")
