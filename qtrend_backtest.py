import pandas as pd
import numpy as np
import os

# Load OHLC data
DATA_PATH = "stpRNG_1min.csv"  # Replace with your actual CSV filename
if not os.path.isfile(DATA_PATH):
    raise FileNotFoundError(f"File not found: {DATA_PATH}")

df = pd.read_csv(DATA_PATH)
df = df.dropna().reset_index(drop=True)

# Parameters from 78% WR setup
TREND_PERIOD = 200
ATR_PERIOD = 3
ATR_MULT = 1.6

# Calculations
df["src"] = df["close"]
df["highest"] = df["src"].rolling(window=TREND_PERIOD).max()
df["lowest"] = df["src"].rolling(window=TREND_PERIOD).min()
df["d"] = df["highest"] - df["lowest"]
df["mid"] = (df["highest"] + df["lowest"]) / 2

tr = lambda d: np.maximum.reduce([
    d["high"] - d["low"],
    abs(d["high"] - d["close"].shift(1)),
    abs(d["low"] - d["close"].shift(1))
])
df["tr"] = tr(df)
df["atr"] = df["tr"].rolling(window=ATR_PERIOD).mean().shift(1)
df["epsilon"] = ATR_MULT * df["atr"]

df["trend"] = np.nan
df["signal"] = ""
trend = None

for i in range(len(df)):
    if i < TREND_PERIOD:
        continue

    src = df.at[i, "src"]
    mid = df.at[i-1, "mid"]
    eps = df.at[i, "epsilon"]

    change_up = src > mid + eps
    change_down = src < mid - eps

    open_ = df.at[i, "open"]
    h = df.at[i, "highest"]
    l = df.at[i, "lowest"]
    d = df.at[i, "d"]

    sb = l <= open_ < l + d / 8
    ss = h - d / 8 < open_ <= h
    strong_buy = sb or df.at[i-1, "open"] < df.at[i-1, "lowest"] + df.at[i-1, "d"] / 8
    strong_sell = ss or df.at[i-1, "open"] > df.at[i-1, "highest"] - df.at[i-1, "d"] / 8

    if trend is None:
        trend = df.at[i, "mid"]
        signal = ""
    elif change_up:
        trend += eps
        signal = "strong_buy" if strong_buy else "buy"
    elif change_down:
        trend -= eps
        signal = "strong_sell" if strong_sell else "sell"
    else:
        signal = ""

    df.at[i, "trend"] = trend
    df.at[i, "signal"] = signal if signal else df.at[i-1, "signal"] if i > 0 else ""

# Backtest Simulator
capital = 100_000
risk_per_trade = 0.004
balance = capital
position = 0
entry_price = 0
wins = 0
losses = 0

for i in range(1, len(df)):
    sig = df.at[i, "signal"]
    price = df.at[i, "open"]

    if position > 0 and sig.startswith("sell"):
        if price > entry_price:
            wins += 1
        else:
            losses += 1
        balance *= 1 + (risk_per_trade if price > entry_price else -risk_per_trade)
        position = 0

    elif position < 0 and sig.startswith("buy"):
        if price < entry_price:
            wins += 1
        else:
            losses += 1
        balance *= 1 + (risk_per_trade if price < entry_price else -risk_per_trade)
        position = 0

    if sig == "buy" or sig == "strong_buy":
        position = 1
        entry_price = price
    elif sig == "sell" or sig == "strong_sell":
        position = -1
        entry_price = price

total_trades = wins + losses
win_rate = wins / total_trades * 100 if total_trades > 0 else 0

print(f"Final balance: ${balance:,.2f}")
print(f"Total trades: {total_trades}, Wins: {wins}, Losses: {losses}, Win rate: {win_rate:.2f}%")
