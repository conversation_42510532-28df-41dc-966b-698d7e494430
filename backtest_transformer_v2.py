import os
import time
import torch
import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error
from torch.utils.data import DataLoader
import matplotlib.pyplot as plt
from transformer_trading import TransformerRegressor, TimeSeriesDataset, load_tick_data, resample_ohlc, add_indicators, scale_features
import argparse

class ImprovedTradingStrategy:
    """Improved trading strategy with realistic parameters"""
    
    def __init__(self, threshold=0.0005, stop_loss=0.01, take_profit=0.015, max_hold_periods=10):
        self.threshold = threshold  # Lower threshold for more signals
        self.stop_loss = stop_loss  # 1% stop loss
        self.take_profit = take_profit  # 1.5% take profit
        self.max_hold_periods = max_hold_periods  # Max periods to hold position
        
    def generate_signals(self, current_prices, predicted_prices):
        """Generate buy/sell signals based on predictions"""
        signals = []
        
        for i in range(len(predicted_prices)):
            if i == 0:
                signals.append(0)  # No signal for first prediction
                continue
                
            current_price = current_prices[i]
            predicted_price = predicted_prices[i]
            price_change = (predicted_price - current_price) / current_price
            
            if price_change > self.threshold:
                signals.append(1)  # Buy signal
            elif price_change < -self.threshold:
                signals.append(-1)  # Sell signal
            else:
                signals.append(0)  # Hold
                
        return signals

class ImprovedBacktestEngine:
    """Improved backtesting engine with forced position management"""
    
    def __init__(self, initial_capital=10000, commission=0.001, leverage=1):
        self.initial_capital = initial_capital
        self.commission = commission
        self.leverage = leverage
        
    def backtest_strategy(self, prices, signals, strategy):
        """Run backtest with improved position management"""
        capital = self.initial_capital
        position = 0  # 0: no position, 1: long, -1: short
        entry_price = 0
        entry_index = 0
        
        trades = []
        equity_curve = [capital]
        daily_returns = []
        
        for i in range(1, len(signals)):
            current_price = prices[i]
            signal = signals[i]
            
            # Check if we should close existing position
            if position != 0:
                periods_held = i - entry_index
                pnl_pct = 0
                should_close = False
                close_reason = ""
                
                if position == 1:  # Long position
                    pnl_pct = (current_price - entry_price) / entry_price
                    if pnl_pct <= -strategy.stop_loss:
                        should_close = True
                        close_reason = "Stop Loss"
                    elif pnl_pct >= strategy.take_profit:
                        should_close = True
                        close_reason = "Take Profit"
                    elif periods_held >= strategy.max_hold_periods:
                        should_close = True
                        close_reason = "Max Hold Time"
                        
                elif position == -1:  # Short position
                    pnl_pct = (entry_price - current_price) / entry_price
                    if pnl_pct <= -strategy.stop_loss:
                        should_close = True
                        close_reason = "Stop Loss"
                    elif pnl_pct >= strategy.take_profit:
                        should_close = True
                        close_reason = "Take Profit"
                    elif periods_held >= strategy.max_hold_periods:
                        should_close = True
                        close_reason = "Max Hold Time"
                
                if should_close:
                    # Close position
                    trade_size = capital * self.leverage
                    pnl = trade_size * pnl_pct - (trade_size * self.commission * 2)  # Entry + exit commission
                    capital += pnl
                    
                    trades.append({
                        'entry_index': entry_index,
                        'exit_index': i,
                        'entry_price': entry_price,
                        'exit_price': current_price,
                        'position': position,
                        'pnl': pnl,
                        'pnl_pct': pnl_pct,
                        'periods_held': periods_held,
                        'close_reason': close_reason
                    })
                    position = 0
            
            # Open new position based on signal (only if no current position)
            if position == 0 and signal != 0:
                position = signal
                entry_price = current_price
                entry_index = i
                
            equity_curve.append(capital)
            
            # Calculate daily return
            daily_return = (capital - equity_curve[-2]) / equity_curve[-2] if len(equity_curve) > 1 else 0
            daily_returns.append(daily_return)
            
        # Close any remaining open position at the end
        if position != 0:
            current_price = prices[-1]
            periods_held = len(prices) - 1 - entry_index
            
            if position == 1:
                pnl_pct = (current_price - entry_price) / entry_price
            else:
                pnl_pct = (entry_price - current_price) / entry_price
                
            trade_size = capital * self.leverage
            pnl = trade_size * pnl_pct - (trade_size * self.commission * 2)
            capital += pnl
            
            trades.append({
                'entry_index': entry_index,
                'exit_index': len(prices) - 1,
                'entry_price': entry_price,
                'exit_price': current_price,
                'position': position,
                'pnl': pnl,
                'pnl_pct': pnl_pct,
                'periods_held': periods_held,
                'close_reason': 'End of Data'
            })
            
            equity_curve[-1] = capital
            
        return trades, equity_curve, daily_returns
        
    def calculate_metrics(self, trades, equity_curve, daily_returns):
        """Calculate comprehensive performance metrics"""
        if not trades:
            return {}
            
        # Basic metrics
        total_trades = len(trades)
        winning_trades = sum(1 for t in trades if t['pnl'] > 0)
        losing_trades = total_trades - winning_trades
        
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        total_pnl = sum(t['pnl'] for t in trades)
        avg_win = np.mean([t['pnl'] for t in trades if t['pnl'] > 0]) if winning_trades > 0 else 0
        avg_loss = np.mean([t['pnl'] for t in trades if t['pnl'] < 0]) if losing_trades > 0 else 0
        
        # Risk metrics
        if len(daily_returns) > 1:
            returns_array = np.array(daily_returns)
            sharpe_ratio = np.mean(returns_array) / np.std(returns_array) * np.sqrt(252) if np.std(returns_array) > 0 else 0
        else:
            sharpe_ratio = 0
        
        # Drawdown
        peak = np.maximum.accumulate(equity_curve)
        drawdown = (equity_curve - peak) / peak
        max_drawdown = np.min(drawdown)
        
        # Additional metrics
        avg_periods_held = np.mean([t['periods_held'] for t in trades])
        best_trade = max([t['pnl'] for t in trades]) if trades else 0
        worst_trade = min([t['pnl'] for t in trades]) if trades else 0
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'total_pnl': total_pnl,
            'total_return_pct': (equity_curve[-1] - equity_curve[0]) / equity_curve[0] * 100,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': abs(avg_win / avg_loss) if avg_loss != 0 else float('inf'),
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown * 100,
            'avg_periods_held': avg_periods_held,
            'best_trade': best_trade,
            'worst_trade': worst_trade
        }

def run_improved_backtest(model_path, csv_file, resample_freq, seq_len, batch_size):
    """Run improved backtesting with realistic trading parameters"""
    
    print("Starting improved comprehensive backtest...")
    start_time = time.time()
    
    # Load saved model and scaler
    print("Loading model...")
    checkpoint = torch.load(model_path, weights_only=False)
    model_state_dict = checkpoint['model_state_dict']
    scaler = checkpoint['scaler']
    feature_cols = checkpoint['feature_cols']
    num_heads = checkpoint.get('num_heads', 3)
    num_layers = checkpoint.get('num_layers', 2)
    dropout = checkpoint.get('dropout', 0.1)
    
    # Load and preprocess data
    print("Loading and preprocessing data...")
    ticks = load_tick_data(csv_file)
    ohlc = resample_ohlc(ticks, freq=resample_freq)
    ohlc = add_indicators(ohlc)
    data_scaled, _ = scale_features(ohlc, feature_cols)
    
    print(f"Data loaded: {len(ohlc)} OHLC bars")
    
    # Create dataset and loader
    target_idx = feature_cols.index('close')
    dataset = TimeSeriesDataset(data_scaled, seq_len, target_idx)
    loader = DataLoader(dataset, batch_size=batch_size, shuffle=False)
    
    # Initialize model
    model = TransformerRegressor(seq_len, len(feature_cols), num_heads, num_layers, dropout)
    model.load_state_dict(model_state_dict)
    model.eval()
    
    print(f"Model loaded with {sum(p.numel() for p in model.parameters()):,} parameters")
    
    # Generate predictions
    print("Generating predictions...")
    all_preds = []
    all_actuals = []
    
    with torch.no_grad():
        for X_batch, y_batch in loader:
            preds = model(X_batch)
            all_preds.extend(preds.squeeze().numpy())
            all_actuals.extend(y_batch.squeeze().numpy())
    
    # Rescale predictions and actuals
    pred_prices = []
    actual_prices = []
    
    for i in range(len(all_preds)):
        dummy_pred = np.zeros((1, len(feature_cols)))
        dummy_actual = np.zeros((1, len(feature_cols)))
        dummy_pred[0, target_idx] = all_preds[i]
        dummy_actual[0, target_idx] = all_actuals[i]
        
        pred_rescaled = scaler.inverse_transform(dummy_pred)[0, target_idx]
        actual_rescaled = scaler.inverse_transform(dummy_actual)[0, target_idx]
        
        pred_prices.append(pred_rescaled)
        actual_prices.append(actual_rescaled)
    
    print(f"Generated {len(pred_prices)} predictions")
    
    # Calculate prediction accuracy metrics
    rmse = np.sqrt(mean_squared_error(actual_prices, pred_prices))
    mae = mean_absolute_error(actual_prices, pred_prices)
    mape = np.mean(np.abs((np.array(actual_prices) - np.array(pred_prices)) / np.array(actual_prices))) * 100
    
    print(f"\nPrediction Accuracy Metrics:")
    print(f"RMSE: {rmse:.6f}")
    print(f"MAE: {mae:.6f}")
    print(f"MAPE: {mape:.4f}%")
    
    # Generate trading signals with improved strategy
    print("\nGenerating trading signals...")
    strategy = ImprovedTradingStrategy(threshold=0.0005, stop_loss=0.01, take_profit=0.02, max_hold_periods=20)
    signals = strategy.generate_signals(actual_prices, pred_prices)
    
    buy_signals = sum(1 for s in signals if s == 1)
    sell_signals = sum(1 for s in signals if s == -1)
    hold_signals = sum(1 for s in signals if s == 0)
    
    print(f"Signals generated: {buy_signals} BUY, {sell_signals} SELL, {hold_signals} HOLD")
    
    # Run improved backtest
    print("\nRunning improved backtest...")
    backtest_engine = ImprovedBacktestEngine(initial_capital=10000, commission=0.001, leverage=1)
    trades, equity_curve, daily_returns = backtest_engine.backtest_strategy(actual_prices, signals, strategy)
    
    # Calculate performance metrics
    metrics = backtest_engine.calculate_metrics(trades, equity_curve, daily_returns)
    
    # Print results
    print("\n" + "="*60)
    print("IMPROVED BACKTESTING RESULTS")
    print("="*60)
    
    if metrics and trades:
        print(f"Total Trades: {metrics['total_trades']}")
        print(f"Winning Trades: {metrics['winning_trades']}")
        print(f"Losing Trades: {metrics['losing_trades']}")
        print(f"Win Rate: {metrics['win_rate']:.2%}")
        print(f"Total Return: {metrics['total_return_pct']:.2f}%")
        print(f"Total P&L: ${metrics['total_pnl']:.2f}")
        print(f"Average Win: ${metrics['avg_win']:.2f}")
        print(f"Average Loss: ${metrics['avg_loss']:.2f}")
        print(f"Profit Factor: {metrics['profit_factor']:.2f}")
        print(f"Sharpe Ratio: {metrics['sharpe_ratio']:.3f}")
        print(f"Max Drawdown: {metrics['max_drawdown']:.2f}%")
        print(f"Average Hold Time: {metrics['avg_periods_held']:.1f} periods")
        print(f"Best Trade: ${metrics['best_trade']:.2f}")
        print(f"Worst Trade: ${metrics['worst_trade']:.2f}")
        
        # Trade breakdown by close reason
        close_reasons = {}
        for trade in trades:
            reason = trade['close_reason']
            close_reasons[reason] = close_reasons.get(reason, 0) + 1
        
        print("\nTrade Close Reasons:")
        for reason, count in close_reasons.items():
            print(f"  {reason}: {count} trades")
            
    else:
        print("No trades executed during backtest period")
    
    # Create comprehensive plots
    print("\nCreating improved plots...")
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # Plot 1: Price predictions vs actual (more data points)
    plot_range = min(1000, len(actual_prices))
    ax1.plot(range(plot_range), actual_prices[:plot_range], label='Actual Prices', color='blue', alpha=0.7, linewidth=1)
    ax1.plot(range(plot_range), pred_prices[:plot_range], label='Predicted Prices', color='red', alpha=0.7, linewidth=1)
    ax1.set_title(f'Price Predictions vs Actual (First {plot_range} points)')
    ax1.set_xlabel('Time Steps')
    ax1.set_ylabel('Price')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Plot 2: Trading signals with entry/exit points
    ax2.plot(range(plot_range), actual_prices[:plot_range], color='black', alpha=0.6, linewidth=1, label='Price')
    
    # Mark trade entry and exit points
    if trades:
        for trade in trades:
            if trade['entry_index'] < plot_range:
                color = 'green' if trade['position'] == 1 else 'red'
                marker = '^' if trade['position'] == 1 else 'v'
                ax2.scatter(trade['entry_index'], actual_prices[trade['entry_index']], 
                           color=color, marker=marker, s=60, alpha=0.8)
                
                if trade['exit_index'] < plot_range:
                    ax2.scatter(trade['exit_index'], actual_prices[trade['exit_index']], 
                               color='orange', marker='x', s=60, alpha=0.8)
    
    ax2.set_title(f'Trading Entry/Exit Points (First {plot_range} points)')
    ax2.set_xlabel('Time Steps')
    ax2.set_ylabel('Price')
    ax2.legend(['Price', 'Long Entry', 'Short Entry', 'Exit'])
    ax2.grid(True, alpha=0.3)
    
    # Plot 3: Equity curve with comparison
    if equity_curve:
        ax3.plot(equity_curve, color='green', linewidth=2, label='Strategy')
        ax3.set_title('Equity Curve Comparison')
        ax3.set_xlabel('Time Steps')
        ax3.set_ylabel('Portfolio Value ($)')
        ax3.grid(True, alpha=0.3)
        
        # Add buy and hold comparison
        if len(actual_prices) >= len(equity_curve):
            initial_price = actual_prices[0]
            buy_hold_values = []
            for i in range(len(equity_curve)):
                if i < len(actual_prices):
                    current_price = actual_prices[i]
                    buy_hold_value = 10000 * (current_price / initial_price)
                    buy_hold_values.append(buy_hold_value)
                else:
                    buy_hold_values.append(buy_hold_values[-1])
            
            ax3.plot(buy_hold_values, color='blue', linestyle='--', alpha=0.7, label='Buy & Hold')
            ax3.legend()
    
    # Plot 4: Trade P&L distribution
    if trades:
        trade_pnls = [t['pnl'] for t in trades]
        ax4.hist(trade_pnls, bins=30, alpha=0.7, color='purple', edgecolor='black')
        ax4.set_title('Trade P&L Distribution')
        ax4.set_xlabel('Trade P&L ($)')
        ax4.set_ylabel('Frequency')
        ax4.grid(True, alpha=0.3)
        ax4.axvline(x=0, color='red', linestyle='--', alpha=0.8, label='Break-even')
        ax4.legend()
    else:
        ax4.text(0.5, 0.5, 'No Trades Executed', ha='center', va='center', transform=ax4.transAxes)
    
    plt.tight_layout()
    plt.savefig('improved_backtest_results.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    total_time = time.time() - start_time
    print(f"\nImproved backtest completed in {total_time:.2f} seconds")
    print(f"Results saved to: improved_backtest_results.png")
    
    return metrics, trades, equity_curve

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Improved Backtest Transformer Model')
    parser.add_argument('--model_path', type=str, default='transformer_trading_model.pth')
    parser.add_argument('--tick_csv', type=str, default='stpRNG_90days_ticks.csv')
    parser.add_argument('--resample_freq', type=str, default='1min')
    parser.add_argument('--seq_len', type=int, default=60)
    parser.add_argument('--batch_size', type=int, default=32)
    args = parser.parse_args()

    run_improved_backtest(
        model_path=args.model_path,
        csv_file=args.tick_csv,
        resample_freq=args.resample_freq,
        seq_len=args.seq_len,
        batch_size=args.batch_size
    )
