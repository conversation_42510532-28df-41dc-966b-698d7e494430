[2025-06-24 18:25:56] === XGBOOST TRADING STRATEGY (DIRECT) ===
[2025-06-24 18:25:56] Loading model from xgboost_model_20250521_145606.pkl...
[2025-06-24 18:25:56] Loading Renko data...
[2025-06-24 18:25:57] Renko data shape: (224649, 7)
[2025-06-24 18:25:57] Creating features from Renko data...
[2025-06-24 18:30:45] Features created. Shape: (224619, 50)
[2025-06-24 18:30:45] Feature columns (44): ['price', 'price_change', 'price_pct_change', 'direction', 'up_streak', 'down_streak', 'price_momentum_5', 'price_pct_change_5', 'up_count_5', 'down_count_5', 'volatility_5', 'max_up_streak_5', 'max_down_streak_5', 'price_momentum_10', 'price_pct_change_10', 'up_count_10', 'down_count_10', 'volatility_10', 'max_up_streak_10', 'max_down_streak_10', 'price_momentum_15', 'price_pct_change_15', 'up_count_15', 'down_count_15', 'volatility_15', 'max_up_streak_15', 'max_down_streak_15', 'price_momentum_20', 'price_pct_change_20', 'up_count_20', 'down_count_20', 'volatility_20', 'max_up_streak_20', 'max_down_streak_20', 'price_momentum_30', 'price_pct_change_30', 'up_count_30', 'down_count_30', 'volatility_30', 'max_up_streak_30', 'max_down_streak_30', 'hour', 'minute', 'day_of_week']
[2025-06-24 18:30:45] Features shape: (224619, 44)
[2025-06-24 18:30:45] Making predictions...
[2025-06-24 18:30:46] Merging predictions with Renko data...
[2025-06-24 18:30:50] Saved predictions to strategy_logs/predictions_20250624_182556.csv
[2025-06-24 18:30:50] Backtesting the strategy...
[2025-06-24 18:31:13] Saved strategy results to strategy_logs/xgboost_strategy_results_20250624_182556.csv
[2025-06-24 18:31:13] 
=== FINAL STRATEGY RESULTS ===
[2025-06-24 18:31:13] Trades Executed: 0
[2025-06-24 18:31:13] Final Balance: $100.00
[2025-06-24 18:31:13] Win Rate: 0.00%
[2025-06-24 18:31:13] Win Count: 0 of 0 trades (0.00%)
[2025-06-24 18:31:13] Profit Factor: 0.00
[2025-06-24 18:31:14] Saved equity curve to strategy_logs/equity_curve_20250624_182556.png
[2025-06-24 18:31:14] Strategy backtest completed successfully!
