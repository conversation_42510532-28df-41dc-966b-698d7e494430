# transformer_trading.py
# Unified Python script converting Transformer_Trading.ipynb notebook into a single module

import os
import time
import argparse
import numpy as np
import pandas as pd
from sklearn.preprocessing import MinMaxScaler
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
import matplotlib.pyplot as plt
import logging
from datetime import datetime

# ---------------------------
# Logging Setup
# ---------------------------

def setup_logging():
    """Setup detailed logging for training process"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"transformer_training_{timestamp}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename),
            logging.StreamHandler()  # Also print to console
        ]
    )
    return logging.getLogger(__name__)

# ---------------------------
# Fast Plotting Utilities
# ---------------------------

# Configure matplotlib for speed
plt.ion()  # Interactive mode
plt.style.use('fast')  # Use fast rendering style

class FastPlotter:
    """Ultra-fast plotting for real-time training visualization"""
    
    def __init__(self):
        self.fig = None
        self.ax = None
        self.train_line = None
        self.val_line = None
        self.setup_plot()
    
    def setup_plot(self):
        """Initialize plot with minimal overhead"""
        self.fig, self.ax = plt.subplots(figsize=(10, 6))
        self.ax.set_xlabel('Epoch')
        self.ax.set_ylabel('MSE Loss')
        self.ax.set_title('Training & Validation Loss (Live)')
        self.ax.grid(True, alpha=0.3)
        
        # Pre-create empty lines for speed
        self.train_line, = self.ax.plot([], [], 'b-', label='Train Loss', linewidth=2)
        self.val_line, = self.ax.plot([], [], 'r-', label='Val Loss', linewidth=2)
        self.ax.legend()
        
        plt.tight_layout()
        plt.show(block=False)
    
    def update_plot(self, epochs, train_losses, val_losses):
        """Ultra-fast plot update - only update data, not recreate"""
        if not epochs:
            return
            
        # Update line data - blazing fast
        self.train_line.set_data(epochs, train_losses)
        self.val_line.set_data(epochs, val_losses)
        
        # Auto-scale axes
        self.ax.relim()
        self.ax.autoscale_view()
        
        # Fast redraw
        self.fig.canvas.draw_idle()
        self.fig.canvas.flush_events()
    
    def save_final_plot(self, filename='training_loss.png'):
        """Save final plot"""
        self.fig.savefig(filename, dpi=150, bbox_inches='tight')
        print(f"Plot saved to {filename}")
    
    def close(self):
        """Clean up plot resources"""
        if self.fig:
            plt.close(self.fig)

# ---------------------------
# 1. Data Loading & Preprocessing
# ---------------------------

def load_tick_data(csv_file):
    """
    Loads tick-level CSV and returns a DataFrame with datetime index and price column.
    """
    df = pd.read_csv(csv_file)
    df['datetime'] = pd.to_datetime(df['datetime'])
    df.set_index('datetime', inplace=True)
    return df[['price']]


def resample_ohlc(df, freq='1min'):
    """
    Resamples tick data into OHLC bars at given frequency.
    """
    ohlc = df['price'].resample(freq).ohlc().dropna()
    return ohlc


def add_indicators(ohlc):
    """
    Adds RSI, Bollinger Bands, MA20, MA20 slope to OHLC DataFrame.
    """
    # RSI 14
    delta = ohlc['close'].diff()
    gain = delta.clip(lower=0)
    loss = -delta.clip(upper=0)
    avg_gain = gain.rolling(14).mean()
    avg_loss = loss.rolling(14).mean()
    rs = avg_gain / avg_loss
    ohlc['rsi'] = 100 - (100 / (1 + rs))

    # Bollinger Bands (20,2)
    ma20 = ohlc['close'].rolling(20).mean()
    std20 = ohlc['close'].rolling(20).std()
    ohlc['bb_high'] = ma20 + 2 * std20
    ohlc['bb_low']  = ma20 - 2 * std20

    # Moving Average and slope
    ohlc['ma_20'] = ma20
    ohlc['ma_20_slope'] = ma20.diff()

    # Fill NaNs
    ohlc.fillna(method='bfill', inplace=True)
    ohlc.fillna(method='ffill', inplace=True)
    return ohlc


def scale_features(df, feature_cols):
    """
    Scales feature columns between 0 and 1.
    Returns scaled numpy array and fitted scaler.
    """
    scaler = MinMaxScaler()
    arr = scaler.fit_transform(df[feature_cols])
    return arr.astype(np.float32), scaler

# ---------------------------
# 2. Dataset & DataLoader
# ---------------------------

class TimeSeriesDataset(Dataset):
    def __init__(self, data, seq_len, target_col_index):
        self.X, self.y = [], []
        self.seq_len = seq_len
        for i in range(len(data) - seq_len):
            self.X.append(data[i:i+seq_len])
            self.y.append(data[i+seq_len, target_col_index])
        self.X = torch.tensor(self.X)
        self.y = torch.tensor(self.y).unsqueeze(-1)

    def __len__(self):
        return len(self.X)

    def __getitem__(self, idx):
        return self.X[idx], self.y[idx]

# ---------------------------
# 3. Transformer Model
# ---------------------------

class TransformerRegressor(nn.Module):
    def __init__(self, seq_len, feature_size, num_heads=4, num_layers=2, dropout=0.1):
        super().__init__()
        self.pos_emb = nn.Parameter(torch.randn(seq_len, feature_size))
        encoder_layer = nn.TransformerEncoderLayer(d_model=feature_size,
                                                   nhead=num_heads,
                                                   dropout=dropout)
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        self.fc = nn.Linear(feature_size, 1)

    def forward(self, x):
        # x: (batch, seq, features)
        x = x + self.pos_emb.unsqueeze(0)
        x = x.permute(1, 0, 2)  # (seq, batch, features)
        out = self.transformer(x)
        out = out[-1]           # last timestep
        return self.fc(out)

# ---------------------------
# 4. Training & Evaluation
# ---------------------------

def train_model(model, train_loader, val_loader, epochs, lr, logger=None, plot_interval=5):
    """Enhanced training with detailed logging and blazing fast plotting"""
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=lr)
    history = {'train_loss': [], 'val_loss': []}
    
    # Initialize fast plotter
    plotter = FastPlotter()
    
    # Training start time
    training_start = time.time()
    
    if logger:
        logger.info(f"Starting training with {epochs} epochs")
        logger.info(f"Train samples: {len(train_loader.dataset)}, Val samples: {len(val_loader.dataset)}")
        logger.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
        logger.info(f"Plotting every {plot_interval} epochs for speed")
    
    best_val_loss = float('inf')
    no_improve_count = 0
    
    for ep in range(1, epochs + 1):
        epoch_start = time.time()
        
        # Training phase
        model.train()
        train_loss = 0
        batch_count = 0
        
        if logger:
            logger.info(f"Epoch {ep}/{epochs} - Training phase started")
        
        for batch_idx, (X_batch, y_batch) in enumerate(train_loader):
            batch_start = time.time()
            
            optimizer.zero_grad()
            preds = model(X_batch)
            loss = criterion(preds, y_batch)
            loss.backward()
            optimizer.step()
            
            batch_loss = loss.item() * X_batch.size(0)
            train_loss += batch_loss
            batch_count += 1
            
            # Detailed batch logging every 10 batches
            if logger and batch_idx % 10 == 0:
                batch_time = time.time() - batch_start
                logger.debug(f"  Batch {batch_idx}/{len(train_loader)} - Loss: {loss.item():.6f}, Time: {batch_time:.3f}s")
        
        train_loss /= len(train_loader.dataset)
        
        # Validation phase
        model.eval()
        val_loss = 0
        val_start = time.time()
        
        if logger:
            logger.info(f"Epoch {ep}/{epochs} - Validation phase started")
        
        with torch.no_grad():
            for X_batch, y_batch in val_loader:
                preds = model(X_batch)
                val_loss += criterion(preds, y_batch).item() * X_batch.size(0)
        
        val_loss /= len(val_loader.dataset)
        val_time = time.time() - val_start
        
        # Update history
        history['train_loss'].append(train_loss)
        history['val_loss'].append(val_loss)
        
        # Early stopping check
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            no_improve_count = 0
        else:
            no_improve_count += 1
        
        # Epoch timing
        epoch_time = time.time() - epoch_start
        total_time = time.time() - training_start
        
        # Detailed epoch logging
        if logger:
            logger.info(f"Epoch {ep}/{epochs} COMPLETED:")
            logger.info(f"  Train Loss: {train_loss:.6f}")
            logger.info(f"  Val Loss: {val_loss:.6f}")
            logger.info(f"  Best Val Loss: {best_val_loss:.6f}")
            logger.info(f"  Epoch Time: {epoch_time:.2f}s")
            logger.info(f"  Val Time: {val_time:.2f}s")
            logger.info(f"  Total Time: {total_time:.2f}s")
            logger.info(f"  Avg Time/Epoch: {total_time/ep:.2f}s")
            logger.info(f"  ETA: {(total_time/ep) * (epochs-ep):.1f}s")
            logger.info(f"  No Improvement: {no_improve_count} epochs")
            if torch.cuda.is_available():
                logger.info(f"  Memory Usage: {torch.cuda.memory_allocated()/1024/1024:.1f}MB")
            else:
                logger.info("  Memory Usage: CPU mode")
            logger.info("  " + "="*50)
        
        # Console output (always shown)
        improvement = "UP" if val_loss < best_val_loss else "DOWN" if no_improve_count > 0 else "SAME"
        print(f"Epoch {ep:3d}/{epochs} [{improvement}] | Train: {train_loss:.6f} | Val: {val_loss:.6f} | Time: {epoch_time:.1f}s | ETA: {(total_time/ep) * (epochs-ep):.0f}s")
        
        # Fast plotting update (only every plot_interval epochs for speed)
        if ep % plot_interval == 0 or ep == epochs:
            epochs_list = list(range(1, len(history['train_loss']) + 1))
            plotter.update_plot(epochs_list, history['train_loss'], history['val_loss'])
            
            if logger:
                logger.info(f"Plot updated at epoch {ep}")
    
    # Final training summary
    total_training_time = time.time() - training_start
    
    if logger:
        logger.info("TRAINING COMPLETED!")
        logger.info(f"  Total Training Time: {total_training_time:.2f}s ({total_training_time/60:.1f} minutes)")
        logger.info(f"  Best Validation Loss: {best_val_loss:.6f}")
        logger.info(f"  Final Train Loss: {train_loss:.6f}")
        logger.info(f"  Final Val Loss: {val_loss:.6f}")
        logger.info(f"  Average Epoch Time: {total_training_time/epochs:.2f}s")
    
    print(f"\nTraining Complete! Best Val Loss: {best_val_loss:.6f} | Total Time: {total_training_time:.1f}s")
    
    # Save final plot
    plotter.save_final_plot()
    
    # Clean up plotter
    plotter.close()
    
    return history

# ---------------------------
# 5. Main Execution
# ---------------------------

def main(args):
    # Setup detailed logging
    logger = setup_logging()
    
    logger.info("Starting Transformer Trading Model Training")
    logger.info(f"Configuration: {vars(args)}")
    
    start_time = time.time()
    
    # 1. Load and preprocess
    logger.info("Step 1: Loading and preprocessing data")
    ticks = load_tick_data(args.tick_csv)
    logger.info(f"Loaded {len(ticks)} tick records")
    
    ohlc = resample_ohlc(ticks, freq=args.resample_freq)
    logger.info(f"Resampled to {len(ohlc)} OHLC bars at {args.resample_freq} frequency")
    
    ohlc = add_indicators(ohlc)
    feature_cols = ['open','high','low','close','rsi','bb_high','bb_low','ma_20','ma_20_slope']
    data_scaled, scaler = scale_features(ohlc, feature_cols)
    logger.info(f"Added {len(feature_cols)} features and scaled data")

    # 2. Build dataset
    logger.info("Step 2: Building dataset")
    seq_len = args.seq_len
    target_idx = feature_cols.index('close')
    dataset = TimeSeriesDataset(data_scaled, seq_len, target_idx)
    train_size = int(args.train_split * len(dataset))
    train_ds, val_ds = torch.utils.data.random_split(dataset,
                        [train_size, len(dataset) - train_size])
    train_loader = DataLoader(train_ds, batch_size=args.batch_size, shuffle=True)
    val_loader   = DataLoader(val_ds, batch_size=args.batch_size)
    
    logger.info(f"Dataset created: {len(dataset)} total samples")
    logger.info(f"Train: {len(train_ds)} samples, Val: {len(val_ds)} samples")
    logger.info(f"Batch size: {args.batch_size}, Train batches: {len(train_loader)}, Val batches: {len(val_loader)}")

    # 3. Instantiate model
    logger.info("Step 3: Creating model")
    model = TransformerRegressor(seq_len, len(feature_cols),
                                 num_heads=args.num_heads,
                                 num_layers=args.num_layers,
                                 dropout=args.dropout)
    
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    logger.info(f"Model created with {total_params:,} total parameters ({trainable_params:,} trainable)")

    # 4. Train with detailed logging
    logger.info("Step 4: Starting training")
    history = train_model(model, train_loader, val_loader,
                          epochs=args.epochs, lr=args.learning_rate, logger=logger)

    # 5. Save the trained model
    logger.info("Step 5: Saving model")
    model_path = 'transformer_trading_model.pth'
    torch.save({
        'model_state_dict': model.state_dict(),
        'scaler': scaler,
        'feature_cols': feature_cols,
        'seq_len': seq_len,
        'num_heads': args.num_heads,
        'num_layers': args.num_layers,
        'dropout': args.dropout
    }, model_path)
    logger.info(f"Model saved to {model_path}")
    print(f"Model saved to {model_path}")
    
    # Final summary
    total_time = time.time() - start_time
    logger.info(f"Total execution time: {total_time:.2f}s ({total_time/60:.1f} minutes)")
    logger.info("Training completed successfully!")
    
    print(f"\nAll done! Total time: {total_time:.1f}s")

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Transformer Trading Model')
    parser.add_argument('--tick_csv',     type=str, default='stpRNG_90days_ticks.csv')
    parser.add_argument('--resample_freq',type=str, default='1min')
    parser.add_argument('--seq_len',      type=int, default=60)
    parser.add_argument('--train_split',  type=float, default=0.8)
    parser.add_argument('--batch_size',   type=int, default=32)
    parser.add_argument('--num_heads',    type=int, default=3)
    parser.add_argument('--num_layers',   type=int, default=2)
    parser.add_argument('--dropout',      type=float, default=0.1)
    parser.add_argument('--epochs',       type=int, default=10)
    parser.add_argument('--learning_rate',type=float, default=1e-3)
    args = parser.parse_args()
    main(args)
