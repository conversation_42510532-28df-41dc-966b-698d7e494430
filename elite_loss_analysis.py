# Elite Algorithm Loss Analysis
# This script analyzes the losses, profitable bars during positions, and recovery patterns

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class EliteLossAnalyzer:
    def __init__(self, results_file, trades_file):
        """
        Initialize the loss analyzer
        
        Args:
            results_file: Path to the detailed backtest results CSV
            trades_file: Path to the trades CSV
        """
        self.results_file = results_file
        self.trades_file = trades_file
        self.results = None
        self.trades = None
        self.analysis = {}
        
    def load_data(self):
        """Load the backtest results and trades data"""
        print("Loading backtest data...")
        
        # Load results
        self.results = pd.read_csv(self.results_file, index_col=0, parse_dates=True)
        print(f"Loaded {len(self.results)} bars of data")
        
        # Load trades
        self.trades = pd.read_csv(self.trades_file)
        print(f"Loaded {len(self.trades)} trades")
        
        return self.results, self.trades
    
    def analyze_position_profitability(self):
        """Analyze how many bars are profitable during each position hold"""
        print("\nAnalyzing position profitability by bars...")
        
        results = self.results.copy()
        
        # Track position changes
        position_changes = results['position'] != results['position'].shift(1)
        position_starts = results[position_changes].index
        
        position_analysis = []
        
        for i in range(len(position_starts) - 1):
            start_idx = position_starts[i]
            end_idx = position_starts[i + 1]
            
            # Get position data
            position_data = results.loc[start_idx:end_idx].copy()
            
            if len(position_data) <= 1:
                continue
                
            position_type = position_data['position'].iloc[0]
            entry_price = position_data['close'].iloc[0]
            
            # Calculate unrealized P&L for each bar in the position
            if position_type == 1:  # Long position
                position_data['unrealized_pnl'] = (position_data['close'] - entry_price) / entry_price
                profitable_bars = (position_data['close'] > entry_price).sum()
            elif position_type == -1:  # Short position
                position_data['unrealized_pnl'] = (entry_price - position_data['close']) / entry_price
                profitable_bars = (position_data['close'] < entry_price).sum()
            else:
                continue
            
            total_bars = len(position_data)
            profit_ratio = profitable_bars / total_bars if total_bars > 0 else 0
            max_unrealized_profit = position_data['unrealized_pnl'].max()
            max_unrealized_loss = position_data['unrealized_pnl'].min()
            
            position_analysis.append({
                'start_time': start_idx,
                'end_time': end_idx,
                'position_type': 'Long' if position_type == 1 else 'Short',
                'duration_bars': total_bars,
                'profitable_bars': profitable_bars,
                'profit_ratio': profit_ratio,
                'entry_price': entry_price,
                'exit_price': position_data['close'].iloc[-1],
                'max_unrealized_profit': max_unrealized_profit,
                'max_unrealized_loss': max_unrealized_loss,
                'final_pnl': position_data['unrealized_pnl'].iloc[-1]
            })
        
        self.position_analysis = pd.DataFrame(position_analysis)
        
        # Summary statistics
        avg_profit_ratio = self.position_analysis['profit_ratio'].mean()
        avg_duration = self.position_analysis['duration_bars'].mean()
        positions_with_profits = (self.position_analysis['profit_ratio'] > 0.5).sum()
        
        print(f"Average profitable bars ratio: {avg_profit_ratio:.2f} ({avg_profit_ratio*100:.1f}%)")
        print(f"Average position duration: {avg_duration:.1f} bars")
        print(f"Positions with >50% profitable bars: {positions_with_profits}/{len(self.position_analysis)} ({positions_with_profits/len(self.position_analysis)*100:.1f}%)")
        
        return self.position_analysis
    
    def analyze_recovery_patterns(self, recovery_bars=[3, 4, 5, 10]):
        """Analyze recovery patterns after losses"""
        print(f"\nAnalyzing recovery patterns after losses...")
        
        # Get losing trades
        losing_trades = self.trades[self.trades['return'] < 0].copy()
        print(f"Found {len(losing_trades)} losing trades out of {len(self.trades)} total trades")
        
        recovery_analysis = []
        
        for _, trade in losing_trades.iterrows():
            trade_loss = trade['return']
            
            # Find this trade in the results data
            if trade['type'] == 'Long':
                # For long trades, find where exit_price matches and position was 1
                trade_exit_mask = (self.results['exit_price'] == trade['exit']) & \
                                (self.results['position'].shift(1) == 1)
            else:  # Short trade
                trade_exit_mask = (self.results['exit_price'] == trade['exit']) & \
                                (self.results['position'].shift(1) == -1)
            
            if not trade_exit_mask.any():
                continue
                
            exit_indices = self.results[trade_exit_mask].index
            if len(exit_indices) == 0:
                continue
            exit_idx = exit_indices[0]
            try:
                exit_idx_pos = self.results.index.get_loc(exit_idx)
            except KeyError:
                continue
            
            # Handle case where get_loc returns a slice
            if isinstance(exit_idx_pos, slice):
                exit_idx_pos = exit_idx_pos.start
            
            # Analyze recovery for different time horizons
            for bars in recovery_bars:
                if exit_idx_pos + bars >= len(self.results):
                    continue
                    
                # Get future prices
                future_data = self.results.iloc[exit_idx_pos:exit_idx_pos + bars + 1]
                exit_price = trade['exit']
                
                # Calculate potential recovery
                if trade['type'] == 'Long':
                    # For a long loss, recovery means price going back up
                    future_returns = (future_data['close'] - exit_price) / exit_price
                    max_recovery = future_returns.max()
                    recovery_achieved = max_recovery >= abs(trade_loss)
                else:  # Short loss
                    # For a short loss, recovery means price going back down
                    future_returns = (exit_price - future_data['close']) / exit_price
                    max_recovery = future_returns.max()
                    recovery_achieved = max_recovery >= abs(trade_loss)
                
                bars_to_recovery = None
                if recovery_achieved:
                    recovery_mask = future_returns >= abs(trade_loss)
                    if recovery_mask.any():
                        bars_to_recovery = recovery_mask.idxmax()
                        bars_to_recovery = future_data.index.get_loc(bars_to_recovery)
                
                recovery_analysis.append({
                    'trade_type': trade['type'],
                    'trade_loss': trade_loss,
                    'exit_price': exit_price,
                    'analysis_window': bars,
                    'max_recovery': max_recovery,
                    'recovery_achieved': recovery_achieved,
                    'bars_to_recovery': bars_to_recovery,
                    'recovery_ratio': max_recovery / abs(trade_loss) if trade_loss != 0 else 0
                })
        
        self.recovery_analysis = pd.DataFrame(recovery_analysis)
        
        # Summary by recovery window
        print("\nRecovery Analysis Summary:")
        print("-" * 50)
        
        for bars in recovery_bars:
            window_data = self.recovery_analysis[self.recovery_analysis['analysis_window'] == bars]
            if len(window_data) == 0:
                continue
                
            recovery_rate = (window_data['recovery_achieved']).mean()
            avg_recovery_ratio = window_data['recovery_ratio'].mean()
            avg_bars_to_recovery = window_data[window_data['recovery_achieved']]['bars_to_recovery'].mean()
            
            print(f"{bars}-bar window:")
            print(f"  Recovery rate: {recovery_rate:.2f} ({recovery_rate*100:.1f}%)")
            print(f"  Average recovery ratio: {avg_recovery_ratio:.2f}")
            if not np.isnan(avg_bars_to_recovery):
                print(f"  Average bars to recovery: {avg_bars_to_recovery:.1f}")
            print()
        
        return self.recovery_analysis
    
    def analyze_alternation_patterns(self):
        """Analyze the buy/sell alternation patterns and their profitability"""
        print("\nAnalyzing buy/sell alternation patterns...")
        
        # Get signal changes
        signals = self.results[self.results['final_signal'].notna()].copy()
        
        # Track alternation patterns
        alternation_analysis = []
        
        for i in range(1, len(signals)):
            prev_signal = signals.iloc[i-1]['final_signal']
            curr_signal = signals.iloc[i]['final_signal']
            
            # Check if it's an alternation
            is_alternation = False
            if prev_signal in ['Buy', 'Strong Buy'] and curr_signal in ['Sell', 'Strong Sell']:
                is_alternation = True
                pattern = 'Buy->Sell'
            elif prev_signal in ['Sell', 'Strong Sell'] and curr_signal in ['Buy', 'Strong Buy']:
                is_alternation = True
                pattern = 'Sell->Buy'
            
            if is_alternation:
                # Get the data between signals
                start_idx = signals.index[i-1]
                end_idx = signals.index[i]
                
                between_data = self.results.loc[start_idx:end_idx]
                start_price = between_data['close'].iloc[0]
                end_price = between_data['close'].iloc[-1]
                
                # Calculate return based on signal direction
                if prev_signal in ['Buy', 'Strong Buy']:
                    # Long position
                    signal_return = (end_price - start_price) / start_price
                    profitable_bars = (between_data['close'] > start_price).sum()
                else:
                    # Short position
                    signal_return = (start_price - end_price) / start_price
                    profitable_bars = (between_data['close'] < start_price).sum()
                
                alternation_analysis.append({
                    'pattern': pattern,
                    'start_time': start_idx,
                    'end_time': end_idx,
                    'duration_bars': len(between_data),
                    'start_price': start_price,
                    'end_price': end_price,
                    'signal_return': signal_return,
                    'profitable_bars': profitable_bars,
                    'profit_bar_ratio': profitable_bars / len(between_data) if len(between_data) > 0 else 0,
                    'prev_signal_strength': signals.iloc[i-1]['long_signal_strength'] if prev_signal in ['Buy', 'Strong Buy'] else signals.iloc[i-1]['short_signal_strength']
                })
        
        self.alternation_analysis = pd.DataFrame(alternation_analysis)
        
        # Summary statistics
        if len(self.alternation_analysis) > 0:
            profitable_alternations = (self.alternation_analysis['signal_return'] > 0).sum()
            avg_return = self.alternation_analysis['signal_return'].mean()
            avg_profit_bar_ratio = self.alternation_analysis['profit_bar_ratio'].mean()
            avg_duration = self.alternation_analysis['duration_bars'].mean()
            
            print(f"Total alternations analyzed: {len(self.alternation_analysis)}")
            print(f"Profitable alternations: {profitable_alternations}/{len(self.alternation_analysis)} ({profitable_alternations/len(self.alternation_analysis)*100:.1f}%)")
            print(f"Average return per alternation: {avg_return:.4f} ({avg_return*100:.2f}%)")
            print(f"Average profitable bars ratio: {avg_profit_bar_ratio:.2f} ({avg_profit_bar_ratio*100:.1f}%)")
            print(f"Average duration: {avg_duration:.1f} bars")
            
            # Analyze by pattern type
            for pattern in self.alternation_analysis['pattern'].unique():
                pattern_data = self.alternation_analysis[self.alternation_analysis['pattern'] == pattern]
                pattern_win_rate = (pattern_data['signal_return'] > 0).mean()
                pattern_avg_return = pattern_data['signal_return'].mean()
                
                print(f"\n{pattern} patterns:")
                print(f"  Count: {len(pattern_data)}")
                print(f"  Win rate: {pattern_win_rate:.2f} ({pattern_win_rate*100:.1f}%)")
                print(f"  Average return: {pattern_avg_return:.4f} ({pattern_avg_return*100:.2f}%)")
        
        return self.alternation_analysis
    
    def generate_summary_report(self):
        """Generate a comprehensive summary report"""
        print("\n" + "="*60)
        print("ELITE ALGORITHM LOSS ANALYSIS SUMMARY")
        print("="*60)
        
        # Overall statistics
        total_trades = len(self.trades)
        losing_trades = len(self.trades[self.trades['return'] < 0])
        loss_rate = losing_trades / total_trades
        
        print(f"\nOverall Performance:")
        print(f"Total Trades: {total_trades}")
        print(f"Losing Trades: {losing_trades} ({loss_rate*100:.1f}%)")
        print(f"Average Loss: {self.trades[self.trades['return'] < 0]['return'].mean():.4f}")
        
        # Position profitability
        if hasattr(self, 'position_analysis'):
            avg_profit_ratio = self.position_analysis['profit_ratio'].mean()
            print(f"\nPosition Bar Analysis:")
            print(f"Average profitable bars during positions: {avg_profit_ratio*100:.1f}%")
            print(f"Positions with majority profitable bars: {(self.position_analysis['profit_ratio'] > 0.5).mean()*100:.1f}%")
        
        # Recovery patterns
        if hasattr(self, 'recovery_analysis'):
            print(f"\nRecovery Potential:")
            for bars in [3, 4, 5]:
                recovery_data = self.recovery_analysis[self.recovery_analysis['analysis_window'] == bars]
                if len(recovery_data) > 0:
                    recovery_rate = recovery_data['recovery_achieved'].mean()
                    print(f"  {bars}-bar recovery rate: {recovery_rate*100:.1f}%")
        
        # Alternation patterns
        if hasattr(self, 'alternation_analysis'):
            alternation_win_rate = (self.alternation_analysis['signal_return'] > 0).mean()
            print(f"\nAlternation Patterns:")
            print(f"Signal alternation win rate: {alternation_win_rate*100:.1f}%")
        
        print("\n" + "="*60)
    
    def save_analysis_results(self):
        """Save all analysis results to CSV files"""
        print("\nSaving analysis results...")
        
        if hasattr(self, 'position_analysis'):
            self.position_analysis.to_csv('elite_position_analysis.csv', index=False)
            print("Position analysis saved to: elite_position_analysis.csv")
        
        if hasattr(self, 'recovery_analysis'):
            self.recovery_analysis.to_csv('elite_recovery_analysis.csv', index=False)
            print("Recovery analysis saved to: elite_recovery_analysis.csv")
        
        if hasattr(self, 'alternation_analysis'):
            self.alternation_analysis.to_csv('elite_alternation_analysis.csv', index=False)
            print("Alternation analysis saved to: elite_alternation_analysis.csv")
    
    def run_full_analysis(self):
        """Run the complete loss analysis"""
        self.load_data()
        self.analyze_position_profitability()
        self.analyze_recovery_patterns()
        self.analyze_alternation_patterns()
        self.generate_summary_report()
        self.save_analysis_results()
        
        return {
            'position_analysis': self.position_analysis if hasattr(self, 'position_analysis') else None,
            'recovery_analysis': self.recovery_analysis if hasattr(self, 'recovery_analysis') else None,
            'alternation_analysis': self.alternation_analysis if hasattr(self, 'alternation_analysis') else None
        }


def main():
    """Main function to run the loss analysis"""
    # File paths
    results_file = 'renko_elite_backtest_results.csv'
    trades_file = 'renko_elite_trades.csv'
    
    # Initialize and run analysis
    analyzer = EliteLossAnalyzer(results_file, trades_file)
    analysis_results = analyzer.run_full_analysis()
    
    print("\nLoss analysis completed successfully!")
    return analyzer

if __name__ == "__main__":
    analyzer = main()
