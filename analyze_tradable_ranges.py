import pandas as pd
import numpy as np

def analyze_tradable_ranges(file_path, spike_threshold_pct=0.1):
    """
    Analyzes the tradability of ranges in tick data, simulating short trades
    while attempting to evade spikes.

    Args:
        file_path (str): Path to the tick data CSV file.
        spike_threshold_pct (float): Percentage change in bid price to consider a spike.
    """
    print(f"Loading data from {file_path}...")
    try:
        df = pd.read_csv(file_path)
        print("Data loaded successfully.")
    except FileNotFoundError:
        print(f"Error: File not found at {file_path}")
        return
    except Exception as e:
        print(f"Error loading data: {e}")
        return

    # Ensure 'time' column is datetime and sort
    df['time'] = pd.to_datetime(df['time'])
    df = df.sort_values('time').reset_index(drop=True)

    # --- Spike Identification ---
    # Calculate percentage change in bid price
    df['bid_change_pct'] = df['bid'].pct_change() * 100
    # Mark minutes where a spike occurred
    df['is_spike'] = df['bid_change_pct'].abs() > spike_threshold_pct

    # --- Minute-level OHLC and Range Calculation ---
    # Group by minute and calculate OHLC for bid price
    minute_data = df.set_index('time').groupby(pd.Grouper(freq='1min'))
    minute_ohlc = minute_data['bid'].ohlc()
    minute_ohlc['range'] = minute_ohlc['high'] - minute_ohlc['low']

    # Determine if a spike occurred within each minute interval
    # If any tick in the minute had a spike, mark the minute as having a spike
    minute_ohlc['has_spike'] = minute_data['is_spike'].any()

    # Filter out minutes with no data (NaN ranges) or no trading activity
    minute_ohlc = minute_ohlc.dropna(subset=['range'])

    print(f"\nAnalyzed {len(minute_ohlc)} one-minute intervals for tradability.")

    # --- Tradability Simulation ---
    trades = []
    for index, row in minute_ohlc.iterrows():
        if not row['has_spike']:
            # Simulate a short trade: open at high, close at low
            entry_price = row['high']
            exit_price = row['low']
            
            # Only consider trades where there's a positive range to capture
            if entry_price > exit_price:
                profit = entry_price - exit_price
                trades.append({
                    'timestamp': index,
                    'entry_price': entry_price,
                    'exit_price': exit_price,
                    'profit': profit,
                    'range': row['range']
                })

    trades_df = pd.DataFrame(trades)

    print(f"Simulated {len(trades_df)} tradable ranges (minutes without spikes).")

    # --- Statistical Analysis ---
    if not trades_df.empty:
        total_trades = len(trades_df)
        profitable_trades = trades_df[trades_df['profit'] > 0]
        losing_trades = trades_df[trades_df['profit'] <= 0]

        num_profitable = len(profitable_trades)
        num_losing = len(losing_trades)

        win_rate = (num_profitable / total_trades) * 100 if total_trades > 0 else 0
        total_profit = trades_df['profit'].sum()

        avg_profit_per_profitable_trade = profitable_trades['profit'].mean() if num_profitable > 0 else 0
        avg_loss_per_losing_trade = losing_trades['profit'].mean() if num_losing > 0 else 0 # This will be negative or zero

        avg_range_of_traded_intervals = trades_df['range'].mean()

        print("\n--- Tradability Statistics ---")
        print(f"Total Simulated Trades: {total_trades}")
        print(f"Profitable Trades: {num_profitable}")
        print(f"Losing Trades (or zero profit): {num_losing}")
        print(f"Win Rate: {win_rate:.2f}%")
        print(f"Total Simulated P&L: {total_profit:.2f}")
        print(f"Average Profit per Profitable Trade: {avg_profit_per_profitable_trade:.2f}")
        print(f"Average Loss per Losing Trade: {avg_loss_per_losing_trade:.2f}")
        print(f"Average Range of Traded Intervals: {avg_range_of_traded_intervals:.2f}")

        print("\n--- Sample Trades (Profitable) ---")
        print(profitable_trades.head())

        print("\n--- Sample Trades (Losing/Zero) ---")
        print(losing_trades.head())

    else:
        print("No tradable ranges found after spike evasion.")

if __name__ == "__main__":
    data_file = "Range_Break_100_7days_20250623_20250630.csv"
    analyze_tradable_ranges(data_file)
