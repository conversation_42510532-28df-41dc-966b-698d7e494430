# Inverse Elite Algorithm Trading Strategy
# This script implements the exact logic that achieved 61% win rate by trading opposite to Elite signals

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# Import the Elite Algorithm
from elite import run_elite_algo

class InverseEliteStrategy:
    def __init__(self, data_path, initial_capital=10000):
        """
        Initialize the Inverse Elite Strategy
        
        Args:
            data_path: Path to the Renko data CSV file
            initial_capital: Starting capital for strategy
        """
        self.data_path = data_path
        self.initial_capital = initial_capital
        
        # Results storage
        self.renko_data = None
        self.ohlcv_data = None
        self.elite_signals = None
        self.strategy_results = None
        self.trades = None
        
    def load_and_prepare_data(self):
        """Load Renko data and prepare OHLCV format"""
        print(f"Loading Renko data from {self.data_path}...")
        
        # Load Renko data
        self.renko_data = pd.read_csv(self.data_path)
        self.renko_data['datetime'] = pd.to_datetime(self.renko_data['datetime'])
        self.renko_data.set_index('datetime', inplace=True)
        
        print(f"Loaded {len(self.renko_data)} Renko bars")
        print(f"Date range: {self.renko_data.index.min()} to {self.renko_data.index.max()}")
        
        # Prepare OHLCV data for Elite Algorithm
        self.ohlcv_data = self.renko_data[['open', 'high', 'low', 'close']].copy()
        self.ohlcv_data['volume'] = 1000  # Constant volume for Renko bars
        
        print(f"Prepared {len(self.ohlcv_data)} OHLCV bars from Renko data")
        return self.ohlcv_data
    
    def generate_elite_signals(self):
        """Generate Elite Algorithm signals"""
        print("Generating Elite Algorithm signals...")
        
        if self.ohlcv_data is None:
            raise ValueError("OHLCV data not prepared. Run load_and_prepare_data() first.")
        
        # Run the Elite Algorithm
        self.elite_signals = run_elite_algo(self.ohlcv_data.copy())
        
        # Count original signals
        signal_counts = self.elite_signals['final_signal'].value_counts()
        print("Elite Algorithm signal counts:")
        print(signal_counts)
        
        return self.elite_signals
    
    def create_inverse_signals(self):
        """Create inverse signals - exact opposite of Elite Algorithm"""
        print("Creating inverse signals...")
        
        df = self.elite_signals.copy()
        
        # Inverse signal mapping - exact opposite
        inverse_signal_map = {
            'Buy': 'Sell',
            'Strong Buy': 'Strong Sell', 
            'Sell': 'Buy',
            'Strong Sell': 'Strong Buy'
        }
        
        # Apply inverse mapping
        df['inverse_signal'] = df['final_signal'].map(inverse_signal_map)
        
        # Count inverse signals
        inverse_signal_counts = df['inverse_signal'].value_counts()
        print("Inverse signal counts:")
        print(inverse_signal_counts)
        
        return df
    
    def run_strategy(self):
        """Run the Inverse Elite Strategy - exact same logic that got 61% win rate"""
        print("Running Inverse Elite Strategy...")
        
        df = self.create_inverse_signals()
        
        # Initialize strategy columns
        df['position'] = 0  # 1 for long, -1 for short, 0 for flat
        df['entry_price'] = np.nan
        df['exit_price'] = np.nan
        df['trade_return'] = 0.0
        df['cumulative_return'] = 0.0
        df['portfolio_value'] = self.initial_capital
        df['drawdown'] = 0.0
        
        # Track current position
        current_position = 0
        entry_price = 0
        trades = []
        
        # Execute trades based on inverse signals - EXACT same logic as the 61% winner
        for i in range(len(df)):
            signal = df.iloc[i]['inverse_signal']
            current_price = df.iloc[i]['close']
            
            # No slippage - pure price data
            execution_price = current_price
            
            # Process inverse signals - EXACT same logic
            if signal in ['Buy', 'Strong Buy'] and current_position <= 0:
                # Close short position if any
                if current_position < 0:
                    trade_return = (entry_price - execution_price) / entry_price
                    df.iloc[i, df.columns.get_loc('exit_price')] = execution_price
                    df.iloc[i, df.columns.get_loc('trade_return')] = trade_return
                    
                    trades.append({
                        'type': 'Short',
                        'entry': entry_price,
                        'exit': execution_price,
                        'return': trade_return,
                        'entry_time': df.index[i],
                        'signal_strength': df.iloc[i]['short_signal_strength']
                    })
                
                # Open long position
                current_position = 1
                entry_price = execution_price
                df.iloc[i, df.columns.get_loc('entry_price')] = execution_price
                
            elif signal in ['Sell', 'Strong Sell'] and current_position >= 0:
                # Close long position if any
                if current_position > 0:
                    trade_return = (execution_price - entry_price) / entry_price
                    df.iloc[i, df.columns.get_loc('exit_price')] = execution_price
                    df.iloc[i, df.columns.get_loc('trade_return')] = trade_return
                    
                    trades.append({
                        'type': 'Long',
                        'entry': entry_price,
                        'exit': execution_price,
                        'return': trade_return,
                        'entry_time': df.index[i],
                        'signal_strength': df.iloc[i]['long_signal_strength']
                    })
                
                # Open short position
                current_position = -1
                entry_price = execution_price
                df.iloc[i, df.columns.get_loc('entry_price')] = execution_price
            
            # Update position
            df.iloc[i, df.columns.get_loc('position')] = current_position
        
        # Calculate portfolio metrics
        df['cumulative_return'] = df['trade_return'].cumsum()
        df['portfolio_value'] = self.initial_capital * (1 + df['cumulative_return'])
        
        # Calculate drawdown
        running_max = df['portfolio_value'].expanding().max()
        df['drawdown'] = (df['portfolio_value'] - running_max) / running_max
        
        self.strategy_results = df
        self.trades = pd.DataFrame(trades)
        
        return df, self.trades
    
    def calculate_performance_metrics(self):
        """Calculate strategy performance metrics"""
        print("Calculating performance metrics...")
        
        if self.strategy_results is None:
            raise ValueError("Strategy not run yet.")
        
        df = self.strategy_results
        trades = self.trades
        
        # Basic metrics
        total_return = df['cumulative_return'].iloc[-1]
        final_value = df['portfolio_value'].iloc[-1]
        max_drawdown = df['drawdown'].min()
        num_trades = len(trades)
        
        if num_trades > 0:
            winning_trades = len(trades[trades['return'] > 0])
            losing_trades = len(trades[trades['return'] < 0])
            win_rate = winning_trades / num_trades
            avg_return = trades['return'].mean()
            avg_win = trades[trades['return'] > 0]['return'].mean() if winning_trades > 0 else 0
            avg_loss = trades[trades['return'] < 0]['return'].mean() if losing_trades > 0 else 0
            profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')
            sharpe = avg_return / trades['return'].std() if trades['return'].std() != 0 else 0
        else:
            winning_trades = losing_trades = 0
            win_rate = avg_return = avg_win = avg_loss = profit_factor = sharpe = 0
        
        # Performance metrics
        metrics = {
            'Total Return': f"{total_return:.4f} ({total_return*100:.2f}%)",
            'Final Portfolio Value': f"${final_value:,.2f}",
            'Max Drawdown': f"{max_drawdown:.4f} ({max_drawdown*100:.2f}%)",
            'Total Trades': num_trades,
            'Winning Trades': winning_trades,
            'Losing Trades': losing_trades,
            'Win Rate': f"{win_rate:.3f} ({win_rate*100:.1f}%)",
            'Average Return per Trade': f"{avg_return:.6f} ({avg_return*100:.4f}%)",
            'Average Winning Trade': f"{avg_win:.6f} ({avg_win*100:.4f}%)",
            'Average Losing Trade': f"{avg_loss:.6f} ({avg_loss*100:.4f}%)",
            'Profit Factor': f"{profit_factor:.2f}",
            'Sharpe Ratio': f"{sharpe:.2f}"
        }
        
        return metrics
    
    def print_performance_report(self):
        """Print detailed performance report"""
        metrics = self.calculate_performance_metrics()
        
        print("\n" + "="*60)
        print("INVERSE ELITE ALGORITHM STRATEGY RESULTS")
        print("="*60)
        
        for key, value in metrics.items():
            print(f"{key:<30}: {value}")
        
        print("\n" + "="*60)
        
        return metrics
    
    def save_results(self):
        """Save strategy results"""
        print("\nSaving strategy results...")
        
        if self.strategy_results is not None:
            self.strategy_results.to_csv('inverse_elite_strategy_results.csv')
            print("Strategy results saved to: inverse_elite_strategy_results.csv")
        
        if self.trades is not None and len(self.trades) > 0:
            self.trades.to_csv('inverse_elite_strategy_trades.csv', index=False)
            print("Trade log saved to: inverse_elite_strategy_trades.csv")
    
    def run_complete_strategy(self):
        """Run the complete strategy pipeline"""
        print("=" * 60)
        print("INVERSE ELITE ALGORITHM TRADING STRATEGY")
        print("=" * 60)
        
        # Execute strategy pipeline
        self.load_and_prepare_data()
        self.generate_elite_signals()
        self.run_strategy()
        
        # Generate performance report
        metrics = self.print_performance_report()
        
        # Save results
        self.save_results()
        
        print("\nInverse Elite Strategy execution completed!")
        return self.strategy_results, self.trades, metrics


def main():
    """Main function to run the Inverse Elite Strategy"""
    # Configuration
    DATA_PATH = r"C:\Users\<USER>\META\stpRNG_renko_0_1_extended.csv"
    
    # Initialize and run strategy
    strategy = InverseEliteStrategy(
        data_path=DATA_PATH,
        initial_capital=10000
    )
    
    # Execute complete strategy
    results, trades, metrics = strategy.run_complete_strategy()
    
    return strategy

if __name__ == "__main__":
    strategy = main()
