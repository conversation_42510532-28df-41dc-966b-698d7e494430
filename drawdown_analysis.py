import pandas as pd
import numpy as np

# Placeholder for trade data - replace with actual data source
trades_df = pd.DataFrame({
    'entry_time': pd.date_range(start='2025-01-01', periods=100, freq='D'),
    'exit_time': pd.date_range(start='2025-01-02', periods=100, freq='D'),
    'entry_price': np.random.uniform(0.8, 1.2, 100),
    'exit_price': np.random.uniform(0.8, 1.2, 100),
    'pnl': np.random.uniform(-0.1, 0.1, 100),
    'probability': np.random.uniform(0.4, 0.6, 100),
    'capital': [100000] * 100
})

# Calculate High-Water Mark and Drawdown
trades_df['cumulative_pnl'] = trades_df['pnl'].cumsum()
trades_df['high_water_mark'] = trades_df['cumulative_pnl'].cummax()
trades_df['drawdown'] = trades_df['cumulative_pnl'] - trades_df['high_water_mark']
trades_df['drawdown_pct'] = trades_df['drawdown'] / trades_df['high_water_mark'] * 100

max_drawdown = trades_df['drawdown_pct'].min()
drawdown_duration = (trades_df['drawdown'] != 0).astype(int).sum()  # Count periods within a drawdown

print(f"Maximum Drawdown: {max_drawdown:.2f}%")
print(f"Drawdown Duration: {drawdown_duration} periods")

# Calculate Risk of Ruin
win_probability = 0.6  # Example probability of a trade being a win
lose_probability = 1 - win_probability
capital = 100000
unit_risk = trades_df['pnl'].abs().mean()  # Assume average risk per trade in the same unit

risk_of_ruin = (lose_probability / win_probability) ** (capital / unit_risk) / \
                (1 - (lose_probability / win_probability) ** (capital / unit_risk))

print(f"Risk of Ruin: {risk_of_ruin:.4f}")
