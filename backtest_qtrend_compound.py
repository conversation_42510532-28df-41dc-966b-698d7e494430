import pandas as pd
import numpy as np
import math
from datetime import datetime
import os
import time

# Logging setup
log_dir = "strategy_logs"
os.makedirs(log_dir, exist_ok=True)
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
log_filename = f"{log_dir}/qtrend_strategy_{timestamp}.log"
def log_message(message):
    timestamp_str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_msg = f"[{timestamp_str}] {message}"
    print(log_msg)
    with open(log_filename, 'a') as f:
        f.write(log_msg + '\n')

# Load 5-second range bar data
csv_path = "C:/Users/<USER>/META/stpRNG_range_5s.csv"
df = pd.read_csv(csv_path)
df['datetime'] = pd.to_datetime(df['datetime'])

# Q-Trend parameters (adjusted for 5s bars)
p = 60  # Reduced period for faster response
atr_p = 20  # Increased ATR period for more stable volatility measurement
mult = 0.3  # Reduced multiplier for tighter bands
mode = "Type B"
use_ema = True  # Enable EMA for smoother price action

# Strategy parameters
SPREAD = 0.0
MIN_VOL = 0.10
MAX_VOL_PER_POS = 10.0     # Further reduced for 5s timeframe
MAX_TOTAL_VOL = 40.0       # Further reduced for 5s timeframe
BAR_SIZE = 0.05            # Average bar size
MIN_EQUITY = 50.0          # Higher minimum equity requirement
INITIAL_RISK = 0.01        # Very conservative initial risk
MAX_CONSECUTIVE_LOSSES = 2  # More aggressive stopping after losses
MIN_WIN_RATE = 0.4         # Minimum required win rate to continue trading
RECOVERY_BARS = 20         # Bars to wait after stopping

def calculate_dynamic_risk(equity, equity_history, win_streak, loss_streak, recent_outcomes=None):
    if loss_streak >= MAX_CONSECUTIVE_LOSSES:
        return 0.0  # Stop trading after max consecutive losses
        
    # Calculate recent win rate
    if recent_outcomes and len(recent_outcomes) >= 10:
        recent_win_rate = sum(1 for t in recent_outcomes[-10:] if t > 0) / 10
        if recent_win_rate < MIN_WIN_RATE:
            return 0.0  # Stop if win rate is too low
    
    base_risk = INITIAL_RISK
    if win_streak >= 3:
        streak_factor = 1.1
    elif win_streak >= 2:
        streak_factor = 1.05
    elif loss_streak >= 1:
        streak_factor = 0.5
    else:
        streak_factor = 1.0
        
    if equity < 500:
        equity_factor = 0.5
    elif equity < 1000:
        equity_factor = 0.6
    elif equity < 10000:
        equity_factor = 0.7
    else:
        equity_factor = 0.8
        
    if recent_outcomes and len(recent_outcomes) >= 10:  # Changed from 20 to 10 for faster adaptation
        win_rate = sum(1 for t in recent_outcomes[-10:] if t > 0) / 10
        if win_rate > 0.7:  # Changed from 0.9
            performance_factor = 1.1  # Reduced from 1.2
        elif win_rate > 0.6:  # Changed from 0.8
            performance_factor = 1.05  # Reduced from 1.1
        elif win_rate < 0.4:  # Added more granular scaling
            performance_factor = 0.7
        elif win_rate < 0.5:
            performance_factor = 0.8
        else:
            performance_factor = 1.0
    else:
        performance_factor = 0.8  # Start more conservatively
        
    if len(equity_history) > 1:
        peak = max(equity_history)
        drawdown = (peak - equity) / peak
        if drawdown > 0.15:  # More aggressive scaling at lower drawdown
            drawdown_factor = 0.5
        elif drawdown > 0.1:
            drawdown_factor = 0.7
        elif drawdown > 0.05:
            drawdown_factor = 0.85
        else:
            drawdown_factor = 1.0
    else:
        drawdown_factor = 1.0
        
    risk = base_risk * streak_factor * equity_factor * performance_factor * drawdown_factor
    return max(0.01, min(risk, 0.03))  # Further reduced max risk to 3%

def calculate_position_size(risk_amount, price_risk, max_per_position, max_total_vol, current_open_vol):
    ideal_lot_size = risk_amount / (price_risk * 10)
    available_vol = max_total_vol - current_open_vol
    if ideal_lot_size <= max_per_position:
        lot_size = min(ideal_lot_size, max_per_position, available_vol)
        num_positions = 1 if lot_size > 0 else 0
    else:
        max_possible_vol = min(ideal_lot_size, available_vol)
        if max_possible_vol <= 0:
            return 0, 0
        num_positions = math.ceil(max_possible_vol / max_per_position)
        lot_size = min(max_per_position, max_possible_vol / num_positions)
    return num_positions, lot_size

def find_execution_index(df, signal_index, delay_ms=938):
    """Find the first index where datetime is at least delay_ms milliseconds after the signal time"""
    signal_time = df['datetime'].iloc[signal_index]
    target_time = signal_time + pd.Timedelta(milliseconds=delay_ms)
    future_times = df['datetime'].iloc[signal_index:] >= target_time
    if not any(future_times):
        return None
    return signal_index + future_times.idxmax()

# Precompute signals
if use_ema:
    src = df['close'].ewm(span=3, adjust=False).mean()
else:
    src = df['close']
highest = src.rolling(p).max()
lowest = src.rolling(p).min()
midpoint = (highest + lowest) / 2
tr1 = df['high'] - df['low']
tr2 = (df['high'] - df['close'].shift()).abs()
tr3 = (df['low'] - df['close'].shift()).abs()
tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
atr = tr.rolling(atr_p).mean().shift()
eps = atr * mult

df['signal'] = 0
for i in range(p + 1, len(df)):
    m_prev = midpoint.iat[i - 1]
    e = eps.iat[i]
    if pd.isna(e):
        continue
    if mode == "Type A":
        up = src.iat[i] > m_prev + e
        down = src.iat[i] < m_prev - e
    else:
        up = (src.iat[i - 1] <= m_prev + e and src.iat[i] > m_prev + e)
        down = (src.iat[i - 1] >= m_prev - e and src.iat[i] < m_prev - e)
    if up:
        df.at[i, 'signal'] = 1
    elif down:
        df.at[i, 'signal'] = -1

# Backtest with compounding and volume limits
log_message("Starting Q-Trend compounding backtest with improved risk management...")
equity = 100.0
open_volume = 0
win_streak = 0
loss_streak = 0
trade_outcomes = []
equity_history = [equity]
trades = []
trade_count = 0
win_count = 0
consecutive_loss_pause = 0
i = p + 1

while i < len(df) - 10 and equity >= MIN_EQUITY:
    row = df.iloc[i]
    
    # Skip trading if we're in a consecutive loss pause
    if consecutive_loss_pause > 0:
        consecutive_loss_pause -= 1
        i += 1
        continue
        
    if row['signal'] != 0:
        # Find execution index after 938ms delay
        exec_idx = find_execution_index(df, i)
        if exec_idx is None or exec_idx >= len(df) - 1:
            i += 1
            continue
            
        risk_percentage = calculate_dynamic_risk(equity, equity_history, win_streak, loss_streak, trade_outcomes)
        risk_amount = equity * risk_percentage
        price_risk = 0.2 + SPREAD
        num_positions, lot_size = calculate_position_size(risk_amount, price_risk, MAX_VOL_PER_POS, MAX_TOTAL_VOL, open_volume)
        
        if num_positions > 0 and lot_size >= MIN_VOL:
            entry_row = df.iloc[exec_idx]
            entry_time = entry_row['datetime']
            entry_price = entry_row['close']
            position_type = "LONG" if row['signal'] == 1 else "SHORT"
            position_lot_size = lot_size
            open_volume += position_lot_size
            trade_count += 1
            log_message(f"Trade #{trade_count} executed - {position_type} at {entry_time}, price: {entry_price}, volume: {position_lot_size:.2f}")
            
            tp_bricks = 5
            sl_bricks = 2
            profit = 0
            outcome = None
            exit_price = entry_price
            exit_time = entry_time
            close_signal_idx = None
            
            for j in range(exec_idx + 1, min(exec_idx + 20, len(df))):
                move = df.iloc[j]['direction']
                current_price = df.iloc[j]['close']
                
                if position_type == "LONG":
                    if move == 'up':
                        tp_bricks -= 1
                        if tp_bricks == 0:
                            close_signal_idx = j
                            break
                    elif move == 'down':
                        sl_bricks -= 1
                        if sl_bricks == 0:
                            close_signal_idx = j
                            break
                else:  # SHORT
                    if move == 'down':
                        tp_bricks -= 1
                        if tp_bricks == 0:
                            close_signal_idx = j
                            break
                    elif move == 'up':
                        sl_bricks -= 1
                        if sl_bricks == 0:
                            close_signal_idx = j
                            break
                
                # Time-based exit after 10 bars
                if j >= exec_idx + 10:
                    close_signal_idx = j
                    break
            
            if close_signal_idx is not None:
                # Find execution index for close after 938ms delay
                exit_exec_idx = find_execution_index(df, close_signal_idx)
                if exit_exec_idx is None:
                    exit_exec_idx = close_signal_idx
                
                exit_row = df.iloc[exit_exec_idx]
                exit_price = exit_row['close']
                exit_time = exit_row['datetime']
                
                if position_type == "LONG":
                    profit = (exit_price - entry_price - SPREAD) * 10 * position_lot_size
                else:
                    profit = (entry_price - exit_price - SPREAD) * 10 * position_lot_size
                
                outcome = 'TP' if profit > 0 else 'SL'
            
            if profit == 0:
                if position_type == "LONG":
                    profit = (df.iloc[min(i + 10, len(df) - 1)]['close'] - entry_price) * 10 * position_lot_size
                else:
                    profit = (entry_price - df.iloc[min(i + 10, len(df) - 1)]['close']) * 10 * position_lot_size
                outcome = 'TIME_EXIT'

            equity += profit
            open_volume -= position_lot_size
            trade_outcomes.append(profit)
            equity_history.append(equity)

            if profit > 0:
                win_streak += 1
                loss_streak = 0
                win_count += 1
                consecutive_loss_pause = 0  # Reset pause counter on win
            else:
                win_streak = 0
                loss_streak += 1
                if loss_streak >= MAX_CONSECUTIVE_LOSSES:
                    consecutive_loss_pause = 10  # Pause trading for 10 bars after max consecutive losses
                    log_message(f"Pausing trading for 10 bars after {MAX_CONSECUTIVE_LOSSES} consecutive losses")

            trades.append({
                'entry_time': entry_time,
                'exit_time': exit_time,
                'type': position_type,
                'entry_price': entry_price,
                'exit_price': exit_price,
                'volume': position_lot_size,
                'profit': profit,
                'outcome': outcome,
                'equity': equity
            })

            log_message(f"Trade #{trade_count} closed - {outcome} at {exit_time}, profit: {profit:.2f}, equity: {equity:.2f}")

        i = exec_idx + 1 if exec_idx is not None else i + 1
    else:
        i += 1

# Final statistics
win_rate = win_count / trade_count if trade_count > 0 else 0
total_profit = equity - 100.0
max_drawdown = ((max(equity_history) - min(equity_history))/max(equity_history)) if equity_history else 0

log_message(f"\nBacktest completed:")
log_message(f"Total trades: {trade_count}")
log_message(f"Win rate: {win_rate:.2%}")
log_message(f"Final equity: {equity:.2f}")
log_message(f"Total profit: {total_profit:.2f}")
log_message(f"Maximum drawdown: {max_drawdown:.2%}")
log_message(f"Profit factor: {sum(t for t in trade_outcomes if t > 0)/-sum(t for t in trade_outcomes if t < 0):.2f}" if any(t < 0 for t in trade_outcomes) else "inf")
