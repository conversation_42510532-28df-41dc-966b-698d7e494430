"""
CORRECTED Boom 1000 Index Post-Spike Strategy Backtest
Strategy: SELL ONLY after spikes - expecting price reversion downward
This makes logical sense: after upward spike, price typically reverts down
"""

import pandas as pd
import numpy as np
from datetime import datetime

class SellOnlyBoomBacktester:
    def __init__(self, csv_file, starting_capital=20):
        self.csv_file = csv_file
        self.starting_capital = starting_capital
        self.current_capital = starting_capital
        
        # Boom 1000 Index specifications
        self.min_volume = 0.20  # lots
        self.max_volume = 50.0  # lots
        self.volume_limit = 120.0  # lots for multiple entries
        self.tick_size = 0.001  # 0.001 for Boom 1000
        self.tick_value = 0.001  # $0.001 per tick per lot
        self.spread = 0.0  # Zero spread as specified
        
        # Strategy parameters
        self.collection_window = 100  # ticks to hold position after spike
        
        # Results tracking
        self.trades = []
        self.equity_curve = []
        self.df = None
        self.spikes = []
        
    def load_and_parse_data(self):
        """Load and parse the tick data"""
        print("Loading tick data for SELL-ONLY backtesting...")
        
        raw_df = pd.read_csv(self.csv_file)
        parsed_data = []
        
        for _, row in raw_df.iterrows():
            try:
                tuple_str = row['0']
                if tuple_str.startswith('(') and tuple_str.endswith(')'):
                    values = tuple_str[1:-1].split(', ')
                    
                    tick_data = {
                        'timestamp': int(values[0]),
                        'bid': float(values[1]),
                        'ask': float(values[2]),
                        'last': float(values[3]),
                        'volume': int(values[4]),
                        'timestamp_msc': int(values[5]),
                        'flags': int(values[6]),
                        'volume_real': float(values[7]),
                        'symbol': row['symbol']
                    }
                    parsed_data.append(tick_data)
            except:
                continue
        
        self.df = pd.DataFrame(parsed_data)
        self.df['datetime'] = pd.to_datetime(self.df['timestamp'], unit='s')
        self.df['datetime_msc'] = pd.to_datetime(self.df['timestamp_msc'], unit='ms')
        self.df['mid_price'] = (self.df['bid'] + self.df['ask']) / 2
        self.df = self.df.sort_values('timestamp_msc').reset_index(drop=True)
        
        print(f"Loaded {len(self.df)} ticks for backtesting")
        return self.df
    
    def detect_spikes(self):
        """Detect spikes using the same enhanced method"""
        print("Detecting spikes for SELL-ONLY backtesting...")
        
        # Calculate price changes
        self.df['price_change'] = self.df['mid_price'].diff()
        self.df['price_change_pct'] = (self.df['price_change'] / self.df['mid_price'].shift(1)) * 100
        
        # Rolling statistics
        window = 100
        self.df['rolling_mean'] = self.df['price_change'].rolling(window=window).mean()
        self.df['rolling_std'] = self.df['price_change'].rolling(window=window).std()
        
        # Multiple spike detection methods
        pct_spikes = self.df[
            (self.df['price_change_pct'] > 0.1) & 
            (self.df['price_change'] > 0)
        ].index.tolist()
        
        std_threshold = 2.5
        std_spikes = self.df[
            (self.df['price_change'] > self.df['rolling_mean'] + std_threshold * self.df['rolling_std']) &
            (self.df['price_change'] > 0)
        ].index.tolist()
        
        abs_threshold = 0.5
        abs_spikes = self.df[
            (self.df['price_change'] > abs_threshold)
        ].index.tolist()
        
        # Combine and filter
        all_spike_candidates = list(set(pct_spikes + std_spikes + abs_spikes))
        all_spike_candidates.sort()
        
        filtered_spikes = []
        min_distance = 5
        
        for spike_idx in all_spike_candidates:
            if not filtered_spikes or spike_idx - filtered_spikes[-1] >= min_distance:
                filtered_spikes.append(spike_idx)
        
        self.spikes = filtered_spikes
        self.df['is_spike'] = False
        self.df.loc[self.spikes, 'is_spike'] = True
        
        print(f"Detected {len(self.spikes)} spikes for backtesting")
        return self.spikes
    
    def calculate_position_size(self):
        """Calculate position size based on current capital"""
        # Conservative position sizing - risk 2% of capital per trade
        risk_per_trade = self.current_capital * 0.02
        
        # Estimate average risk per lot (assume 20 ticks max adverse movement)
        estimated_risk_ticks = 20
        risk_per_lot = estimated_risk_ticks * self.tick_value  # $0.02 per lot
        
        if risk_per_lot > 0 and risk_per_trade > 0:
            optimal_lots = risk_per_trade / risk_per_lot
        else:
            optimal_lots = self.min_volume
        
        # Apply constraints
        optimal_lots = max(self.min_volume, min(optimal_lots, self.max_volume))
        optimal_lots = min(optimal_lots, self.volume_limit)
        
        return round(optimal_lots, 2)
    
    def execute_sell_only_strategy(self):
        """Execute SELL-ONLY strategy after each spike"""
        print("Executing SELL-ONLY post-spike strategy...")
        print("Logic: After upward spike, SELL expecting downward reversion")
        
        self.current_capital = self.starting_capital
        self.equity_curve = [self.current_capital]
        
        for spike_idx in self.spikes:
            # Check if we have enough data after the spike
            if spike_idx + self.collection_window >= len(self.df):
                continue
            
            # Entry: Immediately after spike (SELL position)
            entry_idx = spike_idx + 1  # Enter right after spike
            exit_idx = min(spike_idx + self.collection_window, len(self.df) - 1)
            
            # For SELL position: we sell at entry and buy back at exit
            entry_price = self.df.iloc[entry_idx]['ask']  # Sell at ask price
            exit_price = self.df.iloc[exit_idx]['bid']    # Buy back at bid price
            
            # Calculate position size
            position_size = self.calculate_position_size()
            
            # Calculate P&L for SELL position
            # Profit when exit_price < entry_price (price went down as expected)
            price_change = entry_price - exit_price  # Positive when price drops
            ticks_gained = price_change / self.tick_size
            pnl = ticks_gained * self.tick_value * position_size
            
            # Update capital
            self.current_capital += pnl
            
            # Record trade
            trade = {
                'trade_number': len(self.trades) + 1,
                'spike_idx': spike_idx,
                'direction': 'SELL',
                'entry_time': self.df.iloc[entry_idx]['datetime'],
                'exit_time': self.df.iloc[exit_idx]['datetime'],
                'entry_price': entry_price,
                'exit_price': exit_price,
                'position_size': position_size,
                'price_change': price_change,
                'ticks_gained': ticks_gained,
                'pnl': pnl,
                'capital_after': self.current_capital,
                'spike_size': self.df.iloc[spike_idx]['price_change'],
                'spike_pct': self.df.iloc[spike_idx]['price_change_pct'],
                'duration_ticks': exit_idx - entry_idx,
                'reversion_success': price_change > 0  # True if price reverted down as expected
            }
            
            self.trades.append(trade)
            self.equity_curve.append(self.current_capital)
            
            # Print progress every 20 trades
            if len(self.trades) % 20 == 0:
                recent_trades = self.trades[-20:]
                recent_success = sum(1 for t in recent_trades if t['reversion_success'])
                print(f"Completed {len(self.trades)} trades, Capital: ${self.current_capital:.2f}, "
                      f"Recent reversion success: {recent_success}/20 ({recent_success/20*100:.1f}%)")
        
        print(f"SELL-ONLY backtesting completed: {len(self.trades)} trades executed")
        return self.trades
    
    def calculate_statistics(self):
        """Calculate comprehensive backtest statistics"""
        if not self.trades:
            print("No trades to analyze")
            return None
        
        trades_df = pd.DataFrame(self.trades)
        
        # Basic statistics
        total_trades = len(trades_df)
        winning_trades = len(trades_df[trades_df['pnl'] > 0])
        losing_trades = len(trades_df[trades_df['pnl'] < 0])
        
        win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
        
        total_pnl = trades_df['pnl'].sum()
        total_return = ((self.current_capital - self.starting_capital) / self.starting_capital) * 100
        
        avg_win = trades_df[trades_df['pnl'] > 0]['pnl'].mean() if winning_trades > 0 else 0
        avg_loss = trades_df[trades_df['pnl'] < 0]['pnl'].mean() if losing_trades > 0 else 0
        
        max_win = trades_df['pnl'].max()
        max_loss = trades_df['pnl'].min()
        
        # Reversion analysis
        successful_reversions = len(trades_df[trades_df['reversion_success'] == True])
        reversion_rate = (successful_reversions / total_trades) * 100 if total_trades > 0 else 0
        
        # Calculate drawdown
        equity_series = pd.Series(self.equity_curve)
        running_max = equity_series.cummax()
        drawdown = equity_series - running_max
        max_drawdown = drawdown.min()
        max_drawdown_pct = (max_drawdown / running_max.loc[drawdown.idxmin()]) * 100 if len(running_max) > 0 else 0
        
        # Sharpe ratio (simplified)
        returns = trades_df['pnl'] / self.starting_capital
        sharpe_ratio = returns.mean() / returns.std() if returns.std() > 0 else 0
        
        # Spike size analysis
        avg_spike_size = trades_df['spike_size'].mean()
        avg_spike_pct = trades_df['spike_pct'].mean()
        
        stats = {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'reversion_rate': reversion_rate,
            'total_pnl': total_pnl,
            'total_return': total_return,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'max_win': max_win,
            'max_loss': max_loss,
            'max_drawdown': max_drawdown,
            'max_drawdown_pct': max_drawdown_pct,
            'sharpe_ratio': sharpe_ratio,
            'starting_capital': self.starting_capital,
            'final_capital': self.current_capital,
            'avg_spike_size': avg_spike_size,
            'avg_spike_pct': avg_spike_pct
        }
        
        return stats
    
    def print_results(self):
        """Print comprehensive backtest results"""
        stats = self.calculate_statistics()
        if not stats:
            return
        
        print("\n" + "="*80)
        print("BOOM 1000 INDEX SELL-ONLY POST-SPIKE STRATEGY BACKTEST RESULTS")
        print("="*80)
        
        print(f"\n📊 STRATEGY OVERVIEW:")
        print(f"{'Strategy:':<25} SELL-ONLY after spikes (reversion)")
        print(f"{'Starting Capital:':<25} ${stats['starting_capital']:.2f}")
        print(f"{'Final Capital:':<25} ${stats['final_capital']:.2f}")
        print(f"{'Total Return:':<25} {stats['total_return']:.2f}%")
        print(f"{'Total P&L:':<25} ${stats['total_pnl']:.4f}")
        
        print(f"\n📈 TRADE STATISTICS:")
        print(f"{'Total Trades:':<25} {stats['total_trades']}")
        print(f"{'Winning Trades:':<25} {stats['winning_trades']}")
        print(f"{'Losing Trades:':<25} {stats['losing_trades']}")
        print(f"{'Win Rate:':<25} {stats['win_rate']:.2f}%")
        print(f"{'Price Reversion Rate:':<25} {stats['reversion_rate']:.2f}%")
        
        print(f"\n🎯 SPIKE ANALYSIS:")
        print(f"{'Average Spike Size:':<25} {stats['avg_spike_size']:.3f} price units")
        print(f"{'Average Spike %:':<25} {stats['avg_spike_pct']:.3f}%")
        
        print(f"\n💰 P&L ANALYSIS:")
        print(f"{'Average Win:':<25} ${stats['avg_win']:.4f}")
        print(f"{'Average Loss:':<25} ${stats['avg_loss']:.4f}")
        print(f"{'Best Trade:':<25} ${stats['max_win']:.4f}")
        print(f"{'Worst Trade:':<25} ${stats['max_loss']:.4f}")
        
        print(f"\n📉 RISK METRICS:")
        print(f"{'Max Drawdown:':<25} ${stats['max_drawdown']:.4f}")
        print(f"{'Max Drawdown %:':<25} {stats['max_drawdown_pct']:.2f}%")
        print(f"{'Sharpe Ratio:':<25} {stats['sharpe_ratio']:.4f}")
        
        # Sample trades
        if self.trades:
            print(f"\n🔍 SAMPLE TRADES (First 10):")
            for trade in self.trades[:10]:
                reversion_icon = "✅" if trade['reversion_success'] else "❌"
                print(f"Trade {trade['trade_number']} (SELL): "
                      f"{trade['entry_time'].strftime('%m-%d %H:%M:%S')} → "
                      f"{trade['exit_time'].strftime('%H:%M:%S')} | "
                      f"Spike: {trade['spike_pct']:.2f}% | "
                      f"P&L: ${trade['pnl']:.4f} | "
                      f"Reversion: {reversion_icon} | "
                      f"Capital: ${trade['capital_after']:.2f}")
        
        print("\n" + "="*80)
        
        return stats

def main():
    """Run the SELL-ONLY backtest"""
    print("Starting Boom 1000 Index SELL-ONLY Post-Spike Strategy Backtest")
    print("Logic: After every upward spike, SELL immediately expecting price reversion")
    print("Starting Capital: $20")
    
    # Initialize backtester
    csv_file = "C:\\Users\\<USER>\\META\\Boom_1000_Index_7days_20250619_20250626.csv"
    backtester = SellOnlyBoomBacktester(csv_file, starting_capital=20)
    
    # Load data
    df = backtester.load_and_parse_data()
    
    # Detect spikes
    spikes = backtester.detect_spikes()
    
    # Execute SELL-ONLY strategy
    trades = backtester.execute_sell_only_strategy()
    
    # Print results
    results = backtester.print_results()
    
    return backtester, results

if __name__ == "__main__":
    backtester, results = main()
