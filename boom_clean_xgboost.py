"""
CLEAN XGBoost Model for Boom 1000 Index - ZERO LOOK-AHEAD BIAS
Proper time-series machine learning for trading
"""

import pandas as pd
import numpy as np
import xgboost as xgb
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

class CleanBoomML:
    def __init__(self, csv_file):
        self.csv_file = csv_file
        self.df = None
        self.model = None
        self.scaler = StandardScaler()
        
    def load_data(self):
        """Load and parse raw tick data"""
        print("Loading raw tick data...")
        raw_df = pd.read_csv(self.csv_file)
        parsed_data = []
        
        for _, row in raw_df.iterrows():
            try:
                tuple_str = row['0']
                if tuple_str.startswith('(') and tuple_str.endswith(')'):
                    values = tuple_str[1:-1].split(', ')
                    parsed_data.append({
                        'timestamp': int(values[0]),
                        'bid': float(values[1]),
                        'ask': float(values[2]),
                        'mid_price': (float(values[1]) + float(values[2])) / 2
                    })
            except: continue
        
        self.df = pd.DataFrame(parsed_data)
        self.df['datetime'] = pd.to_datetime(self.df['timestamp'], unit='s')
        self.df = self.df.sort_values('timestamp').reset_index(drop=True)
        print(f"Loaded {len(self.df)} ticks")
        
    def create_features(self):
        """Create features using ONLY historical data"""
        print("Creating features (NO future data)...")
        
        # Basic price features
        self.df['price_change'] = self.df['mid_price'].diff()
        self.df['price_change_pct'] = (self.df['price_change'] / self.df['mid_price'].shift(1)) * 100
        
        # Volatility features (looking back)
        for window in [5, 10, 20, 50, 100]:
            self.df[f'vol_{window}'] = self.df['price_change'].rolling(window=window).std()
            self.df[f'mean_{window}'] = self.df['mid_price'].rolling(window=window).mean()
            self.df[f'max_{window}'] = self.df['mid_price'].rolling(window=window).max()
            self.df[f'min_{window}'] = self.df['mid_price'].rolling(window=window).min()
        
        # Momentum features (looking back)
        for period in [3, 5, 10, 20, 50]:
            self.df[f'momentum_{period}'] = self.df['mid_price'] - self.df['mid_price'].shift(period)
            self.df[f'roc_{period}'] = ((self.df['mid_price'] / self.df['mid_price'].shift(period)) - 1) * 100
        
        # RSI (looking back)
        for period in [7, 14, 21]:
            delta = self.df['mid_price'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            self.df[f'rsi_{period}'] = 100 - (100 / (1 + rs))
        
        # Price position in range
        for window in [10, 20, 50]:
            price_range = self.df[f'max_{window}'] - self.df[f'min_{window}']
            self.df[f'position_{window}'] = np.where(
                price_range > 0,
                (self.df['mid_price'] - self.df[f'min_{window}']) / price_range,
                0.5
            )
        
        # Spike detection (historical)
        self.df['is_spike'] = ((self.df['price_change_pct'] > 0.1) & (self.df['price_change'] > 0)).astype(int)
        
        # Ticks since last spike
        spike_indices = self.df[self.df['is_spike'] == 1].index.tolist()
        self.df['ticks_since_spike'] = 0
        
        for i in range(len(self.df)):
            last_spikes = [idx for idx in spike_indices if idx < i]
            if last_spikes:
                self.df.iloc[i, self.df.columns.get_loc('ticks_since_spike')] = i - max(last_spikes)
            else:
                self.df.iloc[i, self.df.columns.get_loc('ticks_since_spike')] = i
        
        # Moving averages and crossovers
        self.df['ma5'] = self.df['mid_price'].rolling(window=5).mean()
        self.df['ma20'] = self.df['mid_price'].rolling(window=20).mean()
        self.df['ma_cross'] = (self.df['ma5'] > self.df['ma20']).astype(int)
        
        # Time features
        self.df['hour'] = self.df['datetime'].dt.hour
        self.df['minute'] = self.df['datetime'].dt.minute
        
        # Price acceleration
        self.df['acceleration'] = self.df['price_change'].diff()
        
        print(f"Created features, total columns: {len(self.df.columns)}")
    
    def create_targets(self):
        """Create target labels WITHOUT look-ahead bias"""
        print("Creating target labels...")
        
        # We'll create targets by looking at what ACTUALLY happened
        # But we'll only use this for model training, not prediction
        targets = []
        
        for i in range(len(self.df) - 50):  # Leave 50 ticks at end
            current_price = self.df.iloc[i]['mid_price']
            
            # Look at next 30 ticks to see what happened
            future_slice = self.df.iloc[i+1:i+31]
            min_future_price = future_slice['mid_price'].min()
            
            # Target: Can we make any profit by selling now?
            profit_pct = (current_price - min_future_price) / current_price
            target = 1 if profit_pct > 0.0 else 0  # Any profit
            targets.append(target)
        
        # Pad with NaN for the last 50 ticks
        targets.extend([np.nan] * 50)
        self.df['target'] = targets
        
        # Show target distribution
        valid_targets = [t for t in targets if not pd.isna(t)]
        positive_rate = sum(valid_targets) / len(valid_targets) * 100
        print(f"Target distribution: {positive_rate:.1f}% profitable opportunities")
        
        return positive_rate > 5  # Need at least 5% positive cases
    
    def prepare_training_data(self):
        """Prepare data for training with proper time-series split"""
        print("Preparing training data...")
        
        # Get feature columns
        feature_cols = [col for col in self.df.columns if col not in [
            'timestamp', 'bid', 'ask', 'mid_price', 'datetime', 'target'
        ]]
        
        # Remove rows with NaN
        clean_data = self.df[feature_cols + ['target']].dropna()
        
        # Time-series split: first 60% train, next 20% validation, last 20% test
        n = len(clean_data)
        train_end = int(n * 0.6)
        val_end = int(n * 0.8)
        
        train_data = clean_data.iloc[:train_end]
        val_data = clean_data.iloc[train_end:val_end]
        test_data = clean_data.iloc[val_end:]
        
        X_train = train_data[feature_cols]
        y_train = train_data['target']
        X_val = val_data[feature_cols]
        y_val = val_data['target']
        X_test = test_data[feature_cols]
        y_test = test_data['target']
        
        print(f"Train: {len(X_train)}, Val: {len(X_val)}, Test: {len(X_test)}")
        print(f"Features: {len(feature_cols)}")
        
        return X_train, X_val, X_test, y_train, y_val, y_test, feature_cols
    
    def train_model(self):
        """Train XGBoost model with proper validation"""
        print("Training XGBoost model...")
        
        X_train, X_val, X_test, y_train, y_val, y_test, feature_cols = self.prepare_training_data()
        
        # Scale features
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_val_scaled = self.scaler.transform(X_val)
        X_test_scaled = self.scaler.transform(X_test)
        
        # Train model with early stopping
        self.model = xgb.XGBClassifier(
            n_estimators=200,
            max_depth=5,
            learning_rate=0.1,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42,
            eval_metric='logloss',
            early_stopping_rounds=10
        )
        
        # Fit with validation set for early stopping
        self.model.fit(
            X_train_scaled, y_train,
            eval_set=[(X_val_scaled, y_val)],
            verbose=False
        )
        
        # Evaluate on test set
        y_pred = self.model.predict(X_test_scaled)
        y_prob = self.model.predict_proba(X_test_scaled)[:, 1]
        
        accuracy = accuracy_score(y_test, y_pred)
        
        print(f"\n📊 MODEL RESULTS:")
        print(f"Test Accuracy: {accuracy:.3f}")
        print(f"\nConfusion Matrix:")
        print(confusion_matrix(y_test, y_pred))
        print(f"\nClassification Report:")
        print(classification_report(y_test, y_pred))
        
        # Feature importance
        importance_df = pd.DataFrame({
            'feature': feature_cols,
            'importance': self.model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        print(f"\n🔥 TOP 15 FEATURES:")
        for i, (_, row) in enumerate(importance_df.head(15).iterrows()):
            print(f"{i+1:2d}. {row['feature']:<20} {row['importance']:.4f}")
        
        # High confidence analysis
        high_conf_mask = y_prob > 0.75
        if high_conf_mask.sum() > 0:
            high_conf_acc = accuracy_score(y_test[high_conf_mask], y_pred[high_conf_mask])
            print(f"\n🎯 HIGH CONFIDENCE (>75%): {high_conf_mask.sum()} samples, {high_conf_acc:.3f} accuracy")
        
        return X_test_scaled, y_test, y_prob, feature_cols
    
    def run_backtest(self):
        """Run realistic backtest on test data"""
        print("\n💰 RUNNING REALISTIC BACKTEST...")
        
        if self.model is None:
            print("No model trained!")
            return
        
        X_test_scaled, y_test, y_prob, feature_cols = self.train_model()
        
        # Only trade high-confidence predictions
        confidence_threshold = 0.75
        trade_signals = y_prob > confidence_threshold
        
        # Get test data indices
        n_total = len(self.df[feature_cols + ['target']].dropna())
        test_start_idx = int(n_total * 0.8)
        
        # Simulate trading
        starting_capital = 20
        position_size = 0.2  # Conservative 0.2 lots
        tick_value = 0.001
        current_capital = starting_capital
        trades = []
        
        clean_df = self.df[feature_cols + ['target', 'mid_price', 'datetime']].dropna()
        
        for i, should_trade in enumerate(trade_signals):
            if should_trade:
                actual_idx = test_start_idx + i
                
                if actual_idx < len(clean_df) - 30:  # Ensure exit data
                    # Entry
                    entry_price = clean_df.iloc[actual_idx]['mid_price']
                    entry_time = clean_df.iloc[actual_idx]['datetime']
                    
                    # Exit after 30 ticks
                    exit_idx = actual_idx + 30
                    exit_price = clean_df.iloc[exit_idx]['mid_price']
                    exit_time = clean_df.iloc[exit_idx]['datetime']
                    
                    # P&L calculation for SELL position
                    price_change = entry_price - exit_price
                    ticks_gained = price_change / 0.001
                    pnl = ticks_gained * tick_value * position_size
                    
                    current_capital += pnl
                    
                    trades.append({
                        'entry_time': entry_time,
                        'entry_price': entry_price,
                        'exit_price': exit_price,
                        'pnl': pnl,
                        'confidence': y_prob[i],
                        'capital': current_capital
                    })
        
        # Results
        if trades:
            trades_df = pd.DataFrame(trades)
            wins = trades_df[trades_df['pnl'] > 0]
            
            print(f"\n📈 BACKTEST RESULTS:")
            print(f"Total trades: {len(trades_df)}")
            print(f"Win rate: {len(wins)/len(trades_df)*100:.1f}% ({len(wins)}/{len(trades_df)})")
            print(f"Starting capital: ${starting_capital:.2f}")
            print(f"Final capital: ${current_capital:.2f}")
            print(f"Total return: {(current_capital-starting_capital)/starting_capital*100:.1f}%")
            print(f"Average trade P&L: ${trades_df['pnl'].mean():.4f}")
            print(f"Best trade: ${trades_df['pnl'].max():.4f}")
            print(f"Worst trade: ${trades_df['pnl'].min():.4f}")
            
            if len(trades_df) > 0:
                print(f"Profit factor: {wins['pnl'].sum() / abs(trades_df[trades_df['pnl'] < 0]['pnl'].sum()) if len(trades_df[trades_df['pnl'] < 0]) > 0 else 'N/A'}")
        else:
            print("No trades executed!")

def main():
    """Run complete clean analysis"""
    print("🧹 CLEAN BOOM 1000 XGBOOST ANALYSIS - ZERO LOOK-AHEAD")
    print("="*60)
    
    csv_file = "C:\\Users\\<USER>\\META\\Boom_1000_Index_7days_20250619_20250626.csv"
    ml = CleanBoomML(csv_file)
    
    # Execute pipeline
    ml.load_data()
    ml.create_features()
    
    if ml.create_targets():
        ml.train_model()
        ml.run_backtest()
    else:
        print("Insufficient profitable opportunities!")
    
    return ml

if __name__ == "__main__":
    analyzer = main()
