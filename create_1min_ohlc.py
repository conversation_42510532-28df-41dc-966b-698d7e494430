oimport MetaTrader5 as mt5
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, timezone
import pytz

def initialize_mt5():
    """Initialize connection to MT5 terminal"""
    if not mt5.initialize():
        print("Failed to initialize MT5")
        return False
    
    # Login to demo account
    authorized = mt5.login(5749910, password="@Ripper25", server="Deriv-Demo")
    if not authorized:
        print("Failed to login to MT5")
        mt5.shutdown()
        return False
    
    print("✅ MT5 connection established")
    print(f"📊 Terminal info: {mt5.terminal_info()}")
    print(f"👤 Account info: {mt5.account_info()}")
    return True

def download_ticks(symbol, days=5):
    """Download tick data for specified number of days"""
    utc_tz = pytz.timezone('UTC')
    end_date = datetime.now(utc_tz)
    start_date = end_date - timedelta(days=days)
    
    print(f"Downloading {symbol} ticks from {start_date} to {end_date}")
    
    ticks = mt5.copy_ticks_range(
        symbol,
        start_date,
        end_date,
        mt5.COPY_TICKS_ALL
    )
    
    if ticks is None:
        print("Failed to download ticks")
        return None
    
    # Convert to DataFrame
    df = pd.DataFrame(ticks)
    df['time'] = pd.to_datetime(df['time'], unit='s')
    return df

def create_1min_ohlc(df):
    """Convert tick data to 1-minute OHLC"""
    # Set time as index for resampling
    df.set_index('time', inplace=True)
    
    # Create OHLC using last price
    ohlc = df['last'].resample('1min').agg({
        'open': 'first',
        'high': 'max',
        'low': 'min',
        'close': 'last'
    }).dropna()
    
    # Add volume
    volume = df['volume'].resample('1min').sum()
    ohlc['volume'] = volume
    
    # Reset index to make time a column
    ohlc.reset_index(inplace=True)
    return ohlc

def get_symbol_name():
    """Get the correct symbol name from available symbols"""
    symbols = mt5.symbols_get()
    if symbols is None:
        print("Failed to get symbols")
        return None
        
    # Print all available symbols for debugging
    print("Available symbols:")
    for symbol in symbols:
        print(f"- {symbol.name}")
        
    # Try to find the Volatility 75 symbol
    for symbol in symbols:
        if "Volatility 75" in symbol.name:
            print(f"Found matching symbol: {symbol.name}")
            return symbol.name
            
    return None

def main():
    # Initialize MT5
    if not initialize_mt5():
        return
    
    try:
        # Get correct symbol name
        symbol = get_symbol_name()
        if symbol is None:
            print("Could not find Volatility 75 symbol")
            return
            
        print(f"Using symbol: {symbol}")
        
        # Download ticks
        ticks_df = download_ticks(symbol)
        
        if ticks_df is not None and not ticks_df.empty:
            print(f"Downloaded {len(ticks_df)} ticks")
            
            # Save raw ticks
            ticks_df.to_csv(f'{symbol.replace(" ", "_")}_ticks.csv', index=False)
            print("✅ Saved raw tick data")
            
            # Create 1-minute OHLC
            ohlc_df = create_1min_ohlc(ticks_df)
            
            # Save OHLC data
            ohlc_df.to_csv(f'{symbol.replace(" ", "_")}_1min.csv', index=False)
            print("✅ Created and saved 1-minute OHLC data")
            
            print(f"Generated {len(ohlc_df)} 1-minute candles")
        
    except Exception as e:
        print(f"Error: {str(e)}")
    
    finally:
        mt5.shutdown()
        print("MT5 connection closed")

if __name__ == "__main__":
    main()
