#!/usr/bin/env python3
"""
Comprehensive Trading Performance Report Generator
Integrates all metrics: Streak Statistics, Drawdown Analysis, Risk of Ruin, 
Monte Carlo Distributions, and Scenario Survival Analysis
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import argparse
import os
from datetime import datetime
from typing import Dict, List, Tuple, Union, Optional
import warnings
warnings.filterwarnings('ignore')

# Import existing analysis modules
from streak_analysis import analyze_win_loss_streaks, generate_streak_report
from monte_carlo_simulation import monte_carlo_simulation
from account_survival_analysis import analyze_monte_carlo_results
from comprehensive_drawdown_analysis import calculate_high_water_marks_and_drawdowns, calculate_risk_of_ruin


class TradingPerformanceReportGenerator:
    """Comprehensive trading performance report generator"""
    
    def __init__(self, trades_df: pd.DataFrame, initial_capital: float = 100000):
        """
        Initialize the report generator
        
        Args:
            trades_df: DataFrame containing trade data
            initial_capital: Starting capital amount
        """
        self.trades_df = trades_df.copy()
        self.initial_capital = initial_capital
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Initialize results storage
        self.streak_results = {}
        self.drawdown_results = {}
        self.ror_results = {}
        self.monte_carlo_results = {}
        self.survival_results = {}
        
        # Ensure required columns exist
        self._prepare_data()
    
    def _prepare_data(self):
        """Prepare and validate trade data"""
        print("📊 Preparing trade data...")
        
        # Ensure we have required columns
        if 'pnl' not in self.trades_df.columns:
            raise ValueError("DataFrame must contain 'pnl' column")
        
        # Create win/loss indicators if they don't exist
        if 'is_winner' not in self.trades_df.columns:
            self.trades_df['is_winner'] = self.trades_df['pnl'] > 0
        
        if 'is_loser' not in self.trades_df.columns:
            self.trades_df['is_loser'] = self.trades_df['pnl'] < 0
        
        # Calculate cumulative capital if not present
        if 'capital' not in self.trades_df.columns:
            self.trades_df['capital'] = self.initial_capital + self.trades_df['pnl'].cumsum()
        
        # Ensure entry_time exists for reporting
        if 'entry_time' not in self.trades_df.columns:
            self.trades_df['entry_time'] = pd.date_range(
                start='2023-01-01', periods=len(self.trades_df), freq='H'
            )
    
    def analyze_streak_statistics(self) -> Dict:
        """Analyze win/loss streak statistics"""
        print("🔥 Analyzing streak statistics...")
        
        self.streak_results = generate_streak_report(
            self.trades_df, 
            win_column='is_winner',
            loss_column='is_loser'
        )
        
        return self.streak_results
    
    def analyze_drawdown(self) -> Dict:
        """Analyze drawdown metrics"""
        print("📉 Analyzing drawdown metrics...")
        
        self.drawdown_results = calculate_high_water_marks_and_drawdowns(self.trades_df)
        
        return self.drawdown_results
    
    def analyze_risk_of_ruin(self) -> Dict:
        """Analyze risk of ruin probability"""
        print("💀 Analyzing risk of ruin...")
        
        self.ror_results = calculate_risk_of_ruin(self.trades_df)
        
        return self.ror_results
    
    def run_monte_carlo_analysis(self, num_simulations: int = 10000) -> Dict:
        """Run Monte Carlo simulation analysis"""
        print(f"🎲 Running Monte Carlo simulation ({num_simulations:,} runs)...")
        
        # Prepare trade returns for Monte Carlo
        trade_returns = self.trades_df['pnl'] / self.initial_capital
        
        # Run Monte Carlo simulation
        mc_results = monte_carlo_simulation(trade_returns.values, num_simulations)
        
        # Convert to more detailed statistics
        final_returns = mc_results['final_return_distribution']
        max_drawdowns = mc_results['max_drawdown_distribution']
        ruin_events = mc_results['ruin_events_distribution']
        
        self.monte_carlo_results = {
            'num_simulations': num_simulations,
            'final_returns': final_returns,
            'max_drawdowns': max_drawdowns,
            'ruin_events': ruin_events,
            'statistics': {
                'positive_returns_pct': (final_returns > 0).mean() * 100,
                'negative_returns_pct': (final_returns < 0).mean() * 100,
                'avg_return': final_returns.mean(),
                'median_return': np.median(final_returns),
                'std_return': final_returns.std(),
                'best_return': final_returns.max(),
                'worst_return': final_returns.min(),
                'avg_max_drawdown': max_drawdowns.mean(),
                'worst_max_drawdown': max_drawdowns.min(),
                'ruin_probability': ruin_events.mean(),
                'survival_probability': 1 - ruin_events.mean()
            }
        }
        
        return self.monte_carlo_results
    
    def analyze_scenario_survival(self) -> Dict:
        """Analyze survival probabilities across different scenarios"""
        print("🛡️ Analyzing scenario survival probabilities...")
        
        # Base survival probability from Monte Carlo
        base_survival = self.monte_carlo_results['statistics']['survival_probability']
        
        # Define scenarios with different risk adjustments
        scenarios = {
            'Conservative (-25% Risk)': {
                'risk_adjustment': -0.25,
                'survival_prob': min(1.0, base_survival + 0.25 * (1 - base_survival))
            },
            'Baseline': {
                'risk_adjustment': 0.0,
                'survival_prob': base_survival
            },
            'Aggressive (+25% Risk)': {
                'risk_adjustment': 0.25,
                'survival_prob': max(0.0, base_survival - 0.25 * base_survival)
            },
            'High Risk (+50% Risk)': {
                'risk_adjustment': 0.50,
                'survival_prob': max(0.0, base_survival - 0.50 * base_survival)
            }
        }
        
        # Create scenario survival table
        scenario_data = []
        for scenario_name, scenario_data_dict in scenarios.items():
            scenario_data.append({
                'Scenario': scenario_name,
                'Risk_Adjustment': f"{scenario_data_dict['risk_adjustment']:+.0%}",
                'Survival_Probability': scenario_data_dict['survival_prob'],
                'Survival_Percentage': f"{scenario_data_dict['survival_prob']*100:.2f}%",
                'Ruin_Probability': 1 - scenario_data_dict['survival_prob'],
                'Ruin_Percentage': f"{(1-scenario_data_dict['survival_prob'])*100:.2f}%"
            })
        
        self.survival_results = {
            'scenarios': scenarios,
            'scenario_table': pd.DataFrame(scenario_data)
        }
        
        return self.survival_results
    
    def create_visualizations(self, save_charts: bool = True) -> Dict[str, str]:
        """Create all visualization charts"""
        print("📊 Creating visualizations...")
        
        chart_files = {}
        
        # Set up the plotting style
        plt.style.use('seaborn-v0_8' if 'seaborn-v0_8' in plt.style.available else 'default')
        
        # 1. Streak Distribution Charts
        chart_files['streak_charts'] = self._create_streak_charts()
        
        # 2. Drawdown Analysis Charts
        chart_files['drawdown_charts'] = self._create_drawdown_charts()
        
        # 3. Monte Carlo Distribution Charts
        chart_files['monte_carlo_charts'] = self._create_monte_carlo_charts()
        
        # 4. Scenario Survival Chart
        chart_files['survival_chart'] = self._create_survival_chart()
        
        # 5. Combined Summary Dashboard
        chart_files['dashboard'] = self._create_summary_dashboard()
        
        return chart_files
    
    def _create_streak_charts(self) -> str:
        """Create streak analysis charts"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # Win streak distribution
        win_dist = self.streak_results['win_streak_distribution']
        if win_dist:
            lengths, counts = zip(*sorted(win_dist.items()))
            ax1.bar(lengths, counts, color='green', alpha=0.7)
            ax1.set_title('Win Streak Distribution')
            ax1.set_xlabel('Streak Length')
            ax1.set_ylabel('Frequency')
            ax1.grid(True, alpha=0.3)
        
        # Loss streak distribution
        loss_dist = self.streak_results['loss_streak_distribution']
        if loss_dist:
            lengths, counts = zip(*sorted(loss_dist.items()))
            ax2.bar(lengths, counts, color='red', alpha=0.7)
            ax2.set_title('Loss Streak Distribution')
            ax2.set_xlabel('Streak Length')
            ax2.set_ylabel('Frequency')
            ax2.grid(True, alpha=0.3)
        
        # Streak timeline
        streak_df = self.streak_results['streak_summary_df']
        if not streak_df.empty:
            win_streaks = streak_df[streak_df['streak_type'] == 'win']
            loss_streaks = streak_df[streak_df['streak_type'] == 'loss']
            
            ax3.scatter(win_streaks['streak_number'], win_streaks['streak_length'], 
                       color='green', alpha=0.7, label='Win Streaks')
            ax3.scatter(loss_streaks['streak_number'], loss_streaks['streak_length'], 
                       color='red', alpha=0.7, label='Loss Streaks')
            ax3.set_title('Streak Timeline')
            ax3.set_xlabel('Streak Number')
            ax3.set_ylabel('Streak Length')
            ax3.legend()
            ax3.grid(True, alpha=0.3)
        
        # Summary statistics
        stats_text = f"""
Longest Win Streak: {self.streak_results['longest_win_streak']} trades
Longest Loss Streak: {self.streak_results['longest_loss_streak']} trades
Average Win Streak: {self.streak_results['average_win_streak']:.1f} trades
Average Loss Streak: {self.streak_results['average_loss_streak']:.1f} trades
Total Streaks: {self.streak_results['total_streaks']}
Win Rate: {self.streak_results['win_rate']:.1f}%
        """
        ax4.text(0.05, 0.95, stats_text.strip(), transform=ax4.transAxes, 
                fontsize=10, verticalalignment='top', fontfamily='monospace')
        ax4.set_title('Streak Statistics Summary')
        ax4.axis('off')
        
        plt.tight_layout()
        filename = f"streak_analysis_{self.timestamp}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()
        
        return filename
    
    def _create_drawdown_charts(self) -> str:
        """Create drawdown analysis charts"""
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
        
        # Equity curve with high-water marks
        ax1.plot(self.trades_df.index, self.trades_df['equity'], 
                label='Equity Curve', linewidth=1, color='blue')
        ax1.plot(self.trades_df.index, self.trades_df['high_water_mark'], 
                label='High Water Mark', linewidth=2, color='green', alpha=0.7)
        ax1.set_title('Equity Curve with High-Water Marks')
        ax1.set_ylabel('Equity ($)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Drawdown over time
        ax2.fill_between(self.trades_df.index, self.trades_df['drawdown_percentage'], 0,
                        color='red', alpha=0.6, label='Drawdown')
        ax2.set_title('Drawdown Over Time')
        ax2.set_xlabel('Trade Number')
        ax2.set_ylabel('Drawdown (%)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # Add max drawdown line
        max_dd = self.drawdown_results['maximum_drawdown']
        ax2.axhline(y=max_dd, color='darkred', linestyle='--', 
                   label=f'Max Drawdown: {max_dd:.2f}%')
        ax2.legend()
        
        plt.tight_layout()
        filename = f"drawdown_analysis_{self.timestamp}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()
        
        return filename
    
    def _create_monte_carlo_charts(self) -> str:
        """Create Monte Carlo distribution charts"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        mc_stats = self.monte_carlo_results['statistics']
        
        # Final returns distribution
        returns = self.monte_carlo_results['final_returns']
        ax1.hist(returns * 100, bins=50, alpha=0.7, color='blue', edgecolor='black')
        ax1.axvline(mc_stats['avg_return'] * 100, color='red', linestyle='--', 
                   label=f'Mean: {mc_stats["avg_return"]*100:.2f}%')
        ax1.axvline(mc_stats['median_return'] * 100, color='orange', linestyle='--', 
                   label=f'Median: {mc_stats["median_return"]*100:.2f}%')
        ax1.set_title('Final Returns Distribution')
        ax1.set_xlabel('Final Return (%)')
        ax1.set_ylabel('Frequency')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Max drawdowns distribution
        drawdowns = self.monte_carlo_results['max_drawdowns']
        ax2.hist(drawdowns * 100, bins=50, alpha=0.7, color='red', edgecolor='black')
        ax2.axvline(mc_stats['avg_max_drawdown'] * 100, color='darkred', linestyle='--',
                   label=f'Mean: {mc_stats["avg_max_drawdown"]*100:.2f}%')
        ax2.set_title('Max Drawdown Distribution')
        ax2.set_xlabel('Max Drawdown (%)')
        ax2.set_ylabel('Frequency')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # Risk/Return scatter
        ax3.scatter(drawdowns * 100, returns * 100, alpha=0.5, color='purple')
        ax3.set_title('Risk vs Return (Monte Carlo)')
        ax3.set_xlabel('Max Drawdown (%)')
        ax3.set_ylabel('Final Return (%)')
        ax3.grid(True, alpha=0.3)
        
        # Monte Carlo statistics summary
        stats_text = f"""
Simulations: {self.monte_carlo_results['num_simulations']:,}
Positive Returns: {mc_stats['positive_returns_pct']:.1f}%
Negative Returns: {mc_stats['negative_returns_pct']:.1f}%
Average Return: {mc_stats['avg_return']*100:.2f}%
Best Case: {mc_stats['best_return']*100:.2f}%
Worst Case: {mc_stats['worst_return']*100:.2f}%
Avg Max Drawdown: {mc_stats['avg_max_drawdown']*100:.2f}%
Worst Drawdown: {mc_stats['worst_max_drawdown']*100:.2f}%
Ruin Probability: {mc_stats['ruin_probability']*100:.2f}%
Survival Probability: {mc_stats['survival_probability']*100:.2f}%
        """
        ax4.text(0.05, 0.95, stats_text.strip(), transform=ax4.transAxes,
                fontsize=10, verticalalignment='top', fontfamily='monospace')
        ax4.set_title('Monte Carlo Statistics')
        ax4.axis('off')
        
        plt.tight_layout()
        filename = f"monte_carlo_analysis_{self.timestamp}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()
        
        return filename
    
    def _create_survival_chart(self) -> str:
        """Create scenario survival chart"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        scenario_table = self.survival_results['scenario_table']
        
        # Survival probabilities
        survival_probs = scenario_table['Survival_Probability'].values * 100
        scenarios = scenario_table['Scenario'].values
        colors = ['green', 'blue', 'orange', 'red']
        
        bars1 = ax1.bar(scenarios, survival_probs, color=colors, alpha=0.7)
        ax1.set_title('Account Survival Probability by Scenario')
        ax1.set_ylabel('Survival Probability (%)')
        ax1.set_ylim(0, 100)
        ax1.grid(True, alpha=0.3)
        plt.setp(ax1.get_xticklabels(), rotation=45, ha='right')
        
        # Add value labels on bars
        for bar, prob in zip(bars1, survival_probs):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{prob:.1f}%', ha='center', va='bottom', fontweight='bold')
        
        # Ruin probabilities
        ruin_probs = scenario_table['Ruin_Probability'].values * 100
        bars2 = ax2.bar(scenarios, ruin_probs, color=colors, alpha=0.7)
        ax2.set_title('Account Ruin Probability by Scenario')
        ax2.set_ylabel('Ruin Probability (%)')
        ax2.set_ylim(0, 100)
        ax2.grid(True, alpha=0.3)
        plt.setp(ax2.get_xticklabels(), rotation=45, ha='right')
        
        # Add value labels on bars
        for bar, prob in zip(bars2, ruin_probs):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{prob:.1f}%', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        filename = f"scenario_survival_{self.timestamp}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()
        
        return filename
    
    def _create_summary_dashboard(self) -> str:
        """Create comprehensive summary dashboard"""
        fig = plt.figure(figsize=(20, 16))
        
        # Create a 4x4 grid for the dashboard
        gs = fig.add_gridspec(4, 4, hspace=0.3, wspace=0.3)
        
        # Key metrics summary (top row)
        ax_summary = fig.add_subplot(gs[0, :])
        ax_summary.axis('off')
        
        total_trades = len(self.trades_df)
        total_pnl = self.trades_df['pnl'].sum()
        win_rate = self.streak_results['win_rate']
        max_dd = self.drawdown_results['maximum_drawdown']
        ror = self.ror_results['risk_of_ruin']
        mc_survival = self.monte_carlo_results['statistics']['survival_probability']
        
        summary_text = f"""
TRADING PERFORMANCE DASHBOARD - {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

KEY METRICS:
• Total Trades: {total_trades:,} | Total P&L: ${total_pnl:,.2f} | Win Rate: {win_rate:.1f}%
• Max Drawdown: {max_dd:.2f}% | Risk of Ruin: {ror:.6f} ({ror*100:.4f}%) | MC Survival: {mc_survival*100:.1f}%
• Longest Win Streak: {self.streak_results['longest_win_streak']} | Longest Loss Streak: {self.streak_results['longest_loss_streak']}
        """
        
        ax_summary.text(0.5, 0.5, summary_text.strip(), transform=ax_summary.transAxes,
                       fontsize=14, fontweight='bold', ha='center', va='center',
                       bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8))
        
        # Equity curve (second row, full width)
        ax_equity = fig.add_subplot(gs[1, :])
        ax_equity.plot(self.trades_df.index, self.trades_df['equity'], 
                      label='Equity', linewidth=1, color='blue')
        ax_equity.fill_between(self.trades_df.index, 
                              self.trades_df['equity'] + self.trades_df['drawdown_absolute'], 
                              self.trades_df['equity'], 
                              color='red', alpha=0.3, label='Drawdown')
        ax_equity.set_title('Equity Curve with Drawdown Visualization')
        ax_equity.set_ylabel('Equity ($)')
        ax_equity.legend()
        ax_equity.grid(True, alpha=0.3)
        
        # Monte Carlo returns (third row, left)
        ax_mc_returns = fig.add_subplot(gs[2, :2])
        returns = self.monte_carlo_results['final_returns']
        ax_mc_returns.hist(returns * 100, bins=30, alpha=0.7, color='green', edgecolor='black')
        ax_mc_returns.axvline(np.mean(returns) * 100, color='red', linestyle='--', 
                             label=f'Mean: {np.mean(returns)*100:.2f}%')
        ax_mc_returns.set_title('Monte Carlo Final Returns')
        ax_mc_returns.set_xlabel('Return (%)')
        ax_mc_returns.legend()
        ax_mc_returns.grid(True, alpha=0.3)
        
        # Scenario survival (third row, right)
        ax_scenarios = fig.add_subplot(gs[2, 2:])
        scenario_table = self.survival_results['scenario_table']
        survival_probs = scenario_table['Survival_Probability'].values * 100
        scenarios = [s.replace(' ', '\n') for s in scenario_table['Scenario'].values]
        colors = ['green', 'blue', 'orange', 'red']
        
        bars = ax_scenarios.bar(scenarios, survival_probs, color=colors, alpha=0.7)
        ax_scenarios.set_title('Scenario Survival Probabilities')
        ax_scenarios.set_ylabel('Survival (%)')
        ax_scenarios.grid(True, alpha=0.3)
        
        for bar, prob in zip(bars, survival_probs):
            height = bar.get_height()
            ax_scenarios.text(bar.get_x() + bar.get_width()/2., height + 1,
                             f'{prob:.1f}%', ha='center', va='bottom', fontsize=8)
        
        # Streak distribution (bottom left)
        ax_streaks = fig.add_subplot(gs[3, :2])
        win_dist = self.streak_results['win_streak_distribution']
        loss_dist = self.streak_results['loss_streak_distribution']
        
        if win_dist and loss_dist:
            all_lengths = sorted(set(list(win_dist.keys()) + list(loss_dist.keys())))
            win_counts = [win_dist.get(l, 0) for l in all_lengths]
            loss_counts = [loss_dist.get(l, 0) for l in all_lengths]
            
            width = 0.35
            x = np.arange(len(all_lengths))
            ax_streaks.bar(x - width/2, win_counts, width, label='Win Streaks', 
                          color='green', alpha=0.7)
            ax_streaks.bar(x + width/2, loss_counts, width, label='Loss Streaks', 
                          color='red', alpha=0.7)
            ax_streaks.set_title('Streak Length Distribution')
            ax_streaks.set_xlabel('Streak Length')
            ax_streaks.set_ylabel('Frequency')
            ax_streaks.set_xticks(x)
            ax_streaks.set_xticklabels(all_lengths)
            ax_streaks.legend()
            ax_streaks.grid(True, alpha=0.3)
        
        # Risk metrics table (bottom right)
        ax_risk = fig.add_subplot(gs[3, 2:])
        ax_risk.axis('off')
        
        risk_text = f"""
RISK METRICS SUMMARY

Drawdown Analysis:
• Maximum: {max_dd:.2f}%
• Duration: {self.drawdown_results['max_drawdown_duration']} trades
• Periods: {self.drawdown_results['total_drawdown_periods']}

Risk of Ruin:
• Probability: {ror:.6f} ({ror*100:.4f}%)
• Win Rate: {self.ror_results['win_probability']*100:.2f}%
• Unit Risk: ${self.ror_results['unit_risk']:.2f}

Monte Carlo ({self.monte_carlo_results['num_simulations']:,} runs):
• Survival: {mc_survival*100:.1f}%
• Avg Return: {self.monte_carlo_results['statistics']['avg_return']*100:.2f}%
• Best Case: {self.monte_carlo_results['statistics']['best_return']*100:.2f}%
• Worst Case: {self.monte_carlo_results['statistics']['worst_return']*100:.2f}%
        """
        
        ax_risk.text(0.05, 0.95, risk_text.strip(), transform=ax_risk.transAxes,
                    fontsize=10, verticalalignment='top', fontfamily='monospace')
        
        plt.suptitle('Trading Performance Analysis Dashboard', fontsize=16, fontweight='bold')
        
        filename = f"performance_dashboard_{self.timestamp}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()
        
        return filename
    
    def generate_markdown_report(self) -> str:
        """Generate comprehensive Markdown report"""
        print("📝 Generating Markdown report...")
        
        filename = f"trading_performance_report_{self.timestamp}.md"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(self._generate_markdown_content())
        
        return filename
    
    def _generate_markdown_content(self) -> str:
        """Generate the markdown content for the report"""
        
        total_trades = len(self.trades_df)
        total_pnl = self.trades_df['pnl'].sum()
        
        content = f"""# Trading Performance Analysis Report

**Generated:** {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}  
**Analysis Period:** {self.trades_df['entry_time'].min()} to {self.trades_df['entry_time'].max()}  
**Total Trades:** {total_trades:,}  
**Total P&L:** ${total_pnl:,.2f}  

---

## 1. Streak Statistics

### Summary
- **Longest Win Streak:** {self.streak_results['longest_win_streak']} trades
- **Longest Loss Streak:** {self.streak_results['longest_loss_streak']} trades
- **Average Win Streak:** {self.streak_results['average_win_streak']:.1f} trades
- **Average Loss Streak:** {self.streak_results['average_loss_streak']:.1f} trades
- **Total Streaks:** {self.streak_results['total_streaks']}
- **Win Rate:** {self.streak_results['win_rate']:.1f}%

### Win Streak Distribution
| Streak Length | Frequency | Percentage |
|---------------|-----------|------------|
"""
        
        # Add win streak distribution table
        win_dist = self.streak_results['win_streak_distribution']
        total_win_streaks = sum(win_dist.values()) if win_dist else 0
        
        if win_dist:
            for length in sorted(win_dist.keys()):
                frequency = win_dist[length]
                percentage = (frequency / total_win_streaks) * 100
                content += f"| {length} | {frequency} | {percentage:.1f}% |\n"
        
        content += f"""
### Loss Streak Distribution
| Streak Length | Frequency | Percentage |
|---------------|-----------|------------|
"""
        
        # Add loss streak distribution table
        loss_dist = self.streak_results['loss_streak_distribution']
        total_loss_streaks = sum(loss_dist.values()) if loss_dist else 0
        
        if loss_dist:
            for length in sorted(loss_dist.keys()):
                frequency = loss_dist[length]
                percentage = (frequency / total_loss_streaks) * 100
                content += f"| {length} | {frequency} | {percentage:.1f}% |\n"
        
        content += f"""

---

## 2. Drawdown Analysis

### Key Metrics
- **Maximum Drawdown:** {self.drawdown_results['maximum_drawdown']:.2f}%
- **Maximum Drawdown Duration:** {self.drawdown_results['max_drawdown_duration']} trades
- **Average Drawdown Duration:** {self.drawdown_results['avg_drawdown_duration']:.1f} trades
- **Total Drawdown Periods:** {self.drawdown_results['total_drawdown_periods']}

### Drawdown Periods
"""
        
        if self.drawdown_results['drawdown_periods']:
            content += "| Period | Duration (Trades) |\n|--------|------------------|\n"
            for i, duration in enumerate(self.drawdown_results['drawdown_periods'], 1):
                content += f"| {i} | {duration} |\n"
        
        content += f"""

---

## 3. Risk of Ruin Analysis

### Formula Used
```
P(ruin) = ((q/p)^(capital/unit)) / (1-((q/p)^(capital/unit)))
```
Where:
- p = probability of winning a trade
- q = probability of losing a trade = 1-p  
- capital = total trading capital
- unit = risk per trade (average loss amount)

### Results
- **Risk of Ruin Probability:** {self.ror_results['risk_of_ruin']:.6f} ({self.ror_results['risk_of_ruin']*100:.4f}%)
- **Win Probability (p):** {self.ror_results['win_probability']:.4f}
- **Loss Probability (q):** {self.ror_results['loss_probability']:.4f}
- **Capital:** ${self.ror_results['capital']:,.2f}
- **Unit Risk:** ${self.ror_results['unit_risk']:.2f}
- **Total Trades:** {self.ror_results['total_trades']:,}
- **Wins:** {self.ror_results['wins']:,}
- **Losses:** {self.ror_results['losses']:,}

---

## 4. Monte Carlo Simulation Results

### Configuration
- **Number of Simulations:** {self.monte_carlo_results['num_simulations']:,}

### Return Distribution
- **Positive Returns:** {self.monte_carlo_results['statistics']['positive_returns_pct']:.1f}%
- **Negative Returns:** {self.monte_carlo_results['statistics']['negative_returns_pct']:.1f}%
- **Average Return:** {self.monte_carlo_results['statistics']['avg_return']*100:.2f}%
- **Median Return:** {self.monte_carlo_results['statistics']['median_return']*100:.2f}%
- **Standard Deviation:** {self.monte_carlo_results['statistics']['std_return']*100:.2f}%
- **Best Case Return:** {self.monte_carlo_results['statistics']['best_return']*100:.2f}%
- **Worst Case Return:** {self.monte_carlo_results['statistics']['worst_return']*100:.2f}%

### Drawdown Distribution
- **Average Max Drawdown:** {self.monte_carlo_results['statistics']['avg_max_drawdown']*100:.2f}%
- **Worst Max Drawdown:** {self.monte_carlo_results['statistics']['worst_max_drawdown']*100:.2f}%

### Risk Metrics
- **Ruin Probability:** {self.monte_carlo_results['statistics']['ruin_probability']*100:.2f}%
- **Survival Probability:** {self.monte_carlo_results['statistics']['survival_probability']*100:.2f}%

### Percentile Analysis
"""
        
        returns = self.monte_carlo_results['final_returns']
        percentiles = [1, 5, 10, 25, 50, 75, 90, 95, 99]
        
        content += "| Percentile | Return |\n|------------|--------|\n"
        for p in percentiles:
            value = np.percentile(returns, p)
            content += f"| {p}th | {value*100:.2f}% |\n"
        
        content += f"""

---

## 5. Scenario Survival Analysis

### Survival Probabilities by Risk Scenario
"""
        
        scenario_table = self.survival_results['scenario_table']
        content += "| Scenario | Risk Adjustment | Survival Probability | Ruin Probability |\n"
        content += "|----------|----------------|---------------------|------------------|\n"
        
        for _, row in scenario_table.iterrows():
            content += f"| {row['Scenario']} | {row['Risk_Adjustment']} | {row['Survival_Percentage']} | {row['Ruin_Percentage']} |\n"
        
        content += f"""

### Scenario Definitions
- **Conservative (-25% Risk):** Reduced volatility and risk exposure
- **Baseline:** Current system performance based on historical data
- **Aggressive (+25% Risk):** Increased volatility and risk exposure  
- **High Risk (+50% Risk):** Significantly increased risk profile

---

## Summary and Conclusions

### System Performance Overview
This trading system demonstrates {'excellent' if self.streak_results['win_rate'] > 70 else 'good' if self.streak_results['win_rate'] > 60 else 'moderate'} performance with a {self.streak_results['win_rate']:.1f}% win rate across {total_trades:,} trades.

### Key Strengths
- Maximum drawdown of {self.drawdown_results['maximum_drawdown']:.2f}% is {'excellent' if abs(self.drawdown_results['maximum_drawdown']) < 10 else 'acceptable' if abs(self.drawdown_results['maximum_drawdown']) < 20 else 'concerning'}
- Risk of ruin probability is extremely low at {self.ror_results['risk_of_ruin']*100:.4f}%
- Monte Carlo analysis shows {self.monte_carlo_results['statistics']['survival_probability']*100:.1f}% survival probability
- Consistent streak patterns with manageable loss streaks

### Risk Considerations
- Longest loss streak of {self.streak_results['longest_loss_streak']} trades requires adequate capital buffer
- {'Conservative risk management recommended' if self.monte_carlo_results['statistics']['ruin_probability'] > 0.05 else 'Risk levels appear well-controlled'}
- Monitor performance under different market conditions as modeled in scenario analysis

### Recommendations
1. **Capital Management:** Maintain sufficient capital to handle drawdown periods
2. **Risk Monitoring:** Regular assessment of streak patterns and drawdown metrics
3. **Scenario Planning:** Prepare for different risk environments as outlined in survival analysis
4. **Performance Tracking:** Continue monitoring against Monte Carlo projections

---

*Report generated using comprehensive trading performance analysis framework*
*Charts and detailed visualizations available in accompanying PNG files*
"""
        
        return content
    
    def export_csv_data(self) -> Dict[str, str]:
        """Export detailed data to CSV files"""
        print("💾 Exporting CSV data...")
        
        csv_files = {}
        
        # 1. Enhanced trade data with all calculated metrics
        enhanced_trades = self.trades_df.copy()
        csv_files['trades'] = f"trades_detailed_{self.timestamp}.csv"
        enhanced_trades.to_csv(csv_files['trades'], index=False)
        
        # 2. Streak analysis data
        if not self.streak_results['streak_summary_df'].empty:
            csv_files['streaks'] = f"streak_analysis_{self.timestamp}.csv"
            self.streak_results['streak_summary_df'].to_csv(csv_files['streaks'], index=False)
        
        # 3. Monte Carlo results
        mc_data = pd.DataFrame({
            'simulation': range(len(self.monte_carlo_results['final_returns'])),
            'final_return': self.monte_carlo_results['final_returns'],
            'max_drawdown': self.monte_carlo_results['max_drawdowns'],
            'ruin_event': self.monte_carlo_results['ruin_events']
        })
        csv_files['monte_carlo'] = f"monte_carlo_results_{self.timestamp}.csv"
        mc_data.to_csv(csv_files['monte_carlo'], index=False)
        
        # 4. Scenario survival data
        csv_files['scenarios'] = f"scenario_survival_{self.timestamp}.csv"
        self.survival_results['scenario_table'].to_csv(csv_files['scenarios'], index=False)
        
        # 5. Summary statistics
        summary_data = {
            'Metric': [
                'Total Trades', 'Total PnL', 'Win Rate (%)', 'Max Drawdown (%)',
                'Risk of Ruin', 'Longest Win Streak', 'Longest Loss Streak',
                'MC Survival Probability (%)', 'MC Average Return (%)'
            ],
            'Value': [
                len(self.trades_df),
                self.trades_df['pnl'].sum(),
                self.streak_results['win_rate'],
                self.drawdown_results['maximum_drawdown'],
                self.ror_results['risk_of_ruin'],
                self.streak_results['longest_win_streak'],
                self.streak_results['longest_loss_streak'],
                self.monte_carlo_results['statistics']['survival_probability'] * 100,
                self.monte_carlo_results['statistics']['avg_return'] * 100
            ]
        }
        summary_df = pd.DataFrame(summary_data)
        csv_files['summary'] = f"performance_summary_{self.timestamp}.csv"
        summary_df.to_csv(csv_files['summary'], index=False)
        
        return csv_files
    
    def run_complete_analysis(self, num_simulations: int = 10000, 
                             generate_charts: bool = True, 
                             export_csv: bool = True) -> Dict[str, str]:
        """Run the complete analysis pipeline"""
        print("🚀 Starting comprehensive trading performance analysis...")
        print("=" * 70)
        
        # Run all analyses
        self.analyze_streak_statistics()
        self.analyze_drawdown()
        self.analyze_risk_of_ruin()
        self.run_monte_carlo_analysis(num_simulations)
        self.analyze_scenario_survival()
        
        # Generate outputs
        output_files = {}
        
        # Generate markdown report
        output_files['report'] = self.generate_markdown_report()
        
        # Generate visualizations
        if generate_charts:
            chart_files = self.create_visualizations()
            output_files.update(chart_files)
        
        # Export CSV data
        if export_csv:
            csv_files = self.export_csv_data()
            output_files.update(csv_files)
        
        print("\n" + "=" * 70)
        print("✅ Analysis complete! Generated files:")
        for file_type, filename in output_files.items():
            print(f"  📄 {file_type}: {filename}")
        print("=" * 70)
        
        return output_files


def load_sample_data() -> pd.DataFrame:
    """Load sample trading data for demonstration"""
    np.random.seed(42)  # For reproducible results
    
    num_trades = 1000
    
    # Generate realistic trading data with high win rate but occasional larger losses
    data = {
        'entry_time': pd.date_range(start='2023-01-01', periods=num_trades, freq='H'),
        'exit_time': pd.date_range(start='2023-01-01 01:00:00', periods=num_trades, freq='H'),
        'entry_price': np.random.uniform(1.0, 1.5, num_trades),
        'exit_price': np.random.uniform(1.0, 1.5, num_trades),
    }
    
    # Create realistic P&L with 90% win rate but asymmetric risk/reward
    pnl = []
    for _ in range(num_trades):
        if np.random.random() < 0.90:  # 90% win rate
            pnl.append(np.random.uniform(5, 50))  # Small wins
        else:
            pnl.append(np.random.uniform(-200, -20))  # Larger losses
    
    data['pnl'] = pnl
    
    return pd.DataFrame(data)


def main():
    """Main function with CLI argument parsing"""
    parser = argparse.ArgumentParser(
        description="Comprehensive Trading Performance Report Generator"
    )
    
    parser.add_argument(
        '--data', 
        type=str, 
        help='Path to CSV file containing trade data'
    )
    
    parser.add_argument(
        '--report', 
        action='store_true',
        help='Generate markdown report and save as report.md'
    )
    
    parser.add_argument(
        '--csv', 
        action='store_true',
        help='Export detailed CSV files'
    )
    
    parser.add_argument(
        '--charts', 
        action='store_true',
        help='Generate visualization charts'
    )
    
    parser.add_argument(
        '--simulations', 
        type=int, 
        default=10000,
        help='Number of Monte Carlo simulations (default: 10000)'
    )
    
    parser.add_argument(
        '--capital', 
        type=float, 
        default=100000,
        help='Initial capital amount (default: 100000)'
    )
    
    parser.add_argument(
        '--demo', 
        action='store_true',
        help='Run with sample demonstration data'
    )
    
    args = parser.parse_args()
    
    # Load data
    if args.demo:
        print("📊 Loading demonstration data...")
        trades_df = load_sample_data()
    elif args.data:
        print(f"📊 Loading data from {args.data}...")
        try:
            trades_df = pd.read_csv(args.data)
        except Exception as e:
            print(f"❌ Error loading data: {e}")
            return
    else:
        print("❌ Please provide --data path or use --demo for sample data")
        return
    
    # Initialize report generator
    report_gen = TradingPerformanceReportGenerator(trades_df, args.capital)
    
    # Run analysis
    output_files = report_gen.run_complete_analysis(
        num_simulations=args.simulations,
        generate_charts=args.charts,
        export_csv=args.csv
    )
    
    # Handle --report flag (copy main report to report.md)
    if args.report and 'report' in output_files:
        import shutil
        shutil.copy(output_files['report'], 'report.md')
        print("📝 Main report copied to report.md")


if __name__ == "__main__":
    main()
