import numpy as np
import pandas as pd
from typing import List, Dict, TypedDict, Optional
from dataclasses import dataclass
import talib
from sklearn.preprocessing import StandardScaler
import logging
import math

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class Settings:
    source: str = 'close'
    neighbors_count: int = 8
    max_bars_back: int = 1000
    feature_count: int = 5
    color_compression: int = 1
    show_exits: bool = True
    use_dynamic_exits: bool = True

@dataclass
class FilterSettings:
    use_volatility_filter: bool = True
    use_regime_filter: bool = True
    use_adx_filter: bool = True
    regime_threshold: float = 0.0
    adx_threshold: int = 20

class FeatureSeries:
    def __init__(self):
        self.f1: List[float] = []
        self.f2: List[float] = []
        self.f3: List[float] = []
        self.f4: List[float] = []
        self.f5: List[float] = []

class MLModel:
    def __init__(self):
        self.first_bar_index: int = 0
        self.training_labels: List[int] = []
        self.loop_size: int = 0
        self.last_distance: float = 0.0
        self.distances_array: List[float] = []
        self.predictions_array: List[int] = []
        self.prediction: int = 0

class LorentzianClassifier:
    def __init__(self, settings: Settings = Settings(), filter_settings: FilterSettings = FilterSettings()):
        self.settings = settings
        self.filter_settings = filter_settings
        self.feature_series = FeatureSeries()
        self.ml_model = MLModel()
        self.scaler = StandardScaler()
        
    def normalize_feature(self, feature: List[float]) -> List[float]:
        """Normalize a feature series to range [0, 1]"""
        feature_arr = np.array(feature).reshape(-1, 1)
        return self.scaler.fit_transform(feature_arr).flatten().tolist()

    def calculate_rsi(self, prices: List[float], period: int = 14) -> List[float]:
        """Calculate RSI indicator"""
        return talib.RSI(np.array(prices), timeperiod=period)

    def calculate_wt(self, hlc3: List[float], n1: int = 10, n2: int = 21) -> List[float]:
        """Calculate Wave Trend indicator"""
        ema1 = talib.EMA(np.array(hlc3), timeperiod=n1)
        ema2 = talib.EMA(np.array(hlc3), timeperiod=n2)
        return (ema1 - ema2).tolist()

    def calculate_cci(self, high: List[float], low: List[float], close: List[float], period: int = 20) -> List[float]:
        """Calculate CCI indicator"""
        return talib.CCI(np.array(high), np.array(low), np.array(close), timeperiod=period)

    def calculate_adx(self, high: List[float], low: List[float], close: List[float], period: int = 14) -> List[float]:
        """Calculate ADX indicator"""
        return talib.ADX(np.array(high), np.array(low), np.array(close), timeperiod=period)

    def get_lorentzian_distance(self, current_features: List[float], historical_features: List[List[float]]) -> List[float]:
        """Calculate Lorentzian distance between current and historical features"""
        distances = []
        for hist_feature_set in historical_features:
            distance = sum(math.log(1 + abs(curr - hist)) 
                         for curr, hist in zip(current_features, hist_feature_set))
            distances.append(distance)
        return distances

    def predict(self, features: List[float], historical_features: List[List[float]], 
                historical_labels: List[int]) -> int:
        """Make prediction using K-nearest neighbors with Lorentzian distance"""
        distances = self.get_lorentzian_distance(features, historical_features)
        
        # Get k nearest neighbors
        k = min(self.settings.neighbors_count, len(historical_features) - 1)
        if k <= 0:
            return 0 # Not enough data to make a prediction
        nearest_indices = np.argpartition(distances, k)[:k]
        
        # Get the labels of k nearest neighbors
        nearest_labels = [historical_labels[i] for i in nearest_indices]
        
        # Return the majority vote
        return max(set(nearest_labels), key=nearest_labels.count)

    def process_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Process market data and generate predictions"""
        # Calculate HLC3
        df['hlc3'] = (df['high'] + df['low'] + df['close']) / 3

        # Calculate features
        df['rsi'] = self.normalize_feature(self.calculate_rsi(df['close'].tolist()))
        df['wt'] = self.normalize_feature(self.calculate_wt(df['hlc3'].tolist()))
        df['cci'] = self.normalize_feature(self.calculate_cci(
            df['high'].tolist(), df['low'].tolist(), df['close'].tolist()))
        df['adx'] = self.normalize_feature(self.calculate_adx(
            df['high'].tolist(), df['low'].tolist(), df['close'].tolist()))
        
        # Create feature arrays
        features_list = []
        labels = []
        
        lookback = self.settings.max_bars_back
        for i in range(lookback, len(df)):
            current_features = [
                df['rsi'].iloc[i],
                df['wt'].iloc[i],
                df['cci'].iloc[i],
                df['adx'].iloc[i]
            ]
            
            # Generate labels (this is a simplified version - you might want to adjust this)
            if df['close'].iloc[i] > df['close'].iloc[i-1]:
                label = 1  # Bullish
            elif df['close'].iloc[i] < df['close'].iloc[i-1]:
                label = -1  # Bearish
            else:
                label = 0  # Neutral
                
            features_list.append(current_features)
            labels.append(label)
            
            if i > lookback:
                prediction = self.predict(
                    current_features,
                    features_list[:-1],
                    labels[:-1]
                )
                df.loc[df.index[i], 'prediction'] = prediction

        return df

    def apply_filters(self, df: pd.DataFrame) -> pd.DataFrame:
        """Apply trading filters"""
        if self.filter_settings.use_volatility_filter:
            df['volatility'] = df['close'].rolling(window=20).std()
            df['volatility_filter'] = df['volatility'] > df['volatility'].rolling(window=100).mean()

        if self.filter_settings.use_regime_filter:
            df['regime_filter'] = df['close'].rolling(window=200).mean().diff() > self.filter_settings.regime_threshold

        if self.filter_settings.use_adx_filter:
            df['adx_filter'] = df['adx'] > self.filter_settings.adx_threshold

        # Combine all filters
        df['valid_signal'] = True
        if self.filter_settings.use_volatility_filter:
            df['valid_signal'] &= df['volatility_filter']
        if self.filter_settings.use_regime_filter:
            df['valid_signal'] &= df['regime_filter']
        if self.filter_settings.use_adx_filter:
            df['valid_signal'] &= df['adx_filter']

        return df

    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """Generate trading signals based on predictions and filters"""
        df = self.process_data(df)
        df = self.apply_filters(df)
        
        # Generate signals only where filters allow
        df['signal'] = 0
        df.loc[(df['prediction'] == 1) & (df['valid_signal']), 'signal'] = 1
        df.loc[(df['prediction'] == -1) & (df['valid_signal']), 'signal'] = -1
        
        return df

def backtest_strategy(df: pd.DataFrame, settings: Settings, filter_settings: FilterSettings) -> Optional[Dict]:
    """
    Backtests a trading strategy based on the Lorentzian Classifier.

    Args:
        df: A pandas DataFrame with OHLC data.
        settings: An object containing the settings for the classifier.
        filter_settings: An object containing the settings for the filters.

    Returns:
        A dictionary with the backtest results, or None if no trades were made.
    """
    classifier = LorentzianClassifier(settings, filter_settings)
    results_df = classifier.generate_signals(df.copy())
    
    initial_balance = 10000.0
    balance = initial_balance
    position = 0  # 0: no position, 1: long, -1: short
    trades = []
    entry_price = 0
    entry_time = None
    
    for i in range(1, len(results_df)):
        signal = results_df['signal'].iloc[i]
        
        if position == 0 and signal != 0:  # Open a new position
            position = signal
            entry_price = results_df['close'].iloc[i]
            entry_time = results_df.index[i]
            
        elif (position == 1 and signal == -1) or (position == -1 and signal == 1):  # Close and reverse
            exit_price = results_df['close'].iloc[i]
            exit_time = results_df.index[i]
            
            if position == 1:
                profit = (exit_price - entry_price) / entry_price
            else:
                profit = (entry_price - exit_price) / entry_price
                
            balance *= (1 + profit)
            trades.append({
                'entry_time': entry_time,
                'exit_time': exit_time,
                'profit': profit,
                'balance': balance
            })
            
            # Reverse position
            position = signal
            entry_price = exit_price
            entry_time = exit_time

    if not trades:
        return None

    trade_df = pd.DataFrame(trades)
    win_rate = (trade_df['profit'] > 0).mean()
    
    return {
        'neighbors_count': settings.neighbors_count,
        'max_bars_back': settings.max_bars_back,
        'use_volatility_filter': filter_settings.use_volatility_filter,
        'use_regime_filter': filter_settings.use_regime_filter,
        'use_adx_filter': filter_settings.use_adx_filter,
        'regime_threshold': filter_settings.regime_threshold,
        'adx_threshold': filter_settings.adx_threshold,
        'total_trades': len(trades),
        'win_rate': win_rate,
        'final_balance': balance,
        'profit_factor': trade_df[trade_df['profit'] > 0]['profit'].sum() / abs(trade_df[trade_df['profit'] < 0]['profit'].sum()) if trade_df[trade_df['profit'] < 0]['profit'].sum() != 0 else float('inf')
    }

def main():
    import itertools

    # Load and prepare data
    file_path = 'stpRNG_90days_ticks.csv'
    logger.info(f"Loading data from {file_path}...")
    df = pd.read_csv(file_path)
    df['datetime'] = pd.to_datetime(df['datetime'])
    df.set_index('datetime', inplace=True)
    logger.info("Resampling data to 1-minute OHLC bars...")
    ohlc_df = df['price'].resample('1min').ohlc()
    ohlc_df.dropna(inplace=True) # Remove intervals with no ticks
    ohlc_df.rename(columns={'open': 'open', 'high': 'high', 'low': 'low', 'close': 'close'}, inplace=True)
    logger.info(f"Resampled data shape: {ohlc_df.shape}")

    # Define parameter grid
    param_grid = {
        'neighbors_count': [5, 8, 13],
        'max_bars_back': [500, 1000, 1500],
        'use_volatility_filter': [True, False],
        'use_regime_filter': [True, False],
        'use_adx_filter': [True, False],
        'regime_threshold': [0.0, 0.01, -0.01],
        'adx_threshold': [20, 25, 30]
    }

    keys, values = zip(*param_grid.items())
    combinations = [dict(zip(keys, v)) for v in itertools.product(*values)]
    
    all_results = []
    
    logger.info(f"Starting optimization with {len(combinations)} combinations...")

    for i, params in enumerate(combinations):
        logger.info(f"Testing combination {i+1}/{len(combinations)}: {params}")
        
        settings = Settings(
            neighbors_count=params['neighbors_count'],
            max_bars_back=params['max_bars_back'],
            feature_count=4
        )
        filter_settings = FilterSettings(
            use_volatility_filter=params['use_volatility_filter'],
            use_regime_filter=params['use_regime_filter'],
            use_adx_filter=params['use_adx_filter'],
            regime_threshold=params['regime_threshold'],
            adx_threshold=params['adx_threshold']
        )
        
        result = backtest_strategy(ohlc_df, settings, filter_settings)
        if result:
            all_results.append(result)

    if not all_results:
        logger.info("No profitable configurations found.")
        return

    results_df = pd.DataFrame(all_results)
    results_df.sort_values(by='final_balance', ascending=False, inplace=True)
    
    output_path = 'lorentzian_optimization_results.csv'
    logger.info(f"Saving optimization results to {output_path}...")
    results_df.to_csv(output_path, index=False)
    
    logger.info("\nTop 10 Most Profitable Configurations:")
    print(results_df.head(10).to_string())

if __name__ == "__main__":
    main()
