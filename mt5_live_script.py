import MetaTrader5 as mt5
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# MetaTrader 5 credentials
account = 5749910
password = "@Ripper25"
server = "Deriv-Demo"
symbol = "Volatility 75 (1s) Index"  # Replace with the desired symbol
lot = 0.1  # Replace with the desired lot size

def connect_mt5():
    """Connect to MetaTrader 5."""
    if not mt5.initialize():
        print("initialize() failed, error code =", mt5.last_error())
        quit()
    
    authorized = mt5.login(account=account, password=password, server=server)
    if authorized:
        print("Connected to MetaTrader 5")
    else:
        print("Authorization failed, error code =", mt5.last_error())
        mt5.shutdown()
        quit()

def calculate_average_price_movement(symbol, timeframe=mt5.TIMEFRAME_M1):
    """Calculate average price movement within a 1-minute candle."""
    rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, 100)  # Get last 100 minutes
    df = pd.DataFrame(rates)
    df['time'] = pd.to_datetime(df['time'], unit='s')
    df['price_movement'] = abs(df['high'] - df['low'])
    avg_price_movement = df['price_movement'].mean()
    return avg_price_movement

def trade_cycle():
    """Open a trade, then immediately close it and open a new trade in the opposite direction."""
    
    # Get symbol info
    symbol_info = mt5.symbol_info(symbol)
    if symbol_info is None:
        print(f"Symbol {symbol} not found, can not call symbol_info()")
        return
    
    # Check if the symbol is tradeable
    if not symbol_info.tradeable:
        print(f"Symbol {symbol} is not tradeable")
        return
    
    # Open a buy trade
    request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": lot,
        "type": mt5.ORDER_TYPE_BUY,
        "price": symbol_info.ask,
        "sl": 0.0,
        "tp": 0.0,
        "deviation": 20,
        "magic": 123456,
        "comment": "Python script open",
        "type_time": mt5.ORDER_TYPE_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }
    result = mt5.order_send(request)
    if result.retcode != mt5.TRADE_RETCODE_DONE:
        print(f"OrderSend failed, retcode={result.retcode}")
        return
    else:
        print(f"Buy order opened successfully at {symbol_info.ask}")
    
    # Get the ticket number of the opened order
    ticket = result.order
    
    # Immediately close the opened order
    positions = mt5.positions_get(symbol=symbol)
    if positions is None:
        print("No positions on the symbol, error code =",mt5.last_error())
        return
    if len(positions) > 0:
        position = positions[0]
        if position.ticket == ticket:
            
            # Create a close request
            if position.type == mt5.ORDER_TYPE_BUY:
                trade_type = mt5.ORDER_TYPE_SELL
                close_price = symbol_info.bid
            else:
                trade_type = mt5.ORDER_TYPE_BUY
                close_price = symbol_info.ask
                
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": lot,
                "type": trade_type,
                "position": position.ticket,
                "price": close_price,
                "deviation": 20,
                "magic": 123456,
                "comment": "Python script close",
                "type_time": mt5.ORDER_TYPE_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }
            
            # Send close order
            result = mt5.order_send(request)
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                print(f"OrderClose failed, retcode={result.retcode}")
                return
            else:
                print(f"Close order executed successfully at {close_price}")
            
            # Open a sell trade immediately after closing the buy trade
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": lot,
                "type": mt5.ORDER_TYPE_SELL,
                "price": symbol_info.bid,
                "sl": 0.0,
                "tp": 0.0,
                "deviation": 20,
                "magic": 123456,
                "comment": "Python script open (reverse)",
                "type_time": mt5.ORDER_TYPE_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }
            result = mt5.order_send(request)
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                print(f"Reverse OrderSend failed, retcode={result.retcode}")
                return
            else:
                print(f"Sell order opened successfully at {symbol_info.bid}")
    else:
        print("No positions found")

if __name__ == "__main__":
    connect_mt5()
    
    avg_price_movement = calculate_average_price_movement(symbol)
    print(f"Average price movement within 1-minute candle: {avg_price_movement:.5f}")
    
    trade_cycle()
    
    mt5.shutdown()