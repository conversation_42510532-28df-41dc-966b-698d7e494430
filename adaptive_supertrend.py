import pandas as pd
import numpy as np
from sklearn.cluster import KM<PERSON><PERSON>

def calculate_supertrend(df, factor, atr_length):
    """Calculates the SuperTrend indicator.

    Args:
        df (pd.DataFrame): DataFrame with 'high', 'low', and 'close' columns.
        factor (float): SuperTrend factor.
        atr_length (int): ATR length.

    Returns:
        pd.DataFrame: DataFrame with SuperTrend values.
    """
    df['atr'] = df['high'].rolling(atr_length).max() - df['low'].rolling(atr_length).min()
    df['upper_band'] = df['close'] + factor * df['atr']
    df['lower_band'] = df['close'] - factor * df['atr']
    df['direction'] = 1  # Initial direction
    df['supertrend'] = np.nan

    for i in range(1, len(df)):
        prev_lower_band = df['lower_band'][i-1]
        prev_upper_band = df['upper_band'][i-1]

        if df['lower_band'][i] > prev_lower_band or df['close'][i-1] < prev_lower_band:
            lower_band = df['lower_band'][i]
        else:
            lower_band = prev_lower_band

        if df['upper_band'][i] < prev_upper_band or df['close'][i-1] > prev_upper_band:
            upper_band = df['upper_band'][i]
        else:
            upper_band = prev_upper_band

        if np.isnan(df['atr'][i-1]):
            df['direction'][i] = 1
        elif df['supertrend'][i-1] == prev_upper_band:
            df['direction'][i] = -1 if df['close'][i] > upper_band else 1
        else:
            df['direction'][i] = 1 if df['close'][i] < lower_band else -1

        df['supertrend'][i] = lower_band if df['direction'][i] == -1 else upper_band

    return df

def adaptive_supertrend(file_path, atr_length=10, factor=3, training_data_period=100, highvol=0.75, midvol=0.5, lowvol=0.25):
    """Calculates the Adaptive SuperTrend indicator.

    Args:
        file_path (str): Path to the CSV file.
        atr_length (int): ATR length.
        factor (float): SuperTrend factor.
        training_data_period (int): Training data length for K-means.
        highvol (float): Initial high volatility percentile guess.
        midvol (float): Initial medium volatility percentile guess.
        lowvol (float): Initial low volatility percentile guess.
    """
    df = pd.read_csv(file_path)

    # Ensure the DataFrame has 'high', 'low', and 'close' columns
    if not all(col in df.columns for col in ['high', 'low', 'close']):
        raise ValueError("DataFrame must contain 'high', 'low', and 'close' columns.")

    volatility = df['high'].rolling(atr_length).max() - df['low'].rolling(atr_length).min()

    upper = volatility.rolling(training_data_period).max().iloc[-1]
    lower = volatility.rolling(training_data_period).min().iloc[-1]

    high_volatility = lower + (upper - lower) * highvol
    medium_volatility = lower + (upper - lower) * midvol
    low_volatility = lower + (upper - lower) * lowvol

    # K-Means Clustering
    kmeans = KMeans(n_clusters=3, init=np.array([[high_volatility], [medium_volatility], [low_volatility]]), n_init=1)
    kmeans.fit(volatility[training_data_period:].dropna().values.reshape(-1, 1))

    hv_new, mv_new, lv_new = kmeans.cluster_centers_.flatten()

    # Assign cluster based on distances
    vdist_a = np.abs(volatility - hv_new)
    vdist_b = np.abs(volatility - mv_new)
    vdist_c = np.abs(volatility - lv_new)

    distances = np.array([vdist_a, vdist_b, vdist_c])
    cluster = np.argmin(distances, axis=0)

    assigned_centroid = np.array([hv_new, mv_new, lv_new])[cluster]

    df['assigned_centroid'] = assigned_centroid

    # Calculate SuperTrend
    df = calculate_supertrend(df, factor, atr_length)

    # Add cluster information
    df['cluster'] = cluster

    print(df.tail(20))

# Example usage
file_path = "Volatility_75_(1s)_Index.0_1min.csv"
adaptive_supertrend(file_path)
