import pandas as pd
import numpy as np
import torch
from torch import nn
from sklearn.preprocessing import MinMaxScaler
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report
import argparse

class SpikeDataProcessor:
    def __init__(self, csv_file):
        self.data = pd.read_csv(csv_file)

    def process_data(self, window_size=10, spike_threshold=10):
        # Calculate price changes
        self.data['price_change'] = self.data['bid'].diff()

        # Define spikes
        self.data['spike'] = (self.data['price_change'] > spike_threshold).astype(int)

        # Create features
        for i in range(1, window_size + 1):
            self.data[f'lag_{i}'] = self.data['price_change'].shift(i)
        
        # Drop NaNs
        self.data.dropna(inplace=True)

        # Prepare features and labels
        X = self.data[[f'lag_{i}' for i in range(1, window_size + 1)]].values
        y_spike = self.data['spike'].values

        return X, y_spike

class SpikeDataset(Dataset):
    def __init__(self, X, y_spike):
        self.X = X
        self.y_spike = y_spike

    def __len__(self):
        return len(self.X)

    def __getitem__(self, idx):
        return self.X[idx], self.y_spike[idx]

class SpikePredictor(nn.Module):
    def __init__(self, input_size):
        super(SpikePredictor, self).__init__()
        self.fc1 = nn.Linear(input_size, 64)
        self.fc2 = nn.Linear(64, 2)

    def forward(self, x):
        x = torch.relu(self.fc1(x))
        return self.fc2(x)

def train_model(csv_file):
    processor = SpikeDataProcessor(csv_file)
    X, y_spike = processor.process_data()

    # Train-test split
    X_train, X_test, y_train, y_test = train_test_split(X, y_spike, test_size=0.2, random_state=42)

    # Create datasets
    train_dataset = SpikeDataset(X_train, y_train)
    test_dataset = SpikeDataset(X_test, y_test)

    # Create loaders
    train_loader = DataLoader(train_dataset, batch_size=64, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=64, shuffle=False)

    # Model
    model = SpikePredictor(X_train.shape[1])
    criterion = nn.CrossEntropyLoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)

    # Training
    for epoch in range(20):
        model.train()
        total_loss = 0
        for X_batch, y_batch in train_loader:
            optimizer.zero_grad()
            outputs = model(X_batch.float())
            loss = criterion(outputs, y_batch.long())
            loss.backward()
            optimizer.step()
            total_loss += loss.item()
        print(f'Epoch {epoch + 1}, Loss: {total_loss / len(train_loader)}')

    # Evaluate
    model.eval()
    y_true = []
    y_pred = []
    with torch.no_grad():
        for X_batch, y_batch in test_loader:
            outputs = model(X_batch.float())
            _, predicted = torch.max(outputs, 1)
            y_true.extend(y_batch.numpy())
            y_pred.extend(predicted.numpy())

    print(classification_report(y_true, y_pred))

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Spike Prediction Model')
    parser.add_argument('--csv_file', type=str, default='Boom_1000_Index_7days_20250620_20250627.csv')
    args = parser.parse_args()

    train_model(args.csv_file)
