import pandas as pd
import numpy as np
import xgboost as xgb
import os

def simulate_trading_strategy(file_path, spike_model_path, range_model_path):
    """
    Simulates a trading strategy using trained XGBoost models for spike identification
    and tradable range prediction.

    Args:
        file_path (str): Path to the tick data CSV file.
        spike_model_path (str): Path to the trained spike identification model (.ubj).
        range_model_path (str): Path to the trained tradable range prediction model (.ubj).
    """
    print(f"Loading data from {file_path}...")
    try:
        df = pd.read_csv(file_path)
        print("Data loaded successfully.")
    except FileNotFoundError:
        print(f"Error: File not found at {file_path}")
        return
    except Exception as e:
        print(f"Error loading data: {e}")
        return

    # Load models
    print(f"Loading spike model from {spike_model_path}...")
    spike_model = xgb.XGBClassifier()
    spike_model.load_model(spike_model_path)
    print("Spike model loaded.")

    print(f"Loading range model from {range_model_path}...")
    range_model = xgb.XGBClassifier()
    range_model.load_model(range_model_path)
    print("Range model loaded.")

    # Ensure 'time' column is datetime and sort
    df['time'] = pd.to_datetime(df['time'])
    df = df.sort_values('time').reset_index(drop=True)

    # --- Feature Engineering (must match training features) ---

    # 1. Minute-level OHLCV and Spread
    df['minute'] = df['time'].dt.floor('1min')
    minute_data = df.groupby('minute').agg(
        open=('bid', 'first'),
        high=('bid', 'max'),
        low=('bid', 'min'),
        close=('bid', 'last'),
        volume=('bid', 'size'), # Number of ticks in the minute
        ask_open=('ask', 'first'),
        ask_high=('ask', 'max'),
        ask_low=('ask', 'min'),
        ask_close=('ask', 'last'),
    )

    minute_data['range'] = minute_data['high'] - minute_data['low']
    minute_data['spread'] = minute_data['ask_close'] - minute_data['close']

    # 4. Lagged Features (using bid close for simplicity)
    for lag in range(1, 6): # Lag features for past 5 minutes
        minute_data[f'close_lag_{lag}'] = minute_data['close'].shift(lag)
        minute_data[f'range_lag_{lag}'] = minute_data['range'].shift(lag)
        minute_data[f'volume_lag_{lag}'] = minute_data['volume'].shift(lag)
        minute_data[f'spread_lag_{lag}'] = minute_data['spread'].shift(lag)

    # 5. Rolling Features
    minute_data['bid_std_5min'] = minute_data['close'].rolling(window=5).std()
    minute_data['range_mean_5min'] = minute_data['range'].rolling(window=5).mean()

    # 6. Time-based Features
    minute_data['hour'] = minute_data.index.hour
    minute_data['day_of_week'] = minute_data.index.dayofweek

    # Fill NaN values created by shifting/rolling (initial rows) with 0
    minute_data = minute_data.fillna(0).reset_index()

    # Define features (X) - must match training features
    features = [
        'open', 'high', 'low', 'close', 'volume', 'range', 'spread',
        'ask_open', 'ask_high', 'ask_low', 'ask_close',
        'hour', 'day_of_week',
    ]
    for lag in range(1, 6):
        features.extend([f'close_lag_{lag}', f'range_lag_{lag}', f'volume_lag_{lag}', f'spread_lag_{lag}'])
    features.extend(['bid_std_5min', 'range_mean_5min'])

    X = minute_data[features]

    # --- Prediction ---
    print("Making predictions with trained models...")
    minute_data['predicted_spike'] = spike_model.predict(X)
    minute_data['predicted_tradable_range'] = range_model.predict(X)

    # --- Trading Strategy Simulation ---
    trades = []
    for index, row in minute_data.iterrows():
        if row['predicted_spike'] == 0 and row['predicted_tradable_range'] == 1:
            # Simulate a short trade: open at high, close at low
            # Only if high is greater than low (positive range)
            # Define trading parameters
            TICK_SIZE = 0.1
            VALUE_PER_TICK = 0.1
            LOT_SIZE = 0.01
            COMMISSION_RATE = 0.15 # 15% commission

            # Simulate a short trade: open at high, close at low
            # Apply 1-tick slippage against the trade for both entry and exit
            entry_price_short = row['high'] + TICK_SIZE  # Worse entry for short
            exit_price_short = row['low'] - TICK_SIZE    # Worse exit for short
            
            # Only consider trades where there's a potential positive range to capture after slippage
            if entry_price_short > exit_price_short:
                profit_points_short = entry_price_short - exit_price_short
                profit_currency_short = profit_points_short * (LOT_SIZE / TICK_SIZE) * VALUE_PER_TICK
                trades.append({
                    'timestamp': row['minute'],
                    'trade_type': 'short',
                    'entry_price': entry_price_short,
                    'exit_price': exit_price_short,
                    'profit': profit_currency_short,
                    'range': row['range']
                })
            
            # Simulate a long trade: open at low, close at high
            # Apply 1-tick slippage against the trade for both entry and exit
            entry_price_long = row['low'] - TICK_SIZE    # Worse entry for long
            exit_price_long = row['high'] + TICK_SIZE   # Worse exit for long

            # Only consider trades where there's a potential positive range to capture after slippage
            if exit_price_long > entry_price_long:
                profit_points_long = exit_price_long - entry_price_long
                profit_currency_long = profit_points_long * (LOT_SIZE / TICK_SIZE) * VALUE_PER_TICK * (1 - COMMISSION_RATE)
                trades.append({
                    'timestamp': row['minute'],
                    'trade_type': 'long',
                    'entry_price': entry_price_long,
                    'exit_price': exit_price_long,
                    'profit': profit_currency_long,
                    'range': row['range']
                })

    trades_df = pd.DataFrame(trades)

    print(f"\nSimulated {len(trades_df)} trades.")

    # --- Performance Metrics ---
    if not trades_df.empty:
        total_trades = len(trades_df)
        profitable_trades = trades_df[trades_df['profit'] > 0]
        losing_trades = trades_df[trades_df['profit'] <= 0]

        num_profitable = len(profitable_trades)
        num_losing = len(losing_trades)

        win_rate = (num_profitable / total_trades) * 100 if total_trades > 0 else 0
        total_profit = trades_df['profit'].sum()

        avg_profit_per_profitable_trade = profitable_trades['profit'].mean() if num_profitable > 0 else 0
        avg_loss_per_losing_trade = losing_trades['profit'].mean() if num_losing > 0 else 0

        print("\n--- Trading Strategy Performance ---")
        print(f"Total Simulated Trades: {total_trades}")
        print(f"Profitable Trades: {num_profitable}")
        print(f"Losing Trades (or zero profit): {num_losing}")
        print(f"Win Rate: {win_rate:.2f}%")
        print(f"Total Simulated P&L: {total_profit:.2f}")
        print(f"Average Profit per Profitable Trade: {avg_profit_per_profitable_trade:.2f}")
        print(f"Average Loss per Losing Trade: {avg_loss_per_losing_trade:.2f}")

        # Max Drawdown Calculation
        # Calculate cumulative P&L
        trades_df['cumulative_profit'] = trades_df['profit'].cumsum()
        # Calculate running maximum
        trades_df['peak'] = trades_df['cumulative_profit'].expanding(min_periods=1).max()
        # Calculate drawdown
        trades_df['drawdown'] = trades_df['peak'] - trades_df['cumulative_profit']
        max_drawdown = trades_df['drawdown'].max()
        print(f"Maximum Drawdown: {max_drawdown:.2f}")

        print("\n--- Sample Trades ---")
        print(trades_df.head())

    else:
        print("No trades were simulated based on model predictions and strategy rules.")

def simulate_trading_strategy_with_kelly(file_path, spike_model_path, range_model_path, initial_capital=1000, kelly_fraction=0.5):
    """
    Simulates a trading strategy with fractional Kelly position sizing.
    """
    print(f"Loading data from {file_path}...")
    try:
        df = pd.read_csv(file_path)
    except FileNotFoundError:
        print(f"Error: File not found at {file_path}")
        return
    df['time'] = pd.to_datetime(df['time'])
    df = df.sort_values('time').reset_index(drop=True)

    print("Loading models...")
    spike_model = xgb.XGBClassifier()
    spike_model.load_model(spike_model_path)
    range_model = xgb.XGBClassifier()
    range_model.load_model(range_model_path)

    print("Feature engineering...")
    df['minute'] = df['time'].dt.floor('1min')
    minute_data = df.groupby('minute').agg(
        open=('bid', 'first'), high=('bid', 'max'), low=('bid', 'min'), close=('bid', 'last'),
        volume=('bid', 'size'), ask_open=('ask', 'first'), ask_high=('ask', 'max'),
        ask_low=('ask', 'min'), ask_close=('ask', 'last'),
    )
    minute_data['range'] = minute_data['high'] - minute_data['low']
    minute_data['spread'] = minute_data['ask_close'] - minute_data['close']
    for lag in range(1, 6):
        minute_data[f'close_lag_{lag}'] = minute_data['close'].shift(lag)
        minute_data[f'range_lag_{lag}'] = minute_data['range'].shift(lag)
        minute_data[f'volume_lag_{lag}'] = minute_data['volume'].shift(lag)
        minute_data[f'spread_lag_{lag}'] = minute_data['spread'].shift(lag)
    minute_data['bid_std_5min'] = minute_data['close'].rolling(window=5).std()
    minute_data['range_mean_5min'] = minute_data['range'].rolling(window=5).mean()
    minute_data['hour'] = minute_data.index.hour
    minute_data['day_of_week'] = minute_data.index.dayofweek
    minute_data = minute_data.fillna(0).reset_index()

    features = [
        'open', 'high', 'low', 'close', 'volume', 'range', 'spread',
        'ask_open', 'ask_high', 'ask_low', 'ask_close', 'hour', 'day_of_week',
    ]
    for lag in range(1, 6):
        features.extend([f'close_lag_{lag}', f'range_lag_{lag}', f'volume_lag_{lag}', f'spread_lag_{lag}'])
    features.extend(['bid_std_5min', 'range_mean_5min'])
    X = minute_data[features]

    print("Making predictions with trained models...")
    spike_pred = spike_model.predict(X)
    range_pred = range_model.predict(X)
    
    # Get probabilities for Kelly Criterion
    # Probability of NOT a spike (class 0)
    prob_no_spike = spike_model.predict_proba(X)[:, 0]
    # Probability of a tradable range (class 1)
    prob_tradable_range = range_model.predict_proba(X)[:, 1]
    
    # Combined probability of a successful trade condition
    minute_data['prob_win'] = prob_no_spike * prob_tradable_range
    minute_data['predicted_spike'] = spike_pred
    minute_data['predicted_tradable_range'] = range_pred

    print("Simulating trades with Fractional Kelly...")
    trades = []
    capital = initial_capital
    
    TICK_SIZE = 0.1
    VALUE_PER_TICK = 0.1
    COMMISSION_RATE = 0.15

    for index, row in minute_data.iterrows():
        if row['predicted_spike'] == 0 and row['predicted_tradable_range'] == 1 and row['range'] > 0:
            
            p = row['prob_win']
            q = 1 - p
            # Simplified Kelly assuming 1:1 win/loss payoff ratio
            b = 1.0 
            kelly_f = (b * p - q) / b
            
            risk_fraction = kelly_f * kelly_fraction
            
            if risk_fraction <= 0:
                continue

            # --- Short Trade ---
            entry_price_short = row['high'] + TICK_SIZE
            exit_price_short = row['low'] - TICK_SIZE
            if entry_price_short > exit_price_short:
                # Simplified lot size calculation
                lot_size = (capital * risk_fraction) / 1000  # Risk a fraction of capital, normalized
                if lot_size < 0.01: lot_size = 0.01

                profit_points = entry_price_short - exit_price_short
                profit = profit_points * (lot_size / TICK_SIZE) * VALUE_PER_TICK * (1 - COMMISSION_RATE)
                capital += profit
                trades.append({
                    'timestamp': row['minute'], 'trade_type': 'short', 'entry_price': entry_price_short,
                    'exit_price': exit_price_short, 'profit': profit, 'lot_size': lot_size, 'capital': capital
                })

            # --- Long Trade ---
            entry_price_long = row['low'] - TICK_SIZE
            exit_price_long = row['high'] + TICK_SIZE
            if exit_price_long > entry_price_long:
                lot_size = (capital * risk_fraction) / 1000 # Risk a fraction of capital, normalized
                if lot_size < 0.01: lot_size = 0.01

                profit_points = exit_price_long - entry_price_long
                profit = profit_points * (lot_size / TICK_SIZE) * VALUE_PER_TICK * (1 - COMMISSION_RATE)
                capital += profit
                trades.append({
                    'timestamp': row['minute'], 'trade_type': 'long', 'entry_price': entry_price_long,
                    'exit_price': exit_price_long, 'profit': profit, 'lot_size': lot_size, 'capital': capital
                })

    trades_df = pd.DataFrame(trades)
    
    print(f"\nSimulated {len(trades_df)} trades.")
    if not trades_df.empty:
        total_trades = len(trades_df)
        win_rate = (trades_df['profit'] > 0).sum() / total_trades * 100
        total_profit = trades_df['profit'].sum()
        final_equity = trades_df['capital'].iloc[-1]
        
        print("\n--- Fractional Kelly Strategy Performance ---")
        print(f"Initial Capital: ${initial_capital:.2f}")
        print(f"Final Equity: ${final_equity:.2f}")
        print(f"Total Return: {((final_equity - initial_capital) / initial_capital) * 100:.2f}%")
        print(f"Total Net P&L: ${total_profit:.2f}")
        print(f"Total Trades: {total_trades}")
        print(f"Win Rate: {win_rate:.2f}%")

        trades_df['drawdown'] = trades_df['capital'].expanding().max() - trades_df['capital']
        max_drawdown = trades_df['drawdown'].max()
        print(f"Maximum Drawdown: ${max_drawdown:.2f}")
        
        print("\n--- Sample Trades (Kelly) ---")
        print(trades_df.head())
    else:
        print("No trades were simulated.")


if __name__ == "__main__":
    data_file = "Range_Break_100_7days_20250623_20250630.csv"
    spike_model_file = "xgboost_spike_model.ubj"
    range_model_file = "xgboost_range_model.ubj"
    # simulate_trading_strategy(data_file, spike_model_file, range_model_file)
    simulate_trading_strategy_with_kelly(data_file, spike_model_file, range_model_file, initial_capital=10, kelly_fraction=0.5)