"""
Win/Loss Streak Analysis Module

This module provides functions to analyze consecutive win and loss streaks in trading results,
including longest streaks and frequency distributions for comprehensive performance evaluation.
"""

import pandas as pd
import numpy as np
from typing import Dict, <PERSON>, Tuple, Union
from collections import Counter


def detect_consecutive_streaks(results: pd.Series) -> List[Tuple[str, int]]:
    """
    Detect consecutive streaks of wins and losses from a results series.
    
    Args:
        results: Series with True/False or 1/0 values (True/1 = win, False/0 = loss)
    
    Returns:
        List of tuples containing (streak_type, streak_length)
        where streak_type is 'win' or 'loss'
    """
    if len(results) == 0:
        return []
    
    streaks = []
    current_streak_type = 'win' if results.iloc[0] else 'loss'
    current_streak_length = 1
    
    for i in range(1, len(results)):
        current_result = 'win' if results.iloc[i] else 'loss'
        
        if current_result == current_streak_type:
            current_streak_length += 1
        else:
            # End current streak and start new one
            streaks.append((current_streak_type, current_streak_length))
            current_streak_type = current_result
            current_streak_length = 1
    
    # Add the final streak
    streaks.append((current_streak_type, current_streak_length))
    
    return streaks


def analyze_win_loss_streaks(df: pd.DataFrame, 
                           win_column: str = 'is_winner',
                           loss_column: str = 'is_loser') -> Dict[str, Union[int, Dict, pd.DataFrame]]:
    """
    Comprehensive analysis of win/loss streaks from trading results.
    
    Args:
        df: DataFrame containing trading results
        win_column: Column name indicating wins (True/False)
        loss_column: Column name indicating losses (True/False)
    
    Returns:
        Dictionary containing:
        - longest_win_streak: Maximum consecutive wins
        - longest_loss_streak: Maximum consecutive losses
        - win_streak_distribution: Frequency distribution of win streak lengths
        - loss_streak_distribution: Frequency distribution of loss streak lengths
        - streak_summary_df: DataFrame with detailed streak analysis
        - total_streaks: Total number of streaks
        - average_win_streak: Average length of win streaks
        - average_loss_streak: Average length of loss streaks
    """
    
    if df.empty:
        return {
            'longest_win_streak': 0,
            'longest_loss_streak': 0,
            'win_streak_distribution': {},
            'loss_streak_distribution': {},
            'streak_summary_df': pd.DataFrame(),
            'total_streaks': 0,
            'average_win_streak': 0,
            'average_loss_streak': 0
        }
    
    # Create results series based on wins/losses
    # Prioritize wins over losses if both columns exist
    if win_column in df.columns:
        results = df[win_column].astype(bool)
    elif loss_column in df.columns:
        # Invert losses to get wins
        results = ~df[loss_column].astype(bool)
    else:
        raise ValueError(f"Neither '{win_column}' nor '{loss_column}' found in DataFrame columns")
    
    # Detect all consecutive streaks
    streaks = detect_consecutive_streaks(results)
    
    if not streaks:
        return {
            'longest_win_streak': 0,
            'longest_loss_streak': 0,
            'win_streak_distribution': {},
            'loss_streak_distribution': {},
            'streak_summary_df': pd.DataFrame(),
            'total_streaks': 0,
            'average_win_streak': 0,
            'average_loss_streak': 0
        }
    
    # Separate win and loss streaks
    win_streaks = [length for streak_type, length in streaks if streak_type == 'win']
    loss_streaks = [length for streak_type, length in streaks if streak_type == 'loss']
    
    # Calculate longest streaks
    longest_win_streak = max(win_streaks) if win_streaks else 0
    longest_loss_streak = max(loss_streaks) if loss_streaks else 0
    
    # Calculate frequency distributions
    win_streak_distribution = dict(Counter(win_streaks)) if win_streaks else {}
    loss_streak_distribution = dict(Counter(loss_streaks)) if loss_streaks else {}
    
    # Calculate averages
    average_win_streak = np.mean(win_streaks) if win_streaks else 0
    average_loss_streak = np.mean(loss_streaks) if loss_streaks else 0
    
    # Create detailed summary DataFrame
    streak_data = []
    streak_start_idx = 0
    
    for i, (streak_type, length) in enumerate(streaks):
        streak_end_idx = streak_start_idx + length - 1
        streak_data.append({
            'streak_number': i + 1,
            'streak_type': streak_type,
            'streak_length': length,
            'start_trade': streak_start_idx + 1,  # 1-indexed for readability
            'end_trade': streak_end_idx + 1,
            'start_index': streak_start_idx,
            'end_index': streak_end_idx
        })
        streak_start_idx += length
    
    streak_summary_df = pd.DataFrame(streak_data)
    
    return {
        'longest_win_streak': longest_win_streak,
        'longest_loss_streak': longest_loss_streak,
        'win_streak_distribution': win_streak_distribution,
        'loss_streak_distribution': loss_streak_distribution,
        'streak_summary_df': streak_summary_df,
        'total_streaks': len(streaks),
        'total_win_streaks': len(win_streaks),
        'total_loss_streaks': len(loss_streaks),
        'average_win_streak': round(average_win_streak, 2),
        'average_loss_streak': round(average_loss_streak, 2),
        'win_streaks': win_streaks,
        'loss_streaks': loss_streaks
    }


def create_streak_histogram_data(streak_distribution: Dict[int, int], 
                                streak_type: str = 'win') -> pd.DataFrame:
    """
    Create histogram data for streak length frequency distribution.
    
    Args:
        streak_distribution: Dictionary with streak_length: frequency pairs
        streak_type: Type of streak ('win' or 'loss')
    
    Returns:
        DataFrame with columns: streak_length, frequency, percentage, streak_type
    """
    if not streak_distribution:
        return pd.DataFrame(columns=['streak_length', 'frequency', 'percentage', 'streak_type'])
    
    total_streaks = sum(streak_distribution.values())
    
    histogram_data = []
    for length, frequency in sorted(streak_distribution.items()):
        percentage = (frequency / total_streaks) * 100
        histogram_data.append({
            'streak_length': length,
            'frequency': frequency,
            'percentage': round(percentage, 2),
            'streak_type': streak_type
        })
    
    return pd.DataFrame(histogram_data)


def generate_streak_report(df: pd.DataFrame, 
                          win_column: str = 'is_winner',
                          loss_column: str = 'is_loser') -> Dict[str, Union[str, int, float, pd.DataFrame]]:
    """
    Generate a comprehensive streak analysis report.
    
    Args:
        df: DataFrame containing trading results
        win_column: Column name indicating wins
        loss_column: Column name indicating losses
    
    Returns:
        Dictionary containing formatted report data and summary statistics
    """
    analysis = analyze_win_loss_streaks(df, win_column, loss_column)
    
    # Create histogram DataFrames
    win_histogram_df = create_streak_histogram_data(
        analysis['win_streak_distribution'], 'win'
    )
    loss_histogram_df = create_streak_histogram_data(
        analysis['loss_streak_distribution'], 'loss'
    )
    
    # Combine histograms
    combined_histogram_df = pd.concat([win_histogram_df, loss_histogram_df], 
                                    ignore_index=True)
    
    # Calculate additional metrics
    total_trades = len(df)
    win_rate = (sum(analysis['win_streaks']) / total_trades * 100) if total_trades > 0 else 0
    loss_rate = (sum(analysis['loss_streaks']) / total_trades * 100) if total_trades > 0 else 0
    
    # Generate summary text
    summary_text = f"""
STREAK ANALYSIS SUMMARY
======================
Total Trades: {total_trades}
Total Streaks: {analysis['total_streaks']}

LONGEST STREAKS:
- Longest Win Streak: {analysis['longest_win_streak']} trades
- Longest Loss Streak: {analysis['longest_loss_streak']} trades

STREAK COUNTS:
- Total Win Streaks: {analysis['total_win_streaks']}
- Total Loss Streaks: {analysis['total_loss_streaks']}

AVERAGE STREAK LENGTHS:
- Average Win Streak: {analysis['average_win_streak']} trades
- Average Loss Streak: {analysis['average_loss_streak']} trades

OVERALL PERFORMANCE:
- Win Rate: {win_rate:.2f}%
- Loss Rate: {loss_rate:.2f}%
"""
    
    return {
        'summary_text': summary_text,
        'longest_win_streak': analysis['longest_win_streak'],
        'longest_loss_streak': analysis['longest_loss_streak'],
        'total_streaks': analysis['total_streaks'],
        'average_win_streak': analysis['average_win_streak'],
        'average_loss_streak': analysis['average_loss_streak'],
        'win_rate': round(win_rate, 2),
        'loss_rate': round(loss_rate, 2),
        'streak_summary_df': analysis['streak_summary_df'],
        'histogram_df': combined_histogram_df,
        'win_streak_distribution': analysis['win_streak_distribution'],
        'loss_streak_distribution': analysis['loss_streak_distribution'],
        'detailed_analysis': analysis
    }


def find_longest_streaks_details(df: pd.DataFrame, 
                                win_column: str = 'is_winner',
                                loss_column: str = 'is_loser') -> Dict[str, pd.DataFrame]:
    """
    Find detailed information about the longest win and loss streaks.
    
    Args:
        df: DataFrame containing trading results
        win_column: Column name indicating wins
        loss_column: Column name indicating losses
    
    Returns:
        Dictionary containing DataFrames with details of longest streaks
    """
    analysis = analyze_win_loss_streaks(df, win_column, loss_column)
    streak_summary = analysis['streak_summary_df']
    
    if streak_summary.empty:
        return {
            'longest_win_streak_details': pd.DataFrame(),
            'longest_loss_streak_details': pd.DataFrame()
        }
    
    # Find longest win streak details
    win_streaks = streak_summary[streak_summary['streak_type'] == 'win']
    longest_win_details = pd.DataFrame()
    if not win_streaks.empty:
        longest_win_row = win_streaks.loc[win_streaks['streak_length'].idxmax()]
        start_idx = longest_win_row['start_index']
        end_idx = longest_win_row['end_index']
        longest_win_details = df.iloc[start_idx:end_idx+1].copy()
        longest_win_details['streak_position'] = range(1, len(longest_win_details) + 1)
    
    # Find longest loss streak details
    loss_streaks = streak_summary[streak_summary['streak_type'] == 'loss']
    longest_loss_details = pd.DataFrame()
    if not loss_streaks.empty:
        longest_loss_row = loss_streaks.loc[loss_streaks['streak_length'].idxmax()]
        start_idx = longest_loss_row['start_index']
        end_idx = longest_loss_row['end_index']
        longest_loss_details = df.iloc[start_idx:end_idx+1].copy()
        longest_loss_details['streak_position'] = range(1, len(longest_loss_details) + 1)
    
    return {
        'longest_win_streak_details': longest_win_details,
        'longest_loss_streak_details': longest_loss_details,
        'longest_win_streak_info': win_streaks.loc[win_streaks['streak_length'].idxmax()] if not win_streaks.empty else None,
        'longest_loss_streak_info': loss_streaks.loc[loss_streaks['streak_length'].idxmax()] if not loss_streaks.empty else None
    }


# Example usage and testing function
def test_streak_analysis():
    """Test the streak analysis functions with sample data."""
    
    # Create sample trading data
    sample_data = {
        'trade_id': range(1, 21),
        'is_winner': [True, True, False, False, False, True, True, True, True, False, 
                      False, True, True, False, True, True, True, True, True, False],
        'is_loser': [False, False, True, True, True, False, False, False, False, True,
                     True, False, False, True, False, False, False, False, False, True],
        'pnl': [100, 150, -50, -75, -25, 200, 125, 175, 90, -60,
                -40, 110, 130, -80, 95, 105, 120, 140, 160, -45]
    }
    
    df = pd.DataFrame(sample_data)
    
    print("Sample Data:")
    print(df[['trade_id', 'is_winner', 'is_loser', 'pnl']].head(10))
    print()
    
    # Run analysis
    report = generate_streak_report(df)
    
    print(report['summary_text'])
    
    print("\nStreak Details:")
    print(report['streak_summary_df'])
    
    print("\nHistogram Data:")
    print(report['histogram_df'])
    
    # Test longest streak details
    longest_details = find_longest_streaks_details(df)
    
    print("\nLongest Win Streak Details:")
    if not longest_details['longest_win_streak_details'].empty:
        print(longest_details['longest_win_streak_details'][['trade_id', 'is_winner', 'pnl', 'streak_position']])
    
    print("\nLongest Loss Streak Details:")
    if not longest_details['longest_loss_streak_details'].empty:
        print(longest_details['longest_loss_streak_details'][['trade_id', 'is_loser', 'pnl', 'streak_position']])


if __name__ == "__main__":
    test_streak_analysis()
