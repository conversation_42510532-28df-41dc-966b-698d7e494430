# Trading Performance Report Generator

A comprehensive tool that integrates all trading performance metrics into detailed Markdown/HTML reports with visualization charts and CSV exports.

## Features

### Integrated Analysis Modules
1. **Streak Statistics** - Win/loss streak analysis with distribution charts
2. **Drawdown Analysis** - High-water mark tracking and drawdown metrics  
3. **Risk of Ruin** - Mathematical probability calculations
4. **Monte Carlo Simulations** - Distribution analysis with statistical tables
5. **Scenario Survival Analysis** - Risk scenario modeling

### Output Formats
- **Markdown Report** (`report.md`) - Comprehensive analysis document
- **PNG Charts** - High-quality visualizations including dashboard
- **CSV Exports** - Detailed data exports for further analysis

## Usage

### Basic Usage

```bash
# Generate complete report with demo data
python report_generator.py --demo --report --charts --csv

# Use your own trading data
python report_generator.py --data trades.csv --report --charts --csv
```

### Command Line Options

```bash
python report_generator.py [OPTIONS]

Options:
  --data PATH              Path to CSV file containing trade data
  --report                 Generate markdown report and save as report.md
  --csv                    Export detailed CSV files
  --charts                 Generate visualization charts
  --simulations N          Number of Monte Carlo simulations (default: 10000)
  --capital AMOUNT         Initial capital amount (default: 100000)
  --demo                   Run with sample demonstration data
```

### Data Format Requirements

Your CSV file should contain at minimum:
- `pnl` column with profit/loss values
- Optional: `entry_time`, `exit_time`, `entry_price`, `exit_price`
- Optional: `is_winner`, `is_loser` (will be auto-calculated if missing)

Example CSV format:
```csv
entry_time,exit_time,entry_price,exit_price,pnl
2023-01-01 09:00:00,2023-01-01 09:30:00,1.2345,1.2367,45.50
2023-01-01 10:00:00,2023-01-01 10:45:00,1.2367,1.2340,-32.75
```

## Generated Files

### 1. Main Report (`report.md`)
Comprehensive Markdown report containing:
- Executive summary with key metrics
- Detailed streak analysis with distribution tables
- Drawdown analysis and risk metrics
- Monte Carlo simulation results
- Scenario survival probabilities
- Conclusions and recommendations

### 2. Visualization Charts
- `streak_analysis_[timestamp].png` - Win/loss streak distributions
- `drawdown_analysis_[timestamp].png` - Equity curve and drawdown visualization
- `monte_carlo_analysis_[timestamp].png` - MC return and drawdown distributions
- `scenario_survival_[timestamp].png` - Survival probability charts
- `performance_dashboard_[timestamp].png` - Combined overview dashboard

### 3. CSV Exports
- `trades_detailed_[timestamp].csv` - Enhanced trade data with calculated metrics
- `streak_analysis_[timestamp].csv` - Detailed streak information
- `monte_carlo_results_[timestamp].csv` - MC simulation raw results
- `scenario_survival_[timestamp].csv` - Scenario analysis data
- `performance_summary_[timestamp].csv` - Key metrics summary

## Examples

### Example 1: Quick Analysis with Demo Data
```bash
python report_generator.py --demo --report
```
Generates `report.md` with sample data analysis.

### Example 2: Full Analysis with Custom Data
```bash
python report_generator.py --data my_trades.csv --report --charts --csv --simulations 50000
```
Complete analysis with 50,000 Monte Carlo simulations.

### Example 3: Charts Only
```bash
python report_generator.py --data my_trades.csv --charts
```
Generate only visualization charts without report or CSV exports.

## Integration with Existing Modules

The report generator integrates the following existing analysis modules:
- `streak_analysis.py` - Win/loss streak calculations
- `comprehensive_drawdown_analysis.py` - Drawdown metrics
- `monte_carlo_simulation.py` - MC simulation engine
- `account_survival_analysis.py` - Survival probability analysis

## Customization

### Adding Custom Metrics
You can extend the `TradingPerformanceReportGenerator` class to include additional metrics:

```python
class CustomReportGenerator(TradingPerformanceReportGenerator):
    def analyze_custom_metric(self):
        # Your custom analysis here
        pass
    
    def run_complete_analysis(self, **kwargs):
        # Call parent method
        result = super().run_complete_analysis(**kwargs)
        
        # Add custom analysis
        self.analyze_custom_metric()
        return result
```

### Modifying Chart Styles
Charts use matplotlib with seaborn styling. You can modify the plotting style in the `create_visualizations()` method.

## Performance Notes

- Monte Carlo simulations with 10,000 runs typically complete in 10-30 seconds
- Large datasets (>10,000 trades) may take longer for visualization generation
- Charts are saved at 300 DPI for high-quality output
- Memory usage scales with number of Monte Carlo simulations

## Error Handling

The tool includes comprehensive error handling for:
- Missing required columns in input data
- Invalid data formats
- File I/O errors
- Calculation edge cases (e.g., all wins or all losses)

## Dependencies

Required Python packages:
- pandas
- numpy
- matplotlib
- seaborn
- argparse (built-in)

Install dependencies:
```bash
pip install pandas numpy matplotlib seaborn
```

## Technical Details

### Risk of Ruin Formula
```
P(ruin) = ((q/p)^(capital/unit)) / (1-((q/p)^(capital/unit)))
```
Where:
- p = win probability
- q = loss probability (1-p)
- capital = total trading capital
- unit = average loss per trade

### Monte Carlo Implementation
- Bootstrap resampling of historical returns
- Synthetic equity curve generation
- Statistical analysis of outcomes
- Percentile and distribution calculations

### Drawdown Calculation
- High-water mark tracking
- Running maximum equity calculation
- Percentage and absolute drawdown metrics
- Duration analysis of drawdown periods
