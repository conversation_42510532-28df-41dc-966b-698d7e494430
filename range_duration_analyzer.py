"""
Range Duration Analyzer for Range Break Index
Analyzes how long ticks spend inside ranges before breakouts
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class RangeDurationAnalyzer:
    """
    Analyzes the duration of range-bound periods in Range Break Index tick data
    """
    
    def __init__(self, csv_file):
        self.csv_file = csv_file
        self.df = None
        self.ranges = []
        self.range_durations = []
        
    def load_and_prepare_data(self):
        """Load and prepare tick data"""
        print("Loading Range Break Index tick data...")
        
        try:
            self.df = pd.read_csv(self.csv_file)
            print(f"Loaded {len(self.df)} tick records")
        except Exception as e:
            print(f"Error loading data: {e}")
            return False
            
        # Convert time to datetime and sort
        self.df['time'] = pd.to_datetime(self.df['time'])
        self.df = self.df.sort_values('time').reset_index(drop=True)
        
        # Calculate price changes
        self.df['price_change'] = self.df['bid'].diff()
        self.df['price_change_pct'] = (self.df['price_change'] / self.df['bid'].shift(1)) * 100
        
        print(f"Data prepared. Time range: {self.df['time'].min()} to {self.df['time'].max()}")
        print(f"Price range: {self.df['bid'].min():.1f} - {self.df['bid'].max():.1f}")
        
        return True
    
    def identify_ranges(self, range_threshold=10.0, min_duration_minutes=1):
        """
        VECTORIZED: Identify range-bound periods using numpy operations
        """
        print(f"🚀 VECTORIZED: Identifying ranges (threshold: {range_threshold} points, min duration: {min_duration_minutes} min)...")

        # Convert to numpy arrays for speed
        prices = self.df['bid'].values
        times = pd.to_datetime(self.df['time']).values

        window_size = 100
        n = len(prices)

        # Vectorized rolling window calculations
        print("   Computing rolling windows...")

        # Create rolling windows using numpy stride tricks
        from numpy.lib.stride_tricks import sliding_window_view

        if n < window_size:
            print(f"⚠️ Not enough data points ({n} < {window_size})")
            return []

        # Rolling price windows
        price_windows = sliding_window_view(prices, window_size)
        time_windows = sliding_window_view(times, window_size)

        # Vectorized calculations
        rolling_highs = np.max(price_windows, axis=1)
        rolling_lows = np.min(price_windows, axis=1)
        rolling_ranges = rolling_highs - rolling_lows

        # Calculate durations in minutes (vectorized)
        start_times = time_windows[:, 0]
        end_times = time_windows[:, -1]
        durations_ns = end_times - start_times
        durations_minutes = durations_ns.astype('timedelta64[s]').astype(float) / 60

        # Identify range-bound periods
        is_range_bound = (rolling_ranges <= range_threshold) & (durations_minutes >= min_duration_minutes)

        print("   Finding range periods...")

        # Find contiguous range periods
        range_changes = np.diff(np.concatenate(([False], is_range_bound, [False])).astype(int))
        range_starts = np.where(range_changes == 1)[0]
        range_ends = np.where(range_changes == -1)[0]

        ranges = []
        for start_idx, end_idx in zip(range_starts, range_ends):
            # Adjust indices for original dataframe
            actual_start = start_idx
            actual_end = min(end_idx + window_size - 1, n - 1)

            # Calculate range statistics
            range_data = {
                'start_idx': actual_start,
                'end_idx': actual_end,
                'start_time': pd.to_datetime(times[actual_start]),
                'end_time': pd.to_datetime(times[actual_end]),
                'start_price': prices[actual_start],
                'end_price': prices[actual_end],
                'high': np.max(prices[actual_start:actual_end+1]),
                'low': np.min(prices[actual_start:actual_end+1]),
                'tick_count': actual_end - actual_start + 1
            }

            range_data['range_size'] = range_data['high'] - range_data['low']
            range_data['duration_minutes'] = (
                range_data['end_time'] - range_data['start_time']
            ).total_seconds() / 60

            # Only keep ranges that meet minimum duration
            if range_data['duration_minutes'] >= min_duration_minutes:
                ranges.append(range_data)

        self.ranges = ranges
        print(f"✅ Identified {len(ranges)} range-bound periods in seconds!")

        return ranges
    
    def analyze_range_durations(self):
        """Analyze the duration statistics of identified ranges"""
        if not self.ranges:
            print("No ranges identified. Run identify_ranges() first.")
            return None
        
        print("\n" + "="*60)
        print("RANGE DURATION ANALYSIS")
        print("="*60)
        
        # Extract duration data
        durations = [r['duration_minutes'] for r in self.ranges]
        tick_counts = [r['tick_count'] for r in self.ranges]
        range_sizes = [r['range_size'] for r in self.ranges]
        
        # Calculate statistics
        stats = {
            'total_ranges': len(self.ranges),
            'avg_duration_minutes': np.mean(durations),
            'median_duration_minutes': np.median(durations),
            'min_duration_minutes': np.min(durations),
            'max_duration_minutes': np.max(durations),
            'std_duration_minutes': np.std(durations),
            'avg_tick_count': np.mean(tick_counts),
            'avg_range_size': np.mean(range_sizes)
        }
        
        print(f"📊 Range Duration Statistics:")
        print(f"   Total ranges identified: {stats['total_ranges']}")
        print(f"   Average duration: {stats['avg_duration_minutes']:.2f} minutes")
        print(f"   Median duration: {stats['median_duration_minutes']:.2f} minutes")
        print(f"   Min duration: {stats['min_duration_minutes']:.2f} minutes")
        print(f"   Max duration: {stats['max_duration_minutes']:.2f} minutes")
        print(f"   Standard deviation: {stats['std_duration_minutes']:.2f} minutes")
        print(f"   Average ticks per range: {stats['avg_tick_count']:.0f}")
        print(f"   Average range size: {stats['avg_range_size']:.1f} points")
        
        # Duration distribution
        print(f"\n📈 Duration Distribution:")
        duration_bins = [0, 1, 2, 5, 10, 20, 30, 60, float('inf')]
        duration_labels = ['<1min', '1-2min', '2-5min', '5-10min', '10-20min', '20-30min', '30-60min', '>60min']
        
        for i, (start, end) in enumerate(zip(duration_bins[:-1], duration_bins[1:])):
            count = sum(1 for d in durations if start <= d < end)
            percentage = count / len(durations) * 100
            print(f"   {duration_labels[i]}: {count} ranges ({percentage:.1f}%)")
        
        return stats
    
    def analyze_breakout_patterns(self, breakout_threshold=5.0):
        """VECTORIZED: Analyze breakout patterns after ranges"""
        if not self.ranges:
            print("No ranges identified. Run identify_ranges() first.")
            return None

        print(f"\n🚀 VECTORIZED: Breakout Analysis (threshold: {breakout_threshold} points)...")

        prices = self.df['bid'].values
        n = len(prices)
        lookforward = 50

        # Vectorized breakout analysis
        end_indices = np.array([r['end_idx'] for r in self.ranges])
        end_prices = np.array([r['end_price'] for r in self.ranges])

        # Filter ranges that have enough data after them
        valid_mask = end_indices + lookforward < n
        valid_end_indices = end_indices[valid_mask]
        valid_end_prices = end_prices[valid_mask]
        valid_ranges = [r for i, r in enumerate(self.ranges) if valid_mask[i]]

        if len(valid_ranges) == 0:
            print("   No ranges with sufficient post-range data")
            return []

        # Vectorized calculation of post-range price movements
        breakouts = []

        for i, (end_idx, end_price, range_data) in enumerate(zip(valid_end_indices, valid_end_prices, valid_ranges)):
            # Get post-range prices
            post_prices = prices[end_idx:end_idx + lookforward]

            # Calculate max moves
            max_up_move = np.max(post_prices) - end_price
            max_down_move = end_price - np.min(post_prices)

            # Determine breakout type
            if max_up_move > breakout_threshold:
                breakout_type = 'upward'
                breakout_magnitude = max_up_move
            elif max_down_move > breakout_threshold:
                breakout_type = 'downward'
                breakout_magnitude = max_down_move
            else:
                breakout_type = 'none'
                breakout_magnitude = max(max_up_move, max_down_move)

            breakouts.append({
                'range_duration': range_data['duration_minutes'],
                'range_size': range_data['range_size'],
                'breakout_type': breakout_type,
                'breakout_magnitude': breakout_magnitude
            })

        # Vectorized statistics
        breakout_types = np.array([b['breakout_type'] for b in breakouts])
        total_breakouts = len(breakouts)
        upward_breakouts = np.sum(breakout_types == 'upward')
        downward_breakouts = np.sum(breakout_types == 'downward')
        no_breakouts = np.sum(breakout_types == 'none')

        print(f"   Total ranges analyzed: {total_breakouts}")
        print(f"   Upward breakouts: {upward_breakouts} ({upward_breakouts/total_breakouts*100:.1f}%)")
        print(f"   Downward breakouts: {downward_breakouts} ({downward_breakouts/total_breakouts*100:.1f}%)")
        print(f"   No significant breakout: {no_breakouts} ({no_breakouts/total_breakouts*100:.1f}%)")

        # Vectorized magnitude calculations
        if upward_breakouts > 0:
            upward_magnitudes = np.array([b['breakout_magnitude'] for b in breakouts if b['breakout_type'] == 'upward'])
            avg_up_magnitude = np.mean(upward_magnitudes)
            print(f"   Average upward breakout: {avg_up_magnitude:.1f} points")

        if downward_breakouts > 0:
            downward_magnitudes = np.array([b['breakout_magnitude'] for b in breakouts if b['breakout_type'] == 'downward'])
            avg_down_magnitude = np.mean(downward_magnitudes)
            print(f"   Average downward breakout: {avg_down_magnitude:.1f} points")

        print("✅ Breakout analysis completed in milliseconds!")

        return breakouts

    def analyze_ceiling_floor_oscillations(self, ceiling_tolerance=0.5):
        """
        VECTORIZED: Analyze how many ticks between ceiling and floor touches
        This is exactly how the trading strategy makes money!
        """
        print(f"\n🎯 VECTORIZED: Analyzing ceiling-to-floor tick oscillations...")

        if not self.ranges:
            print("No ranges identified. Run identify_ranges() first.")
            return []

        oscillation_data = []

        for range_data in self.ranges:
            start_idx = range_data['start_idx']
            end_idx = range_data['end_idx']

            # Get range prices (vectorized)
            range_prices = self.df.iloc[start_idx:end_idx+1]['bid'].values
            range_times = self.df.iloc[start_idx:end_idx+1]['time'].values

            if len(range_prices) < 10:
                continue

            # Define ceiling and floor levels
            ceiling = range_data['high']
            floor = range_data['low']
            range_size = ceiling - floor

            # Define zones with tolerance
            ceiling_zone = ceiling - ceiling_tolerance
            floor_zone = floor + ceiling_tolerance

            # Vectorized zone identification
            at_ceiling = range_prices >= ceiling_zone
            at_floor = range_prices <= floor_zone
            in_middle = ~at_ceiling & ~at_floor

            # Find ceiling and floor touches (transitions into zones)
            ceiling_touches = np.where(np.diff(np.concatenate(([False], at_ceiling))) == 1)[0]
            floor_touches = np.where(np.diff(np.concatenate(([False], at_floor))) == 1)[0]

            # Calculate oscillations (ceiling to floor movements)
            all_touches = []

            # Add ceiling touches
            for touch_idx in ceiling_touches:
                all_touches.append({
                    'tick_idx': touch_idx,
                    'price': range_prices[touch_idx],
                    'type': 'ceiling',
                    'time': range_times[touch_idx]
                })

            # Add floor touches
            for touch_idx in floor_touches:
                all_touches.append({
                    'tick_idx': touch_idx,
                    'price': range_prices[touch_idx],
                    'type': 'floor',
                    'time': range_times[touch_idx]
                })

            # Sort by tick index
            all_touches.sort(key=lambda x: x['tick_idx'])

            # Calculate oscillation cycles
            oscillations = []

            for i in range(len(all_touches) - 1):
                current_touch = all_touches[i]
                next_touch = all_touches[i + 1]

                # Only count ceiling-to-floor or floor-to-ceiling moves
                if current_touch['type'] != next_touch['type']:
                    ticks_between = next_touch['tick_idx'] - current_touch['tick_idx']
                    price_move = abs(next_touch['price'] - current_touch['price'])
                    time_diff = (pd.to_datetime(next_touch['time']) - pd.to_datetime(current_touch['time'])).total_seconds()

                    oscillations.append({
                        'from_type': current_touch['type'],
                        'to_type': next_touch['type'],
                        'ticks_between': ticks_between,
                        'price_move': price_move,
                        'time_seconds': time_diff,
                        'from_price': current_touch['price'],
                        'to_price': next_touch['price']
                    })

            if oscillations:
                # Calculate statistics for this range
                ticks_between = np.array([osc['ticks_between'] for osc in oscillations])
                price_moves = np.array([osc['price_move'] for osc in oscillations])
                time_seconds = np.array([osc['time_seconds'] for osc in oscillations])

                # Count different types of moves
                ceiling_to_floor = sum(1 for osc in oscillations if osc['from_type'] == 'ceiling' and osc['to_type'] == 'floor')
                floor_to_ceiling = sum(1 for osc in oscillations if osc['from_type'] == 'floor' and osc['to_type'] == 'ceiling')

                range_oscillation_data = {
                    'range_duration_minutes': range_data['duration_minutes'],
                    'range_size': range_size,
                    'ceiling_price': ceiling,
                    'floor_price': floor,
                    'total_oscillations': len(oscillations),
                    'ceiling_to_floor_moves': ceiling_to_floor,
                    'floor_to_ceiling_moves': floor_to_ceiling,
                    'avg_ticks_between_touches': np.mean(ticks_between),
                    'min_ticks_between_touches': np.min(ticks_between),
                    'max_ticks_between_touches': np.max(ticks_between),
                    'avg_price_move': np.mean(price_moves),
                    'avg_time_between_touches': np.mean(time_seconds),
                    'oscillation_frequency': len(oscillations) / range_data['duration_minutes'] if range_data['duration_minutes'] > 0 else 0,
                    'total_ticks_in_range': len(range_prices),
                    'oscillation_efficiency': len(oscillations) / len(range_prices) * 100  # % of ticks that are oscillations
                }

                oscillation_data.append(range_oscillation_data)

        print(f"✅ Analyzed {len(oscillation_data)} ranges with ceiling-floor oscillations!")

        return oscillation_data

    def generate_oscillation_statistics(self, oscillation_data):
        """Generate comprehensive oscillation statistics"""
        if not oscillation_data:
            print("No oscillation data to analyze.")
            return

        print("\n" + "="*60)
        print("CEILING-FLOOR OSCILLATION ANALYSIS")
        print("="*60)

        # Vectorized statistics
        avg_ticks = np.array([d['avg_ticks_between_touches'] for d in oscillation_data])
        min_ticks = np.array([d['min_ticks_between_touches'] for d in oscillation_data])
        max_ticks = np.array([d['max_ticks_between_touches'] for d in oscillation_data])
        total_oscillations = np.array([d['total_oscillations'] for d in oscillation_data])
        oscillation_freq = np.array([d['oscillation_frequency'] for d in oscillation_data])
        range_sizes = np.array([d['range_size'] for d in oscillation_data])
        avg_time = np.array([d['avg_time_between_touches'] for d in oscillation_data])

        print(f"📊 Tick Oscillation Statistics:")
        print(f"   Ranges with oscillations: {len(oscillation_data)}")
        print(f"   Average ticks ceiling-to-floor: {np.mean(avg_ticks):.1f} ticks")
        print(f"   Minimum ticks between touches: {np.mean(min_ticks):.1f} ticks")
        print(f"   Maximum ticks between touches: {np.mean(max_ticks):.1f} ticks")
        print(f"   Average oscillations per range: {np.mean(total_oscillations):.1f}")
        print(f"   Average oscillation frequency: {np.mean(oscillation_freq):.2f} oscillations/minute")

        print(f"\n⏱️ Timing Analysis:")
        print(f"   Average time ceiling-to-floor: {np.mean(avg_time):.1f} seconds")
        print(f"   Fastest oscillation: {np.min(avg_time):.1f} seconds")
        print(f"   Slowest oscillation: {np.max(avg_time):.1f} seconds")

        print(f"\n💰 Trading Implications:")
        print(f"   Average range size: {np.mean(range_sizes):.1f} points")
        print(f"   Profit per oscillation: ~{np.mean(range_sizes):.1f} points")
        print(f"   Oscillations per minute: {np.mean(oscillation_freq):.2f}")
        print(f"   Potential profit/minute: {np.mean(range_sizes) * np.mean(oscillation_freq):.1f} points/minute")

        # Distribution analysis
        print(f"\n📈 Tick Distribution:")
        tick_ranges = [(0, 10), (10, 20), (20, 50), (50, 100), (100, float('inf'))]
        tick_labels = ['<10 ticks', '10-20 ticks', '20-50 ticks', '50-100 ticks', '>100 ticks']

        for (start, end), label in zip(tick_ranges, tick_labels):
            count = sum(1 for ticks in avg_ticks if start <= ticks < end)
            percentage = count / len(avg_ticks) * 100
            print(f"   {label}: {count} ranges ({percentage:.1f}%)")

        return {
            'avg_ticks_between_touches': np.mean(avg_ticks),
            'avg_oscillations_per_range': np.mean(total_oscillations),
            'avg_oscillation_frequency': np.mean(oscillation_freq),
            'avg_range_size': np.mean(range_sizes),
            'profit_potential_per_minute': np.mean(range_sizes) * np.mean(oscillation_freq)
        }

    def visualize_range_analysis(self):
        """Create visualizations of range duration analysis"""
        if not self.ranges:
            print("No ranges to visualize. Run identify_ranges() first.")
            return

        # Extract data for plotting
        durations = [r['duration_minutes'] for r in self.ranges]
        range_sizes = [r['range_size'] for r in self.ranges]
        tick_counts = [r['tick_count'] for r in self.ranges]

        # Create subplots
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # 1. Duration histogram
        axes[0,0].hist(durations, bins=30, alpha=0.7, edgecolor='black')
        axes[0,0].set_title('Range Duration Distribution')
        axes[0,0].set_xlabel('Duration (minutes)')
        axes[0,0].set_ylabel('Frequency')
        axes[0,0].axvline(np.mean(durations), color='red', linestyle='--',
                         label=f'Mean: {np.mean(durations):.1f} min')
        axes[0,0].legend()

        # 2. Range size vs duration scatter
        axes[0,1].scatter(durations, range_sizes, alpha=0.6)
        axes[0,1].set_title('Range Size vs Duration')
        axes[0,1].set_xlabel('Duration (minutes)')
        axes[0,1].set_ylabel('Range Size (points)')

        # Add correlation
        correlation = np.corrcoef(durations, range_sizes)[0,1]
        axes[0,1].text(0.05, 0.95, f'Correlation: {correlation:.3f}',
                      transform=axes[0,1].transAxes, verticalalignment='top')

        # 3. Tick count vs duration
        axes[1,0].scatter(durations, tick_counts, alpha=0.6, color='green')
        axes[1,0].set_title('Tick Count vs Duration')
        axes[1,0].set_xlabel('Duration (minutes)')
        axes[1,0].set_ylabel('Number of Ticks')

        # 4. Duration box plot by time of day
        # Group ranges by hour
        hours = [r['start_time'].hour for r in self.ranges]
        duration_by_hour = {}
        for hour, duration in zip(hours, durations):
            if hour not in duration_by_hour:
                duration_by_hour[hour] = []
            duration_by_hour[hour].append(duration)

        if len(duration_by_hour) > 1:
            hours_sorted = sorted(duration_by_hour.keys())
            duration_data = [duration_by_hour[h] for h in hours_sorted]
            axes[1,1].boxplot(duration_data, labels=hours_sorted)
            axes[1,1].set_title('Range Duration by Hour of Day')
            axes[1,1].set_xlabel('Hour')
            axes[1,1].set_ylabel('Duration (minutes)')
        else:
            axes[1,1].text(0.5, 0.5, 'Insufficient data\nfor hourly analysis',
                          ha='center', va='center', transform=axes[1,1].transAxes)
            axes[1,1].set_title('Range Duration by Hour of Day')

        plt.tight_layout()
        plt.savefig('range_duration_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("📊 Visualization saved as 'range_duration_analysis.png'")

    def generate_summary_report(self):
        """Generate a comprehensive summary report"""
        if not self.ranges:
            print("No data to report. Run analysis first.")
            return

        print("\n" + "="*60)
        print("COMPREHENSIVE RANGE DURATION REPORT")
        print("="*60)

        # Basic statistics
        durations = [r['duration_minutes'] for r in self.ranges]

        print(f"\n📊 Key Findings:")
        print(f"   • Total range periods identified: {len(self.ranges)}")
        print(f"   • Average time in range: {np.mean(durations):.2f} minutes")
        print(f"   • Median time in range: {np.median(durations):.2f} minutes")
        print(f"   • Longest range period: {np.max(durations):.2f} minutes")
        print(f"   • Shortest range period: {np.min(durations):.2f} minutes")

        # Percentiles
        p25 = np.percentile(durations, 25)
        p75 = np.percentile(durations, 75)
        p90 = np.percentile(durations, 90)

        print(f"\n📈 Duration Percentiles:")
        print(f"   • 25th percentile: {p25:.2f} minutes")
        print(f"   • 75th percentile: {p75:.2f} minutes")
        print(f"   • 90th percentile: {p90:.2f} minutes")

        # Trading implications
        print(f"\n💡 Trading Implications:")
        print(f"   • Typical range duration: {p25:.1f} - {p75:.1f} minutes")
        print(f"   • Strategy window: Plan for {np.mean(durations):.1f} minute average holds")
        print(f"   • Risk management: Expect up to {p90:.1f} minute range periods")

        # Data coverage
        total_time = (self.df['time'].max() - self.df['time'].min()).total_seconds() / 60
        range_time = sum(durations)
        coverage = range_time / total_time * 100

        print(f"\n⏱️ Time Analysis:")
        print(f"   • Total data period: {total_time:.1f} minutes")
        print(f"   • Time spent in ranges: {range_time:.1f} minutes ({coverage:.1f}%)")
        print(f"   • Time in breakout/trending: {total_time - range_time:.1f} minutes ({100-coverage:.1f}%)")

    def run_complete_analysis(self, range_threshold=10.0, min_duration_minutes=1):
        """Run complete range duration analysis"""
        print("🔬 Starting Range Duration Analysis for Range Break Index...")

        # Load data
        if not self.load_and_prepare_data():
            return

        # Identify ranges
        self.identify_ranges(range_threshold, min_duration_minutes)

        # Analyze durations
        duration_stats = self.analyze_range_durations()

        # NEW: Analyze ceiling-floor oscillations (how trades are made!)
        oscillation_data = self.analyze_ceiling_floor_oscillations()

        # Generate oscillation statistics
        if oscillation_data:
            oscillation_stats = self.generate_oscillation_statistics(oscillation_data)

        # Analyze breakouts
        breakout_stats = self.analyze_breakout_patterns()

        # Generate visualizations
        self.visualize_range_analysis()

        # Generate summary report
        self.generate_summary_report()

        print(f"\n✅ Analysis complete!")

        return {
            'ranges': self.ranges,
            'duration_stats': duration_stats,
            'breakout_stats': breakout_stats
        }

def main():
    """Main execution function"""
    data_file = "Range_Break_100_7days_20250623_20250630.csv"

    # Create analyzer
    analyzer = RangeDurationAnalyzer(data_file)

    # Run complete analysis
    results = analyzer.run_complete_analysis(
        range_threshold=10.0,  # 10 point range threshold
        min_duration_minutes=1  # Minimum 1 minute duration
    )

    return results

if __name__ == "__main__":
    analysis_results = main()
