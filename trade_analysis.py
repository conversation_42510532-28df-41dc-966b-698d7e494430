import pandas as pd
import numpy as np
import os

# Load OHLC data
DATA_PATH = "stpRNG_1min.csv"
if not os.path.isfile(DATA_PATH):
    raise FileNotFoundError(f"File not found: {DATA_PATH}")

df = pd.read_csv(DATA_PATH)
df = df.dropna().reset_index(drop=True)

# Parameters - matching Pine Script defaults
p = 200  # Trend period
atr_p = 3  # ATR Period  
mult = 1.6  # ATR Multiplier
mode = "Type A"  # Signal mode
use_ema_smoother = "No"  # Smooth source with EMA
src_ema_period = 3  # EMA Smoother period

# Source selection
if use_ema_smoother == "Yes":
    df["src"] = df["close"].ewm(span=src_ema_period, adjust=False).mean()
else:
    df["src"] = df["close"]

# Calculate ATR manually
def calculate_atr(df, period):
    df["tr1"] = df["high"] - df["low"]
    df["tr2"] = abs(df["high"] - df["close"].shift(1))
    df["tr3"] = abs(df["low"] - df["close"].shift(1))
    df["tr"] = df[["tr1", "tr2", "tr3"]].max(axis=1)
    df["atr"] = df["tr"].rolling(window=period).mean()
    return df["atr"]

# Calculations
df["h"] = df["src"].rolling(window=p).max()  # Highest of src p-bars back
df["l"] = df["src"].rolling(window=p).min()  # Lowest of src p-bars back
df["d"] = df["h"] - df["l"]
df["m"] = (df["h"] + df["l"]) / 2  # Initial trend line

df["atr"] = calculate_atr(df, atr_p).shift(1)  # ATR shifted by 1
df["epsilon"] = mult * df["atr"]

# Initialize columns
df["trend"] = np.nan
df["last_signal"] = ""
df["signal"] = ""
df["change_up"] = False
df["change_down"] = False

# Process each bar
for i in range(len(df)):
    if i < p:
        df.at[i, "trend"] = df.at[i, "m"] if not pd.isna(df.at[i, "m"]) else np.nan
        df.at[i, "last_signal"] = ""
        continue
    
    src = df.at[i, "src"]
    prev_trend = df.at[i-1, "trend"] if i > 0 and not pd.isna(df.at[i-1, "trend"]) else df.at[i, "m"]
    epsilon = df.at[i, "epsilon"]
    
    if pd.isna(epsilon):
        df.at[i, "trend"] = prev_trend
        df.at[i, "last_signal"] = df.at[i-1, "last_signal"] if i > 0 else ""
        continue
    
    # Type A mode logic (crossover/crossunder)
    if mode == "Type A":
        change_up = src > prev_trend + epsilon
        change_down = src < prev_trend - epsilon
    else:  # Type B mode (cross)
        prev_src = df.at[i-1, "src"] if i > 0 else src
        change_up = (prev_src <= prev_trend + epsilon and src > prev_trend + epsilon) or src > prev_trend + epsilon
        change_down = (prev_src >= prev_trend - epsilon and src < prev_trend - epsilon) or src < prev_trend - epsilon
    
    df.at[i, "change_up"] = change_up
    df.at[i, "change_down"] = change_down
    
    # Strong signals logic
    h = df.at[i, "h"]
    l = df.at[i, "l"]
    d = df.at[i, "d"]
    
    # Check current and previous 4 bars for strong signals
    sb = False
    ss = False
    for j in range(max(0, i-4), i+1):
        if j < len(df):
            open_price = df.at[j, "open"]
            if open_price < l + d / 8 and open_price >= l:
                sb = True
            if open_price > h - d / 8 and open_price <= h:
                ss = True
    
    strong_buy = sb
    strong_sell = ss
    
    # Update trend line
    if change_up or change_down:
        if change_up:
            new_trend = prev_trend + epsilon
        elif change_down:
            new_trend = prev_trend - epsilon
        else:
            new_trend = prev_trend
    else:
        new_trend = prev_trend
    
    df.at[i, "trend"] = new_trend
    
    # Signal logic
    prev_last_signal = df.at[i-1, "last_signal"] if i > 0 else ""
    
    if change_up and prev_last_signal != "B":
        if strong_buy:
            signal = "strong_buy"
        else:
            signal = "buy"
        df.at[i, "last_signal"] = "B"
    elif change_down and prev_last_signal != "S":
        if strong_sell:
            signal = "strong_sell"
        else:
            signal = "sell"
        df.at[i, "last_signal"] = "S"
    else:
        signal = ""
        df.at[i, "last_signal"] = prev_last_signal
    
    df.at[i, "signal"] = signal

# Enhanced Backtest with detailed trade tracking
capital = 100_000
risk_per_trade = 0.004
balance = capital
position = 0
entry_price = 0
entry_index = 0
entry_signal = ""
trades = []

for i in range(1, len(df)):
    sig = df.at[i, "signal"]
    price = df.at[i, "open"]
    current_time = df.at[i, "datetime"] if "datetime" in df.columns else i
    
    # Exit positions
    if position > 0 and sig in ["sell", "strong_sell"]:
        pnl = (price - entry_price) / entry_price
        duration = i - entry_index
        
        trade_result = {
            'entry_time': df.at[entry_index, "datetime"] if "datetime" in df.columns else entry_index,
            'exit_time': current_time,
            'entry_price': entry_price,
            'exit_price': price,
            'entry_signal': entry_signal,
            'exit_signal': sig,
            'type': 'long',
            'duration': duration,
            'pnl_pct': pnl * 100,
            'pnl_dollar': (pnl * balance * risk_per_trade) if pnl > 0 else (-balance * risk_per_trade),
            'result': 'win' if pnl > 0 else 'loss',
            'entry_atr': df.at[entry_index, "atr"],
            'entry_epsilon': df.at[entry_index, "epsilon"],
            'entry_trend': df.at[entry_index, "trend"],
            'price_vs_trend': (entry_price - df.at[entry_index, "trend"]) / df.at[entry_index, "trend"] * 100
        }
        
        trades.append(trade_result)
        
        if pnl > 0:
            balance *= (1 + risk_per_trade)
        else:
            balance *= (1 - risk_per_trade)
        
        position = 0
        
    elif position < 0 and sig in ["buy", "strong_buy"]:
        pnl = (entry_price - price) / entry_price
        duration = i - entry_index
        
        trade_result = {
            'entry_time': df.at[entry_index, "datetime"] if "datetime" in df.columns else entry_index,
            'exit_time': current_time,
            'entry_price': entry_price,
            'exit_price': price,
            'entry_signal': entry_signal,
            'exit_signal': sig,
            'type': 'short',
            'duration': duration,
            'pnl_pct': pnl * 100,
            'pnl_dollar': (pnl * balance * risk_per_trade) if pnl > 0 else (-balance * risk_per_trade),
            'result': 'win' if pnl > 0 else 'loss',
            'entry_atr': df.at[entry_index, "atr"],
            'entry_epsilon': df.at[entry_index, "epsilon"],
            'entry_trend': df.at[entry_index, "trend"],
            'price_vs_trend': (entry_price - df.at[entry_index, "trend"]) / df.at[entry_index, "trend"] * 100
        }
        
        trades.append(trade_result)
        
        if pnl > 0:
            balance *= (1 + risk_per_trade)
        else:
            balance *= (1 - risk_per_trade)
        
        position = 0
    
    # Enter new positions
    if sig in ["buy", "strong_buy"] and position == 0:
        position = 1
        entry_price = price
        entry_index = i
        entry_signal = sig
    elif sig in ["sell", "strong_sell"] and position == 0:
        position = -1
        entry_price = price
        entry_index = i
        entry_signal = sig

# Convert trades to DataFrame for analysis
trades_df = pd.DataFrame(trades)

if len(trades_df) > 0:
    print("=== TRADE ANALYSIS REPORT ===\n")
    
    # Overall Statistics
    total_trades = len(trades_df)
    wins = len(trades_df[trades_df['result'] == 'win'])
    losses = len(trades_df[trades_df['result'] == 'loss'])
    win_rate = wins / total_trades * 100
    
    print(f"Overall Performance:")
    print(f"Total Trades: {total_trades}")
    print(f"Wins: {wins}, Losses: {losses}")
    print(f"Win Rate: {win_rate:.2f}%")
    print(f"Final Balance: ${balance:,.2f}")
    print(f"Return: {((balance - capital) / capital * 100):+.2f}%\n")
    
    # Analysis by Signal Type
    print("=== ANALYSIS BY SIGNAL TYPE ===")
    for signal_type in ['buy', 'strong_buy', 'sell', 'strong_sell']:
        signal_trades = trades_df[trades_df['entry_signal'] == signal_type]
        if len(signal_trades) > 0:
            signal_wins = len(signal_trades[signal_trades['result'] == 'win'])
            signal_losses = len(signal_trades[signal_trades['result'] == 'loss'])
            signal_wr = signal_wins / len(signal_trades) * 100
            avg_pnl = signal_trades['pnl_pct'].mean()
            
            print(f"\n{signal_type.upper()}:")
            print(f"  Trades: {len(signal_trades)}")
            print(f"  Win Rate: {signal_wr:.2f}%")
            print(f"  Average PnL: {avg_pnl:+.3f}%")
            print(f"  Best Trade: {signal_trades['pnl_pct'].max():+.3f}%")
            print(f"  Worst Trade: {signal_trades['pnl_pct'].min():+.3f}%")
    
    # Analysis by Trade Direction
    print("\n=== ANALYSIS BY TRADE DIRECTION ===")
    for direction in ['long', 'short']:
        dir_trades = trades_df[trades_df['type'] == direction]
        if len(dir_trades) > 0:
            dir_wins = len(dir_trades[dir_trades['result'] == 'win'])
            dir_losses = len(dir_trades[dir_trades['result'] == 'loss'])
            dir_wr = dir_wins / len(dir_trades) * 100
            avg_pnl = dir_trades['pnl_pct'].mean()
            
            print(f"\n{direction.upper()} trades:")
            print(f"  Trades: {len(dir_trades)}")
            print(f"  Win Rate: {dir_wr:.2f}%")
            print(f"  Average PnL: {avg_pnl:+.3f}%")
    
    # Duration Analysis
    print("\n=== DURATION ANALYSIS ===")
    avg_duration_wins = trades_df[trades_df['result'] == 'win']['duration'].mean()
    avg_duration_losses = trades_df[trades_df['result'] == 'loss']['duration'].mean()
    
    print(f"Average Duration (Wins): {avg_duration_wins:.1f} bars")
    print(f"Average Duration (Losses): {avg_duration_losses:.1f} bars")
    
    # Best and Worst Trades
    print("\n=== BEST AND WORST TRADES ===")
    best_trade = trades_df.loc[trades_df['pnl_pct'].idxmax()]
    worst_trade = trades_df.loc[trades_df['pnl_pct'].idxmin()]
    
    print(f"\nBest Trade:")
    print(f"  Entry: {best_trade['entry_signal']} at {best_trade['entry_price']:.5f}")
    print(f"  Exit: {best_trade['exit_signal']} at {best_trade['exit_price']:.5f}")
    print(f"  Duration: {best_trade['duration']} bars")
    print(f"  PnL: {best_trade['pnl_pct']:+.3f}%")
    
    print(f"\nWorst Trade:")
    print(f"  Entry: {worst_trade['entry_signal']} at {worst_trade['entry_price']:.5f}")
    print(f"  Exit: {worst_trade['exit_signal']} at {worst_trade['exit_price']:.5f}")
    print(f"  Duration: {worst_trade['duration']} bars")
    print(f"  PnL: {worst_trade['pnl_pct']:+.3f}%")
    
    # Save detailed trades to CSV
    trades_df.to_csv('detailed_trades.csv', index=False)
    print(f"\nDetailed trade data saved to 'detailed_trades.csv'")
    
    # Statistical Analysis
    print("\n=== STATISTICAL ANALYSIS ===")
    print(f"Average Win: {trades_df[trades_df['result'] == 'win']['pnl_pct'].mean():+.3f}%")
    print(f"Average Loss: {trades_df[trades_df['result'] == 'loss']['pnl_pct'].mean():+.3f}%")
    print(f"Profit Factor: {abs(trades_df[trades_df['result'] == 'win']['pnl_pct'].sum() / trades_df[trades_df['result'] == 'loss']['pnl_pct'].sum()):.2f}")
    
else:
    print("No trades found!")
