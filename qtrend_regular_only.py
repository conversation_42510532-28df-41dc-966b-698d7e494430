import pandas as pd
import numpy as np
import os

# Load OHLC data
DATA_PATH = "stpRNG_1min.csv"
if not os.path.isfile(DATA_PATH):
    raise FileNotFoundError(f"File not found: {DATA_PATH}")

df = pd.read_csv(DATA_PATH)
df = df.dropna().reset_index(drop=True)

# Parameters - matching Pine Script defaults
p = 200  # Trend period
atr_p = 3  # ATR Period  
mult = 1.6  # ATR Multiplier
mode = "Type A"  # Signal mode
use_ema_smoother = "No"  # Smooth source with EMA
src_ema_period = 3  # EMA Smoother period

# Source selection
if use_ema_smoother == "Yes":
    df["src"] = df["close"].ewm(span=src_ema_period, adjust=False).mean()
else:
    df["src"] = df["close"]

# Calculate ATR manually
def calculate_atr(df, period):
    df["tr1"] = df["high"] - df["low"]
    df["tr2"] = abs(df["high"] - df["close"].shift(1))
    df["tr3"] = abs(df["low"] - df["close"].shift(1))
    df["tr"] = df[["tr1", "tr2", "tr3"]].max(axis=1)
    df["atr"] = df["tr"].rolling(window=period).mean()
    return df["atr"]

# Calculations
df["h"] = df["src"].rolling(window=p).max()  # Highest of src p-bars back
df["l"] = df["src"].rolling(window=p).min()  # Lowest of src p-bars back
df["d"] = df["h"] - df["l"]
df["m"] = (df["h"] + df["l"]) / 2  # Initial trend line

df["atr"] = calculate_atr(df, atr_p).shift(1)  # ATR shifted by 1
df["epsilon"] = mult * df["atr"]

# Initialize columns
df["trend"] = np.nan
df["last_signal"] = ""
df["signal"] = ""
df["change_up"] = False
df["change_down"] = False

# Process each bar
for i in range(len(df)):
    if i < p:
        df.at[i, "trend"] = df.at[i, "m"] if not pd.isna(df.at[i, "m"]) else np.nan
        df.at[i, "last_signal"] = ""
        continue
    
    src = df.at[i, "src"]
    prev_trend = df.at[i-1, "trend"] if i > 0 and not pd.isna(df.at[i-1, "trend"]) else df.at[i, "m"]
    epsilon = df.at[i, "epsilon"]
    
    if pd.isna(epsilon):
        df.at[i, "trend"] = prev_trend
        df.at[i, "last_signal"] = df.at[i-1, "last_signal"] if i > 0 else ""
        continue
    
    # Type A mode logic (crossover/crossunder)
    if mode == "Type A":
        change_up = src > prev_trend + epsilon
        change_down = src < prev_trend - epsilon
    else:  # Type B mode (cross)
        prev_src = df.at[i-1, "src"] if i > 0 else src
        change_up = (prev_src <= prev_trend + epsilon and src > prev_trend + epsilon) or src > prev_trend + epsilon
        change_down = (prev_src >= prev_trend - epsilon and src < prev_trend - epsilon) or src < prev_trend - epsilon
    
    df.at[i, "change_up"] = change_up
    df.at[i, "change_down"] = change_down
    
    # Strong signals logic
    h = df.at[i, "h"]
    l = df.at[i, "l"]
    d = df.at[i, "d"]
    
    # Check current and previous 4 bars for strong signals
    sb = False
    ss = False
    for j in range(max(0, i-4), i+1):
        if j < len(df):
            open_price = df.at[j, "open"]
            if open_price < l + d / 8 and open_price >= l:
                sb = True
            if open_price > h - d / 8 and open_price <= h:
                ss = True
    
    strong_buy = sb
    strong_sell = ss
    
    # Update trend line
    if change_up or change_down:
        if change_up:
            new_trend = prev_trend + epsilon
        elif change_down:
            new_trend = prev_trend - epsilon
        else:
            new_trend = prev_trend
    else:
        new_trend = prev_trend
    
    df.at[i, "trend"] = new_trend
    
    # Signal logic - ONLY REGULAR SIGNALS
    prev_last_signal = df.at[i-1, "last_signal"] if i > 0 else ""
    
    if change_up and prev_last_signal != "B":
        # Only use regular buy signal (ignore strong_buy)
        if not strong_buy:  # Only if NOT a strong signal
            signal = "buy"
            df.at[i, "last_signal"] = "B"
        else:
            signal = ""
            df.at[i, "last_signal"] = prev_last_signal
    elif change_down and prev_last_signal != "S":
        # Only use regular sell signal (ignore strong_sell)
        if not strong_sell:  # Only if NOT a strong signal
            signal = "sell"
            df.at[i, "last_signal"] = "S"
        else:
            signal = ""
            df.at[i, "last_signal"] = prev_last_signal
    else:
        signal = ""
        df.at[i, "last_signal"] = prev_last_signal
    
    df.at[i, "signal"] = signal

# Backtest Simulator - Only Regular Signals
capital = 100_000
risk_per_trade = 0.004
balance = capital
position = 0
entry_price = 0
entry_index = 0
entry_signal = ""
trades = []

for i in range(1, len(df)):
    sig = df.at[i, "signal"]
    price = df.at[i, "open"]
    current_time = df.at[i, "datetime"] if "datetime" in df.columns else i
    
    # Exit positions - only on regular signals
    if position > 0 and sig == "sell":  # Only regular sell
        pnl = (price - entry_price) / entry_price
        duration = i - entry_index
        
        trade_result = {
            'entry_time': df.at[entry_index, "datetime"] if "datetime" in df.columns else entry_index,
            'exit_time': current_time,
            'entry_price': entry_price,
            'exit_price': price,
            'entry_signal': entry_signal,
            'exit_signal': sig,
            'type': 'long',
            'duration': duration,
            'pnl_pct': pnl * 100,
            'result': 'win' if pnl > 0 else 'loss'
        }
        
        trades.append(trade_result)
        
        if pnl > 0:
            balance *= (1 + risk_per_trade)
        else:
            balance *= (1 - risk_per_trade)
        
        position = 0
        
    elif position < 0 and sig == "buy":  # Only regular buy
        pnl = (entry_price - price) / entry_price
        duration = i - entry_index
        
        trade_result = {
            'entry_time': df.at[entry_index, "datetime"] if "datetime" in df.columns else entry_index,
            'exit_time': current_time,
            'entry_price': entry_price,
            'exit_price': price,
            'entry_signal': entry_signal,
            'exit_signal': sig,
            'type': 'short',
            'duration': duration,
            'pnl_pct': pnl * 100,
            'result': 'win' if pnl > 0 else 'loss'
        }
        
        trades.append(trade_result)
        
        if pnl > 0:
            balance *= (1 + risk_per_trade)
        else:
            balance *= (1 - risk_per_trade)
        
        position = 0
    
    # Enter new positions - only on regular signals
    if sig == "buy" and position == 0:  # Only regular buy
        position = 1
        entry_price = price
        entry_index = i
        entry_signal = sig
    elif sig == "sell" and position == 0:  # Only regular sell
        position = -1
        entry_price = price
        entry_index = i
        entry_signal = sig

# Results
total_trades = len(trades)
wins = len([t for t in trades if t['result'] == 'win'])
losses = len([t for t in trades if t['result'] == 'loss'])
win_rate = wins / total_trades * 100 if total_trades > 0 else 0

print("=== REGULAR SIGNALS ONLY BACKTEST ===\n")
print(f"Final balance: ${balance:,.2f}")
print(f"Total trades: {total_trades}, Wins: {wins}, Losses: {losses}, Win rate: {win_rate:.2f}%")
print(f"Return: {((balance - capital) / capital * 100):+.2f}%")

# Signal counts
signal_counts = df['signal'].value_counts()
print(f"\nSignal counts:")
for signal, count in signal_counts.items():
    if signal != "":
        print(f"  {signal}: {count}")

# Compare to original results
print(f"\n=== COMPARISON ===")
print(f"Original (all signals): +30.34% return, 375 trades, 58.93% win rate")
print(f"Regular only: {((balance - capital) / capital * 100):+.2f}% return, {total_trades} trades, {win_rate:.2f}% win rate")

# Save trades
if trades:
    trades_df = pd.DataFrame(trades)
    trades_df.to_csv('regular_signals_trades.csv', index=False)
    print(f"\nRegular signals trade data saved to 'regular_signals_trades.csv'")
