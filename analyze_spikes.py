import pandas as pd
import numpy as np

def analyze_spikes(file_path, range_std_dev_threshold=3, pct_change_threshold=0.01):
    """
    Analyzes price data for spikes based on bar range and percentage change.

    Args:
        file_path (str): The path to the CSV file containing OHLC data.
        range_std_dev_threshold (int): Number of standard deviations above mean range to consider a spike.
        pct_change_threshold (float): Percentage change from previous close to high/low to consider a spike.
    """
    df = pd.read_csv(file_path)
    df['time'] = pd.to_datetime(df['time'])
    df = df.set_index('time')

    # Calculate bar range
    df['range'] = df['high'] - df['low']

    # Calculate percentage change from previous close to current high/low
    df['high_pct_change'] = (df['high'] - df['close'].shift(1)) / df['close'].shift(1)
    df['low_pct_change'] = (df['low'] - df['close'].shift(1)) / df['close'].shift(1)

    # Define spike conditions
    # Condition 1: Range is significantly larger than average
    mean_range = df['range'].mean()
    std_range = df['range'].std()
    range_spike_threshold = mean_range + (range_std_dev_threshold * std_range)
    spikes_by_range = df[df['range'] > range_spike_threshold]

    # Condition 2: Significant percentage change from previous close
    spikes_by_pct_change_high = df[df['high_pct_change'].abs() > pct_change_threshold]
    spikes_by_pct_change_low = df[df['low_pct_change'].abs() > pct_change_threshold]

    # Combine spikes (using index to avoid duplicates)
    all_spike_indices = spikes_by_range.index.union(spikes_by_pct_change_high.index).union(spikes_by_pct_change_low.index)
    spikes_df = df.loc[all_spike_indices].copy()

    print(f"--- Spike Analysis for {file_path} ---")
    print(f"Total bars: {len(df)}")
    print(f"Number of identified spikes: {len(spikes_df)}")

    if not spikes_df.empty:
        print("\n--- Spike Details (first 5 rows) ---")
        print(spikes_df.head())
        print("\n--- Descriptive Statistics for Spikes (Range and Pct Change) ---")
        print(spikes_df[['range', 'high_pct_change', 'low_pct_change']].describe())
    else:
        print("No spikes identified based on the given thresholds.")

if __name__ == "__main__":
    csv_file_path = r"C:\Users\<USER>\META\Boom_1000_Index_1-minute_90days_25920_bars.csv"
    analyze_spikes(csv_file_path, range_std_dev_threshold=3, pct_change_threshold=0.005) # Adjusted pct_change_threshold for more sensitivity
