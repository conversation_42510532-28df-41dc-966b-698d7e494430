"""
Enhanced Boom 1000 Index Spike Analysis
More sensitive spike detection for better analysis
"""

import pandas as pd
import numpy as np
from datetime import datetime
import re

class EnhancedBoomAnalyzer:
    def __init__(self, csv_file):
        self.csv_file = csv_file
        self.df = None
        self.spikes = []
        self.double_spikes = []
        
    def load_and_parse_data(self):
        """Load and parse the tick data from CSV"""
        print("Loading tick data...")
        
        # Read the raw CSV
        raw_df = pd.read_csv(self.csv_file)
        
        # Parse the tick tuple data
        parsed_data = []
        
        for _, row in raw_df.iterrows():
            try:
                # Extract the tuple string
                tuple_str = row['0']
                # Parse the tuple
                if tuple_str.startswith('(') and tuple_str.endswith(')'):
                    # Remove parentheses and split by comma
                    values = tuple_str[1:-1].split(', ')
                    
                    tick_data = {
                        'timestamp': int(values[0]),
                        'bid': float(values[1]),
                        'ask': float(values[2]),
                        'last': float(values[3]),
                        'volume': int(values[4]),
                        'timestamp_msc': int(values[5]),
                        'flags': int(values[6]),
                        'volume_real': float(values[7]),
                        'symbol': row['symbol']
                    }
                    parsed_data.append(tick_data)
                    
            except Exception as e:
                continue
        
        # Convert to DataFrame
        self.df = pd.DataFrame(parsed_data)
        
        # Convert timestamps
        self.df['datetime'] = pd.to_datetime(self.df['timestamp'], unit='s')
        self.df['datetime_msc'] = pd.to_datetime(self.df['timestamp_msc'], unit='ms')
        
        # Calculate mid price
        self.df['mid_price'] = (self.df['bid'] + self.df['ask']) / 2
        
        # Sort by timestamp
        self.df = self.df.sort_values('timestamp_msc').reset_index(drop=True)
        
        print(f"Loaded {len(self.df)} ticks")
        print(f"Date range: {self.df['datetime'].min()} to {self.df['datetime'].max()}")
        
        return self.df
    
    def detect_boom_spikes(self):
        """
        Enhanced spike detection specifically for Boom 1000 Index
        Uses multiple methods to identify spikes
        """
        print("Detecting Boom 1000 spikes...")
        
        # Calculate various price change metrics
        self.df['price_change'] = self.df['mid_price'].diff()
        self.df['price_change_pct'] = (self.df['price_change'] / self.df['mid_price'].shift(1)) * 100
        
        # Rolling statistics for dynamic thresholds
        window = 100
        self.df['rolling_mean'] = self.df['price_change'].rolling(window=window).mean()
        self.df['rolling_std'] = self.df['price_change'].rolling(window=window).std()
        
        # Multiple spike detection methods
        
        # Method 1: Percentage threshold (more sensitive)
        pct_spikes = self.df[
            (self.df['price_change_pct'] > 0.1) &  # Very low threshold
            (self.df['price_change'] > 0)
        ].index.tolist()
        
        # Method 2: Standard deviation based
        std_threshold = 2.5
        std_spikes = self.df[
            (self.df['price_change'] > self.df['rolling_mean'] + std_threshold * self.df['rolling_std']) &
            (self.df['price_change'] > 0)
        ].index.tolist()
        
        # Method 3: Absolute price jump (for Boom 1000, even small jumps can be significant)
        abs_threshold = 0.5  # 0.5 price units
        abs_spikes = self.df[
            (self.df['price_change'] > abs_threshold)
        ].index.tolist()
        
        # Combine all methods
        all_spike_candidates = list(set(pct_spikes + std_spikes + abs_spikes))
        all_spike_candidates.sort()
        
        # Filter spikes to avoid duplicates too close together
        filtered_spikes = []
        min_distance = 5  # Minimum 5 ticks between spikes
        
        for spike_idx in all_spike_candidates:
            # Check if this spike is far enough from the last one
            if not filtered_spikes or spike_idx - filtered_spikes[-1] >= min_distance:
                filtered_spikes.append(spike_idx)
        
        self.spikes = filtered_spikes
        
        # Mark spikes in dataframe
        self.df['is_spike'] = False
        self.df.loc[self.spikes, 'is_spike'] = True
        
        print(f"Found {len(self.spikes)} spikes")
        
        # Detect double spikes
        self.detect_double_spikes()
        
        # Add spike analysis
        self.analyze_spike_characteristics()
        
        return self.spikes
    
    def detect_double_spikes(self):
        """Detect double spikes - spikes that occur within 50-100 ticks of each other"""
        double_spikes = []
        
        for i in range(len(self.spikes) - 1):
            current_spike = self.spikes[i]
            next_spike = self.spikes[i + 1]
            
            # If next spike is within 50-100 ticks (typical double spike pattern)
            tick_distance = next_spike - current_spike
            if 10 <= tick_distance <= 100:
                double_spikes.append((current_spike, next_spike, tick_distance))
        
        self.double_spikes = double_spikes
        print(f"Found {len(self.double_spikes)} double spike pairs")
        
        return self.double_spikes
    
    def analyze_spike_characteristics(self):
        """Analyze characteristics of detected spikes"""
        if not self.spikes:
            return
        
        spike_data = self.df.loc[self.spikes].copy()
        
        print(f"\\nSpike Characteristics:")
        print(f"Average spike size: {spike_data['price_change'].mean():.3f} price units")
        print(f"Average spike percentage: {spike_data['price_change_pct'].mean():.3f}%")
        print(f"Largest spike: {spike_data['price_change'].max():.3f} price units")
        print(f"Largest spike percentage: {spike_data['price_change_pct'].max():.3f}%")
    
    def calculate_ticks_before_spikes(self):
        """Calculate comprehensive statistics about ticks before spikes"""
        if not self.spikes:
            print("No spikes detected.")
            return None
        
        ticks_before_spikes = []
        
        for i, spike_idx in enumerate(self.spikes):
            if i == 0:
                # First spike - count from beginning
                ticks_between = spike_idx
            else:
                # Count from previous spike
                prev_spike_idx = self.spikes[i-1]
                ticks_between = spike_idx - prev_spike_idx
            
            ticks_before_spikes.append(ticks_between)
        
        stats = {
            'average': np.mean(ticks_before_spikes),
            'median': np.median(ticks_before_spikes),
            'min': min(ticks_before_spikes),
            'max': max(ticks_before_spikes),
            'std': np.std(ticks_before_spikes),
            'all_values': ticks_before_spikes
        }
        
        print(f"\\nTicks Before Spikes Analysis:")
        print(f"Average: {stats['average']:.1f} ticks")
        print(f"Median: {stats['median']:.1f} ticks")
        print(f"Range: {stats['min']} - {stats['max']} ticks")
        print(f"Standard Deviation: {stats['std']:.1f} ticks")
        
        # Calculate time-based statistics
        if len(self.spikes) > 1:
            time_intervals = []
            for i in range(1, len(self.spikes)):
                time_diff = (self.df.iloc[self.spikes[i]]['datetime'] - 
                           self.df.iloc[self.spikes[i-1]]['datetime']).total_seconds()
                time_intervals.append(time_diff)
            
            avg_time_between = np.mean(time_intervals)
            print(f"Average time between spikes: {avg_time_between/60:.1f} minutes")
        
        return stats
    
    def calculate_tdi_enhanced(self, period=13):
        """Enhanced TDI calculation with additional indicators"""
        print(f"Calculating enhanced TDI...")
        
        # Basic RSI calculation
        delta = self.df['mid_price'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        # TDI components
        self.df['rsi'] = rsi
        self.df['rsi_ma'] = rsi.rolling(window=period).mean()
        self.df['rsi_signal'] = rsi.rolling(window=7).mean()
        
        # Bollinger Bands on RSI
        rsi_std = rsi.rolling(window=period).std()
        self.df['rsi_upper_bb'] = self.df['rsi_ma'] + (2 * rsi_std)
        self.df['rsi_lower_bb'] = self.df['rsi_ma'] - (2 * rsi_std)
        
        # Additional momentum indicators
        self.df['momentum'] = self.df['mid_price'] - self.df['mid_price'].shift(10)
        self.df['rate_of_change'] = ((self.df['mid_price'] / self.df['mid_price'].shift(10)) - 1) * 100
        
        return self.df[['rsi', 'rsi_ma', 'rsi_signal', 'rsi_upper_bb', 'rsi_lower_bb', 'momentum', 'rate_of_change']]
    
    def analyze_post_spike_opportunities(self, lookback_ticks=100):
        """Enhanced analysis of post-spike opportunities using TDI"""
        if not self.spikes:
            print("No spikes detected.")
            return None
        
        if 'rsi' not in self.df.columns:
            self.calculate_tdi_enhanced()
        
        post_spike_analysis = []
        
        for i, spike_idx in enumerate(self.spikes):
            # Look ahead after the spike
            end_idx = min(spike_idx + lookback_ticks, len(self.df) - 1)
            post_spike_data = self.df.iloc[spike_idx:end_idx + 1].copy()
            
            if len(post_spike_data) < 20:  # Need sufficient data
                continue
            
            # Enhanced favorable conditions using multiple TDI components
            favorable_conditions = (
                # RSI oversold/overbought
                (post_spike_data['rsi'] < 30) |
                (post_spike_data['rsi'] > 70) |
                # RSI signal crossovers
                (post_spike_data['rsi_signal'] < post_spike_data['rsi_ma']) |
                # RSI touching Bollinger Bands
                (post_spike_data['rsi'] <= post_spike_data['rsi_lower_bb']) |
                (post_spike_data['rsi'] >= post_spike_data['rsi_upper_bb']) |
                # Momentum conditions
                (post_spike_data['momentum'] > 0.1) |
                (post_spike_data['rate_of_change'] > 0.05)
            )
            
            favorable_ticks = post_spike_data[favorable_conditions]
            
            # Check if this is part of a double spike
            is_double = any(spike_idx in [pair[0], pair[1]] for pair in self.double_spikes)
            
            spike_info = {
                'spike_idx': spike_idx,
                'spike_number': i + 1,
                'spike_time': self.df.iloc[spike_idx]['datetime'],
                'spike_price': self.df.iloc[spike_idx]['mid_price'],
                'spike_change': self.df.iloc[spike_idx]['price_change'],
                'spike_change_pct': self.df.iloc[spike_idx]['price_change_pct'],
                'post_spike_ticks': len(post_spike_data),
                'favorable_ticks': len(favorable_ticks),
                'favorable_percentage': (len(favorable_ticks) / len(post_spike_data)) * 100,
                'is_double_spike': is_double
            }
            
            post_spike_analysis.append(spike_info)
        
        # Calculate comprehensive statistics
        if post_spike_analysis:
            avg_favorable_pct = np.mean([s['favorable_percentage'] for s in post_spike_analysis])
            perfect_opportunities = [s for s in post_spike_analysis if s['favorable_percentage'] >= 90.0]
            good_opportunities = [s for s in post_spike_analysis if s['favorable_percentage'] >= 80.0]
            
            print(f"\\nPost-Spike TDI Analysis (Next {lookback_ticks} ticks):")
            print(f"Average favorable tick percentage: {avg_favorable_pct:.1f}%")
            print(f"Spikes with 90%+ favorable ticks: {len(perfect_opportunities)}")
            print(f"Spikes with 80%+ favorable ticks: {len(good_opportunities)}")
            print(f"Success rate for 90%+ winning trades: {len(perfect_opportunities)/len(post_spike_analysis)*100:.1f}%")
            
            if perfect_opportunities:
                print(f"\\nTop Opportunities (90%+ favorable):")
                for opp in perfect_opportunities[:10]:
                    double_indicator = " [DOUBLE]" if opp['is_double_spike'] else ""
                    print(f"  Spike {opp['spike_number']}: {opp['spike_time'].strftime('%m-%d %H:%M:%S')} - "
                          f"{opp['spike_change']:.3f} units ({opp['spike_change_pct']:.2f}%) - "
                          f"{opp['favorable_percentage']:.1f}% favorable{double_indicator}")
        
        return post_spike_analysis
    
    def generate_comprehensive_report(self):
        """Generate detailed analysis report"""
        print("\\n" + "="*100)
        print("COMPREHENSIVE BOOM 1000 INDEX SPIKE ANALYSIS REPORT")
        print("="*100)
        
        # Data overview
        duration_hours = (self.df['datetime'].max() - self.df['datetime'].min()).total_seconds() / 3600
        ticks_per_hour = len(self.df) / duration_hours
        
        print(f"\\nDATA OVERVIEW:")
        print(f"{'Total ticks analyzed:':<30} {len(self.df):,}")
        print(f"{'Analysis period:':<30} {self.df['datetime'].min()} to {self.df['datetime'].max()}")
        print(f"{'Duration:':<30} {duration_hours:.1f} hours ({duration_hours/24:.1f} days)")
        print(f"{'Average ticks per hour:':<30} {ticks_per_hour:.0f}")
        
        # Spike analysis
        if self.spikes:
            ticks_stats = self.calculate_ticks_before_spikes()
            post_spike_stats = self.analyze_post_spike_opportunities()
            
            spike_frequency = len(self.df) / len(self.spikes)
            spike_frequency_time = duration_hours / len(self.spikes)
            
            print(f"\\nSPIKE ANALYSIS:")
            print(f"{'Total spikes detected:':<30} {len(self.spikes)}")
            print(f"{'Double spike pairs:':<30} {len(self.double_spikes)}")
            print(f"{'Spike frequency:':<30} {spike_frequency:.0f} ticks per spike")
            print(f"{'Time between spikes:':<30} {spike_frequency_time:.1f} hours on average")
            
            print(f"\\nSPIKE TIMING STATISTICS:")
            print(f"{'Average ticks before spike:':<30} {ticks_stats['average']:.0f}")
            print(f"{'Median ticks before spike:':<30} {ticks_stats['median']:.0f}")
            print(f"{'Min-Max range:':<30} {ticks_stats['min']}-{ticks_stats['max']} ticks")
            
            if post_spike_stats:
                perfect_ops = [s for s in post_spike_stats if s['favorable_percentage'] >= 90.0]
                good_ops = [s for s in post_spike_stats if s['favorable_percentage'] >= 80.0]
                
                print(f"\\nTDI OPPORTUNITY ANALYSIS:")
                print(f"{'90%+ favorable opportunities:':<30} {len(perfect_ops)} ({len(perfect_ops)/len(post_spike_stats)*100:.1f}%)")
                print(f"{'80%+ favorable opportunities:':<30} {len(good_ops)} ({len(good_ops)/len(post_spike_stats)*100:.1f}%)")
                
                # Double spike analysis
                double_spike_opportunities = [s for s in post_spike_stats if s['is_double_spike'] and s['favorable_percentage'] >= 90.0]
                if self.double_spikes:
                    print(f"{'Double spike 90%+ opportunities:':<30} {len(double_spike_opportunities)} out of {len(self.double_spikes)*2} double spike events")
        
        print("\\n" + "="*100)
        
        return {
            'total_ticks': len(self.df),
            'total_spikes': len(self.spikes),
            'double_spikes': len(self.double_spikes),
            'avg_ticks_before_spike': ticks_stats['average'] if self.spikes else 0,
            'duration_hours': duration_hours
        }

def main():
    """Main analysis function"""
    csv_file = "C:\\Users\\<USER>\\META\\Boom_1000_Index_7days_20250619_20250626.csv"
    analyzer = EnhancedBoomAnalyzer(csv_file)
    
    # Load and parse data
    df = analyzer.load_and_parse_data()
    
    # Detect spikes with enhanced method
    spikes = analyzer.detect_boom_spikes()
    
    # Calculate enhanced TDI
    tdi_data = analyzer.calculate_tdi_enhanced()
    
    # Generate comprehensive report
    report = analyzer.generate_comprehensive_report()
    
    return analyzer, report

if __name__ == "__main__":
    analyzer, report = main()
