import pandas as pd
import numpy as np

def smoothrange(x, t, m):
    """Smooth Average Range"""
    wper = (t * 2) - 1
    avrng = abs(x - x.shift(1)).ewm(span=t, adjust=False).mean()
    return avrng.ewm(span=wper, adjust=False).mean() * m

def rangefilter(x, r):
    """Range Filter"""
    rf = x.copy()
    for i in range(1, len(x)):
        if x[i] > rf[i-1]:
            rf[i] = rf[i-1] if (x[i] - r[i]) < rf[i-1] else (x[i] - r[i])
        else:
            rf[i] = rf[i-1] if (x[i] + r[i]) > rf[i-1] else (x[i] + r[i])
    return rf

def smart_labelling_range_filter(df, price_col='close', mult=.382, blabels=True):
    """
    Python implementation of the Smart Labelling - Range Filter strategy.
    """
    price = df[price_col]
    smthr = smoothrange(price, 1440, mult)
    rfilt = rangefilter(price, smthr)

    upward = pd.Series(np.zeros(len(df)))
    downward = pd.Series(np.zeros(len(df)))

    for i in range(1, len(df)):
        if rfilt[i] > rfilt[i-1]:
            upward[i] = upward[i-1] + 1 if not pd.isna(upward[i-1]) else 1
        elif rfilt[i] < rfilt[i-1]:
            upward[i] = 0
        else:
            upward[i] = upward[i-1] if not pd.isna(upward[i-1]) else 0

        if rfilt[i] < rfilt[i-1]:
            downward[i] = downward[i-1] + 1 if not pd.isna(downward[i-1]) else 1
        elif rfilt[i] > rfilt[i-1]:
            downward[i] = 0
        else:
            downward[i] = downward[i-1] if not pd.isna(downward[i-1]) else 0

    state = pd.Series(np.zeros(len(df)))
    for i in range(1, len(df)):
        if upward[i]:
            state[i] = 1
        elif downward[i]:
            state[i] = -1
        else:
            state[i] = state[i-1] if not pd.isna(state[i-1]) else 0

    long = pd.Series(np.zeros(len(df), dtype=bool))
    short = pd.Series(np.zeros(len(df), dtype=bool))

    for i in range(1, len(df)):
        long[i] = (state[i] != state[i-1]) and (state[i-1] == -1) if not pd.isna(state[i-1]) else False
        short[i] = (state[i] != state[i-1]) and (state[i-1] == 1) if not pd.isna(state[i-1]) else False

    df['smthr'] = smthr
    df['rfilt'] = rfilt
    df['upward'] = upward
    df['downward'] = downward
    df['state'] = state
    df['long'] = long
    df['short'] = short

    return df

def deriv_zero_spread_backtest(df, initial_position=1, lot_size=1.0, commission_per_lot=0):
    """
    Backtest for Deriv zero-spread synthetic symbols with always-in-market strategy.
    
    Args:
        df: DataFrame with signals
        initial_position: 1 for long, -1 for short starting position
        lot_size: Fixed lot size for all trades
        commission_per_lot: Commission per lot (if any)
    """
    
    # Initialize tracking variables
    df['position'] = 0
    df['trade_pnl'] = 0.0
    df['cumulative_pnl'] = 0.0
    df['execution_price'] = df['close']  # Zero spread = close price execution
    
    current_position = initial_position
    entry_price = df['close'].iloc[0]
    total_pnl = 0
    trade_count = 0
    
    # Track all position changes
    position_changes = []
    
    for i in range(len(df)):
        df.loc[i, 'position'] = current_position
        
        # Check for signal (position reversal needed)
        signal_triggered = False
        new_position = current_position
        
        if df['long'].iloc[i] and current_position != 1:
            new_position = 1
            signal_triggered = True
        elif df['short'].iloc[i] and current_position != -1:
            new_position = -1
            signal_triggered = True
        
        if signal_triggered:
            # Execute position reversal at bar close price
            exit_price = df['close'].iloc[i]
            
            # Calculate PnL from previous position
            if current_position == 1:  # Was long
                pnl = (exit_price - entry_price) * lot_size
            else:  # Was short
                pnl = (entry_price - exit_price) * lot_size
            
            # Account for commission (double commission for reversal)
            pnl -= (commission_per_lot * lot_size * 2)
            
            df.loc[i, 'trade_pnl'] = pnl
            total_pnl += pnl
            trade_count += 1
            
            # Record position change
            position_changes.append({
                'bar': i,
                'timestamp': df.index[i] if hasattr(df.index, 'name') else i,
                'from_position': current_position,
                'to_position': new_position,
                'execution_price': exit_price,
                'pnl': pnl,
                'cumulative_pnl': total_pnl
            })
            
            # Update for new position
            current_position = new_position
            entry_price = exit_price  # New entry at same price (zero spread)
        
        df.loc[i, 'cumulative_pnl'] = total_pnl
    
    # Calculate final statistics
    position_changes_df = pd.DataFrame(position_changes)
    
    if len(position_changes_df) > 0:
        winning_trades = position_changes_df[position_changes_df['pnl'] > 0]
        losing_trades = position_changes_df[position_changes_df['pnl'] < 0]
        
        win_rate = len(winning_trades) / len(position_changes_df) * 100
        avg_win = winning_trades['pnl'].mean() if len(winning_trades) > 0 else 0
        avg_loss = losing_trades['pnl'].mean() if len(losing_trades) > 0 else 0
        
        stats = {
            'total_trades': len(position_changes_df),
            'total_pnl': total_pnl,
            'win_rate': win_rate,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': abs(winning_trades['pnl'].sum() / losing_trades['pnl'].sum()) if len(losing_trades) > 0 and losing_trades['pnl'].sum() != 0 else float('inf'),
            'max_drawdown': (position_changes_df['cumulative_pnl'] - position_changes_df['cumulative_pnl'].cummax()).min(),
            'sharpe_ratio': position_changes_df['pnl'].mean() / position_changes_df['pnl'].std() if position_changes_df['pnl'].std() != 0 else 0
        }
    else:
        stats = {'total_trades': 0, 'total_pnl': 0}
    
    return df, position_changes_df, stats

if __name__ == '__main__':
    # Example usage with Deriv synthetic symbol
    file_path = 'stpRNG_1min.csv'
    try:
        df = pd.read_csv(file_path)
        print("Data loaded successfully:")
        print(df.head())
        
        # Apply the range filter strategy
        df = smart_labelling_range_filter(df, price_col='close')
        
        # Run Deriv-specific backtest
        df_results, trades_df, stats = deriv_zero_spread_backtest(
            df, 
            initial_position=1,  # Start long
            lot_size=1.0,        # 1 lot per trade
            commission_per_lot=0  # No commission for Deriv synthetics
        )
        
        print("\n=== DERIV ZERO-SPREAD BACKTEST RESULTS ===")
        print(f"Total Trades: {stats['total_trades']}")
        print(f"Total P&L: {stats['total_pnl']:.2f}")
        print(f"Win Rate: {stats['win_rate']:.1f}%")
        print(f"Average Win: {stats['avg_win']:.2f}")
        print(f"Average Loss: {stats['avg_loss']:.2f}")
        print(f"Profit Factor: {stats['profit_factor']:.2f}")
        print(f"Max Drawdown: {stats['max_drawdown']:.2f}")
        print(f"Sharpe Ratio: {stats['sharpe_ratio']:.2f}")
        
        if len(trades_df) > 0:
            print(f"\nFirst 5 Trades:")
            print(trades_df.head())
            
            print(f"\nLast 5 Trades:")
            print(trades_df.tail())
        
    except FileNotFoundError:
        print(f"Error: File not found at {file_path}")
    except Exception as e:
        print(f"An error occurred: {e}")