"""
DEFINITIVE Boom 1000 Index Backtest - As per BOOM_ANALYSIS_SUMMARY.md
Strategy: 
1. SELL IMMEDIATELY after a spike.
2. Use TDI SIGNALS within the next 100 ticks to find the OPTIMAL EXIT.
"""

import pandas as pd
import numpy as np
from datetime import datetime

class DefinitiveBoomBacktester:
    def __init__(self, csv_file, starting_capital=20):
        self.csv_file = csv_file
        self.starting_capital = starting_capital
        self.current_capital = starting_capital
        
        # Boom 1000 Specifications
        self.min_volume = 0.20
        self.position_size = 20.0  # Fixed lots for analysis
        self.tick_size = 0.001
        self.tick_value = 0.001
        
        # Strategy Parameters from Summary
        self.exit_window = 150  # Look for exit signal within this window
        
        # Results Tracking
        self.trades = []
        self.df = None
        self.spikes = []
        
    def load_and_parse_data(self):
        """Load and parse tick data"""
        print("Loading data for DEFINITIVE backtest...")
        raw_df = pd.read_csv(self.csv_file)
        parsed_data = []
        for _, row in raw_df.iterrows():
            try:
                tuple_str = row['0']
                if tuple_str.startswith('(') and tuple_str.endswith(')'):
                    values = tuple_str[1:-1].split(', ')
                    parsed_data.append({
                        'timestamp': int(values[0]),
                        'bid': float(values[1]),
                        'ask': float(values[2]),
                        'mid_price': (float(values[1]) + float(values[2])) / 2
                    })
            except: continue
        self.df = pd.DataFrame(parsed_data)
        self.df['datetime'] = pd.to_datetime(self.df['timestamp'], unit='s')
        self.df = self.df.sort_values('timestamp').reset_index(drop=True)
        print(f"Loaded {len(self.df)} ticks")
        return self.df

    def detect_spikes(self):
        """Detect spikes using the method from the summary"""
        print("Detecting spikes...")
        self.df['price_change'] = self.df['mid_price'].diff()
        self.df['price_change_pct'] = (self.df['price_change'] / self.df['mid_price'].shift(1)) * 100
        pct_spikes = self.df[(self.df['price_change_pct'] > 0.1) & (self.df['price_change'] > 0)].index
        abs_spikes = self.df[self.df['price_change'] > 0.5].index
        all_spikes = sorted(list(set(pct_spikes) | set(abs_spikes)))
        self.spikes = [s for i, s in enumerate(all_spikes) if i == 0 or s - all_spikes[i-1] >= 5]
        print(f"Detected {len(self.spikes)} spikes")
        return self.spikes

    def calculate_tdi(self, period=13):
        """Calculate TDI indicators as per the summary's methodology"""
        print("Calculating TDI indicators...")
        delta = self.df['mid_price'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        self.df['rsi'] = rsi
        self.df['rsi_ma'] = rsi.rolling(window=period).mean()
        self.df['rsi_signal'] = rsi.rolling(window=7).mean()
        return True

    def execute_definitive_strategy(self):
        """Execute the TDI-based SELL-ONLY strategy exactly as per summary logic"""
        print("Executing Definitive Strategy: SELL on spike, exit on TDI signal")
        self.current_capital = self.starting_capital

        for spike_idx in self.spikes:
            if spike_idx + self.exit_window >= len(self.df):
                continue

            # --- ENTRY: Immediately after spike ---
            entry_idx = spike_idx + 1
            entry_price = self.df.iloc[entry_idx]['ask']

            # --- EXIT: Find TDI signal within the collection window ---
            exit_window_df = self.df.iloc[entry_idx : spike_idx + self.exit_window]
            
            # Exit Signal: Price reversion is over. We look for RSI crossing back UP over a threshold (e.g., 40)
            # This indicates the downward momentum (our profit direction) is fading.
            exit_signals = exit_window_df[exit_window_df['rsi'] > 40]
            
            if not exit_signals.empty:
                exit_idx = exit_signals.index[0]
            else:
                # If no clear exit signal, exit at the end of the window (failsafe)
                exit_idx = exit_window_df.index[-1]

            exit_price = self.df.iloc[exit_idx]['bid']
            
            # --- P&L Calculation ---
            price_change = entry_price - exit_price
            ticks_gained = price_change / self.tick_size
            final_pnl = ticks_gained * self.tick_value * self.position_size
            
            # --- Detailed Tracking ---
            trade_period = self.df.iloc[entry_idx:exit_idx+1]
            highest_ask = trade_period['ask'].max()
            worst_price_move = highest_ask - entry_price
            worst_unrealized_pnl = -(worst_price_move / self.tick_size * self.tick_value * self.position_size)

            previous_balance = self.current_capital
            self.current_capital += final_pnl
            
            self.trades.append({
                'trade_num': len(self.trades) + 1,
                'pnl': final_pnl,
                'entry_time': self.df.iloc[entry_idx]['datetime'],
                'exit_time': self.df.iloc[exit_idx]['datetime'],
                'entry_rsi': self.df.iloc[entry_idx]['rsi'],
                'exit_rsi': self.df.iloc[exit_idx]['rsi'],
                'duration': exit_idx - entry_idx,
                'worst_unrealized_pnl': worst_unrealized_pnl,
                'final_balance': self.current_capital
            })

        print(f"Executed {len(self.trades)} trades.")
        return self.trades

    def analyze_results(self):
        """Analyze and print the definitive results"""
        if not self.trades:
            print("No trades were executed.")
            return

        trades_df = pd.DataFrame(self.trades)
        winning_trades = trades_df[trades_df['pnl'] > 0]
        losing_trades = trades_df[trades_df['pnl'] <= 0]

        win_rate = len(winning_trades) / len(trades_df) * 100
        total_pnl = trades_df['pnl'].sum()

        print("\n" + "="*80)
        print("DEFINITIVE STRATEGY ANALYSIS - As Per `BOOM_ANALYSIS_SUMMARY.md`")
        print("="*80)
        
        print(f"\n🎯 OVERALL PERFORMANCE:")
        print(f"Total Trades: {len(trades_df)}")
        print(f"Starting Capital: ${self.starting_capital:.2f}")
        print(f"Final Capital: ${self.current_capital:.2f}")
        print(f"Total P&L: ${total_pnl:.2f}")
        print(f"Return: {total_pnl / self.starting_capital * 100:.2f}%")

        print(f"\n📈 TRADE STATISTICS:")
        print(f"Win Rate: {win_rate:.2f}% ({len(winning_trades)} wins / {len(losing_trades)} losses)")
        if not winning_trades.empty:
            print(f"Average Win: ${winning_trades['pnl'].mean():.2f}")
        if not losing_trades.empty:
            print(f"Average Loss: ${losing_trades['pnl'].mean():.2f}")
        
        print(f"\n⏱️ TRADE DURATION:")
        print(f"Average Duration: {trades_df['duration'].mean():.1f} ticks")
        
        if not losing_trades.empty:
            print("\n🔴 ANALYSIS OF LOSING TRADES:")
            sorted_losses = losing_trades.sort_values('pnl')
            for _, trade in sorted_losses.head(5).iterrows():
                print(f"  - Loss: ${trade['pnl']:.2f}, Duration: {trade['duration']} ticks, Worst Unrealized Loss: ${trade['worst_unrealized_pnl']:.2f}")
        else:
            print("\n✅ NO LOSING TRADES FOUND!")

        print("\n" + "="*80)

def main():
    csv_file = "C:\\Users\\<USER>\\META\\Boom_1000_Index_7days_20250619_20250626.csv"
    analyzer = DefinitiveBoomBacktester(csv_file, starting_capital=20)
    
    analyzer.load_and_parse_data()
    analyzer.detect_spikes()
    analyzer.calculate_tdi()
    analyzer.execute_definitive_strategy()
    analyzer.analyze_results()

if __name__ == "__main__":
    main()
