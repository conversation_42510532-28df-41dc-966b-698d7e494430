import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, classification_report
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

class UniformTickTrader:
    def __init__(self, data_file, uniform_threshold=0.0005, min_sequence_length=3):
        """
        Initialize the Uniform Tick Trading System
        
        Args:
            data_file: Path to tick data CSV
            uniform_threshold: Threshold for uniform tick detection (0.05%)
            min_sequence_length: Minimum sequence length for uniform detection
        """
        self.data_file = data_file
        self.uniform_threshold = uniform_threshold
        self.min_sequence_length = min_sequence_length
        self.model = None
        self.data = None
        self.features = None
        self.targets = None
        self.trades = []
        
    def load_and_prepare_data(self):
        """Load tick data and prepare features"""
        print("Loading tick data...")
        
        # Try different delimiters
        for delimiter in [',', '\t', ';']:
            try:
                self.data = pd.read_csv(self.data_file, delimiter=delimiter)
                if len(self.data.columns) > 1:
                    break
            except:
                continue
        
        print(f"Loaded {len(self.data)} tick records")
        print(f"Columns: {list(self.data.columns)}")
        
        # Find price column
        price_col = None
        for col in ['Bid', 'bid', 'Price', 'price', 'Close', 'close']:
            if col in self.data.columns:
                price_col = col
                break
        
        if price_col is None:
            # Use first numeric column
            numeric_cols = self.data.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) > 0:
                price_col = numeric_cols[0]
                print(f"Using {price_col} as price column")
        
        self.data['price'] = pd.to_numeric(self.data[price_col], errors='coerce')
        
        # Convert timestamp if exists
        if 'time' in self.data.columns or 'Time' in self.data.columns:
            time_col = 'time' if 'time' in self.data.columns else 'Time'
            self.data['timestamp'] = pd.to_datetime(self.data[time_col], errors='coerce')
            if self.data['timestamp'].notna().sum() > 0:
                self.data = self.data.sort_values('timestamp').reset_index(drop=True)
        
        self.data = self.data.dropna(subset=['price']).reset_index(drop=True)
        print(f"Clean data: {len(self.data)} records")
        
    def create_uniform_targets(self):
        """Create uniform tick targets for prediction"""
        print("Creating uniform tick targets...")
        
        # Calculate price changes
        self.data['price_change'] = self.data['price'].diff()
        self.data['price_change_pct'] = self.data['price_change'] / self.data['price'].shift(1)
        
        # Detect uniform ticks (small price changes)
        self.data['is_uniform'] = (abs(self.data['price_change_pct']) <= self.uniform_threshold).astype(int)
        
        # Create forward-looking targets
        self.data['next_uniform'] = self.data['is_uniform'].shift(-1)
        self.data['uniform_sequence_3'] = (
            (self.data['is_uniform'].shift(-1) == 1) &
            (self.data['is_uniform'].shift(-2) == 1) &
            (self.data['is_uniform'].shift(-3) == 1)
        ).astype(int)
        
        # Create directional uniform targets
        self.data['next_price_direction'] = np.where(
            self.data['price'].shift(-1) > self.data['price'], 1,
            np.where(self.data['price'].shift(-1) < self.data['price'], -1, 0)
        )
        
        print(f"Uniform tick rate: {self.data['is_uniform'].mean():.4f}")
        print(f"3-tick uniform sequence rate: {self.data['uniform_sequence_3'].mean():.4f}")
        
    def create_features(self):
        """Create trading features"""
        print("Creating trading features...")
        
        # Price-based features
        for lag in [1, 2, 3, 5]:
            self.data[f'price_lag_{lag}'] = self.data['price'].shift(lag)
            self.data[f'price_change_lag_{lag}'] = self.data['price_change'].shift(lag)
        
        # Rolling statistics
        for window in [5, 10, 20]:
            self.data[f'price_mean_{window}'] = self.data['price'].rolling(window).mean()
            self.data[f'price_std_{window}'] = self.data['price'].rolling(window).std()
            self.data[f'uniform_rate_{window}'] = self.data['is_uniform'].rolling(window).mean()
        
        # Volatility features
        self.data['volatility_5'] = self.data['price_change'].rolling(5).std()
        self.data['volatility_10'] = self.data['price_change'].rolling(10).std()
        
        # Momentum features
        self.data['momentum_5'] = self.data['price'] - self.data['price'].shift(5)
        self.data['momentum_10'] = self.data['price'] - self.data['price'].shift(10)
        
        # Recent uniform behavior
        for lag in [1, 2, 3]:
            self.data[f'uniform_lag_{lag}'] = self.data['is_uniform'].shift(lag)
        
        # Feature columns
        feature_cols = [col for col in self.data.columns if any(x in col for x in 
                       ['lag_', 'mean_', 'std_', 'rate_', 'volatility', 'momentum', 'uniform_lag'])]
        
        self.features = self.data[feature_cols].copy()
        self.targets = self.data[['next_uniform', 'uniform_sequence_3', 'next_price_direction']].copy()
        
        # Remove NaN rows
        valid_mask = self.features.notna().all(axis=1) & self.targets.notna().all(axis=1)
        self.features = self.features[valid_mask]
        self.targets = self.targets[valid_mask]
        self.data = self.data[valid_mask].reset_index(drop=True)
        
        print(f"Features: {self.features.shape}")
        print(f"Feature columns: {list(self.features.columns)}")
        
    def train_models(self):
        """Train prediction models"""
        print("Training prediction models...")
        
        # Split data (70% train, 30% test)
        split_idx = int(len(self.features) * 0.7)
        
        X_train = self.features.iloc[:split_idx]
        X_test = self.features.iloc[split_idx:]
        
        # Train models for different targets
        self.models = {}
        
        for target_col in ['next_uniform', 'uniform_sequence_3']:
            y_train = self.targets[target_col].iloc[:split_idx]
            y_test = self.targets[target_col].iloc[split_idx:]
            
            # Train model
            model = RandomForestClassifier(n_estimators=100, random_state=42, max_depth=10)
            model.fit(X_train, y_train)
            
            # Evaluate
            y_pred = model.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)
            
            self.models[target_col] = model
            print(f"{target_col} model accuracy: {accuracy:.4f}")
        
        # Store test data for trading simulation
        self.test_start_idx = split_idx
        
    def generate_trading_signals(self):
        """Generate trading signals using trained models"""
        print("Generating trading signals...")
        
        test_data = self.data.iloc[self.test_start_idx:].copy()
        test_features = self.features.iloc[self.test_start_idx:]
        
        # Generate predictions
        uniform_pred = self.models['next_uniform'].predict_proba(test_features)[:, 1]
        sequence_pred = self.models['uniform_sequence_3'].predict_proba(test_features)[:, 1]
        
        test_data['uniform_prob'] = uniform_pred
        test_data['sequence_prob'] = sequence_pred
        
        # Create trading signals
        # Signal 1: High probability of uniform tick + direction
        test_data['signal_uniform'] = np.where(
            (test_data['uniform_prob'] > 0.7) & (test_data['next_price_direction'] != 0),
            test_data['next_price_direction'], 0
        )
        
        # Signal 2: Sequence prediction with momentum
        test_data['signal_sequence'] = np.where(
            (test_data['sequence_prob'] > 0.6) & (abs(test_data['momentum_5']) > 0),
            np.sign(test_data['momentum_5']), 0
        )
        
        # Combined signal
        test_data['signal_combined'] = np.where(
            (test_data['uniform_prob'] > 0.6) & (test_data['sequence_prob'] > 0.5),
            test_data['next_price_direction'], 0
        )
        
        return test_data
    
    def simulate_trading(self, test_data, signal_col='signal_combined', min_pip_move=0.0001):
        """Simulate trading based on signals"""
        print(f"Simulating trading with {signal_col}...")
        
        trades = []
        position = 0
        entry_price = 0
        entry_idx = 0
        
        for i in range(len(test_data)):
            current_price = test_data.iloc[i]['price']
            signal = test_data.iloc[i][signal_col]
            
            # Close existing position if profit/loss conditions met
            if position != 0:
                price_change = current_price - entry_price
                pip_change = price_change / min_pip_move if position == 1 else -price_change / min_pip_move
                
                # Close after 5 ticks or if significant move
                if (i - entry_idx >= 5) or (abs(pip_change) >= 10):
                    profit = price_change if position == 1 else -price_change
                    
                    trades.append({
                        'entry_idx': entry_idx,
                        'exit_idx': i,
                        'entry_price': entry_price,
                        'exit_price': current_price,
                        'position': position,
                        'profit': profit,
                        'profit_pips': pip_change if position == 1 else -pip_change,
                        'duration': i - entry_idx
                    })
                    
                    position = 0
            
            # Open new position on signal
            if position == 0 and signal != 0:
                position = signal
                entry_price = current_price
                entry_idx = i
        
        # Close final position if open
        if position != 0:
            profit = (test_data.iloc[-1]['price'] - entry_price) if position == 1 else -(test_data.iloc[-1]['price'] - entry_price)
            trades.append({
                'entry_idx': entry_idx,
                'exit_idx': len(test_data)-1,
                'entry_price': entry_price,
                'exit_price': test_data.iloc[-1]['price'],
                'position': position,
                'profit': profit,
                'profit_pips': profit / min_pip_move,
                'duration': len(test_data) - 1 - entry_idx
            })
        
        return pd.DataFrame(trades)
    
    def calculate_performance_metrics(self, trades_df):
        """Calculate trading performance metrics"""
        if len(trades_df) == 0:
            return {"error": "No trades generated"}
        
        total_trades = len(trades_df)
        winning_trades = len(trades_df[trades_df['profit'] > 0])
        losing_trades = len(trades_df[trades_df['profit'] < 0])
        breakeven_trades = len(trades_df[trades_df['profit'] == 0])
        
        win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
        
        total_profit = trades_df['profit'].sum()
        avg_profit = trades_df['profit'].mean()
        
        winning_profits = trades_df[trades_df['profit'] > 0]['profit']
        losing_profits = trades_df[trades_df['profit'] < 0]['profit']
        
        avg_win = winning_profits.mean() if len(winning_profits) > 0 else 0
        avg_loss = losing_profits.mean() if len(losing_profits) > 0 else 0
        
        profit_factor = abs(winning_profits.sum() / losing_profits.sum()) if len(losing_profits) > 0 and losing_profits.sum() != 0 else float('inf')
        
        # Calculate cumulative profit
        trades_df['cumulative_profit'] = trades_df['profit'].cumsum()
        
        metrics = {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'breakeven_trades': breakeven_trades,
            'win_rate': win_rate,
            'total_profit': total_profit,
            'avg_profit_per_trade': avg_profit,
            'avg_winning_trade': avg_win,
            'avg_losing_trade': avg_loss,
            'profit_factor': profit_factor,
            'max_profit': trades_df['profit'].max(),
            'max_loss': trades_df['profit'].min(),
            'avg_trade_duration': trades_df['duration'].mean()
        }
        
        return metrics
    
    def plot_results(self, test_data, trades_df):
        """Plot trading results"""
        fig, axes = plt.subplots(3, 1, figsize=(15, 12))
        
        # Plot 1: Price and signals
        axes[0].plot(test_data['price'].values, label='Price', alpha=0.7)
        
        # Mark trade entries
        if len(trades_df) > 0:
            for _, trade in trades_df.iterrows():
                color = 'green' if trade['position'] == 1 else 'red'
                axes[0].scatter(trade['entry_idx'], trade['entry_price'], 
                              color=color, marker='^', s=50, alpha=0.7)
                axes[0].scatter(trade['exit_idx'], trade['exit_price'], 
                              color=color, marker='v', s=50, alpha=0.7)
        
        axes[0].set_title('Price Chart with Trade Signals')
        axes[0].set_ylabel('Price')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # Plot 2: Prediction probabilities
        axes[1].plot(test_data['uniform_prob'].values, label='Uniform Probability', alpha=0.7)
        axes[1].plot(test_data['sequence_prob'].values, label='Sequence Probability', alpha=0.7)
        axes[1].axhline(y=0.5, color='gray', linestyle='--', alpha=0.5)
        axes[1].set_title('Model Prediction Probabilities')
        axes[1].set_ylabel('Probability')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)
        
        # Plot 3: Cumulative profit
        if len(trades_df) > 0:
            axes[2].plot(trades_df['cumulative_profit'].values, label='Cumulative Profit')
            axes[2].axhline(y=0, color='gray', linestyle='-', alpha=0.5)
            axes[2].set_title('Cumulative Trading Profit')
            axes[2].set_ylabel('Profit')
            axes[2].set_xlabel('Trade Number')
            axes[2].legend()
            axes[2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('uniform_tick_trading_results.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def run_complete_analysis(self):
        """Run the complete trading analysis"""
        print("=== Uniform Tick Trading System ===")
        
        # Load and prepare data
        self.load_and_prepare_data()
        self.create_uniform_targets()
        self.create_features()
        
        # Train models
        self.train_models()
        
        # Generate signals and simulate trading
        test_data = self.generate_trading_signals()
        
        print("\n=== TRADING SIMULATION RESULTS ===")
        
        # Test different signal strategies
        strategies = ['signal_uniform', 'signal_sequence', 'signal_combined']
        
        for strategy in strategies:
            print(f"\n--- {strategy.upper()} STRATEGY ---")
            trades_df = self.simulate_trading(test_data, strategy)
            
            if len(trades_df) > 0:
                metrics = self.calculate_performance_metrics(trades_df)
                
                print(f"Total Trades: {metrics['total_trades']}")
                print(f"Win Rate: {metrics['win_rate']:.2f}%")
                print(f"Profit Factor: {metrics['profit_factor']:.2f}")
                print(f"Total Profit: {metrics['total_profit']:.6f}")
                print(f"Average Profit per Trade: {metrics['avg_profit_per_trade']:.6f}")
                print(f"Average Win: {metrics['avg_winning_trade']:.6f}")
                print(f"Average Loss: {metrics['avg_losing_trade']:.6f}")
                print(f"Max Profit: {metrics['max_profit']:.6f}")
                print(f"Max Loss: {metrics['max_loss']:.6f}")
                print(f"Average Trade Duration: {metrics['avg_trade_duration']:.1f} ticks")
                
                # Save best strategy for plotting
                if strategy == 'signal_combined':
                    self.best_trades = trades_df
                    self.best_test_data = test_data
            else:
                print("No trades generated for this strategy")
        
        # Plot results for best strategy
        if hasattr(self, 'best_trades') and len(self.best_trades) > 0:
            self.plot_results(self.best_test_data, self.best_trades)
        
        print("\n=== Analysis Complete ===")

if __name__ == "__main__":
    # Initialize and run the trading system
    trader = UniformTickTrader(
        data_file="Boom_1000_Index_7days_20250620_20250627.csv",
        uniform_threshold=0.0005,  # 0.05% threshold
        min_sequence_length=3
    )
    
    trader.run_complete_analysis()
