# Elite Algorithm Inverse Signal Analysis
# This script analyzes what happens if we trade opposite to all Elite Algorithm signals

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class EliteInverseAnalyzer:
    def __init__(self, results_file):
        """
        Initialize the inverse analyzer
        
        Args:
            results_file: Path to the detailed backtest results CSV
        """
        self.results_file = results_file
        self.original_results = None
        self.inverse_results = None
        self.analysis = {}
        
    def load_data(self):
        """Load the original backtest results"""
        print("Loading original backtest data...")
        
        self.original_results = pd.read_csv(self.results_file, index_col=0, parse_dates=True)
        print(f"Loaded {len(self.original_results)} bars of data")
        
        return self.original_results
    
    def create_inverse_signals(self):
        """Create inverse signals - opposite of what Elite Algorithm suggested"""
        print("\nCreating inverse signals...")
        
        df = self.original_results.copy()
        
        # Create inverse signals
        inverse_signal_map = {
            'Buy': 'Sell',
            'Strong Buy': 'Strong Sell', 
            'Sell': 'Buy',
            'Strong Sell': 'Strong Buy'
        }
        
        # Apply inverse mapping
        df['inverse_signal'] = df['final_signal'].map(inverse_signal_map)
        
        # Count inverse signals
        inverse_signal_counts = df['inverse_signal'].value_counts()
        print("Inverse signal counts:")
        print(inverse_signal_counts)
        
        return df
    
    def run_inverse_backtest(self, initial_capital=10000, commission=0.0, slippage=0.0):
        """Run backtest with inverse signals (no costs since data has no spread)"""
        print(f"\nRunning inverse backtest...")
        print(f"Using zero commission and slippage (pure price data)")
        
        df = self.create_inverse_signals()
        
        # Initialize backtest columns for inverse strategy
        df['inverse_position'] = 0
        df['inverse_entry_price'] = np.nan
        df['inverse_exit_price'] = np.nan
        df['inverse_trade_return'] = 0.0
        df['inverse_cumulative_return'] = 0.0
        df['inverse_portfolio_value'] = initial_capital
        df['inverse_drawdown'] = 0.0
        
        # Track current position
        current_position = 0
        entry_price = 0
        inverse_trades = []
        
        for i in range(len(df)):
            signal = df.iloc[i]['inverse_signal']
            current_price = df.iloc[i]['close']
            
            # No slippage since it's pure price data
            execution_price = current_price
            
            # Process inverse signals
            if signal in ['Buy', 'Strong Buy'] and current_position <= 0:
                # Close short position if any
                if current_position < 0:
                    trade_return = (entry_price - execution_price) / entry_price - commission
                    df.iloc[i, df.columns.get_loc('inverse_exit_price')] = execution_price
                    df.iloc[i, df.columns.get_loc('inverse_trade_return')] = trade_return
                    
                    inverse_trades.append({
                        'type': 'Short',
                        'entry': entry_price,
                        'exit': execution_price,
                        'return': trade_return,
                        'signal_strength': df.iloc[i]['short_signal_strength'],
                        'duration_bars': i - len([t for t in inverse_trades if t['type'] == 'Short' and 'entry' in t])
                    })
                
                # Open long position
                current_position = 1
                entry_price = execution_price
                df.iloc[i, df.columns.get_loc('inverse_entry_price')] = execution_price
                
            elif signal in ['Sell', 'Strong Sell'] and current_position >= 0:
                # Close long position if any
                if current_position > 0:
                    trade_return = (execution_price - entry_price) / entry_price - commission
                    df.iloc[i, df.columns.get_loc('inverse_exit_price')] = execution_price
                    df.iloc[i, df.columns.get_loc('inverse_trade_return')] = trade_return
                    
                    inverse_trades.append({
                        'type': 'Long',
                        'entry': entry_price,
                        'exit': execution_price,
                        'return': trade_return,
                        'signal_strength': df.iloc[i]['long_signal_strength'],
                        'duration_bars': i - len([t for t in inverse_trades if t['type'] == 'Long' and 'entry' in t])
                    })
                
                # Open short position
                current_position = -1
                entry_price = execution_price
                df.iloc[i, df.columns.get_loc('inverse_entry_price')] = execution_price
            
            # Update position
            df.iloc[i, df.columns.get_loc('inverse_position')] = current_position
        
        # Calculate cumulative returns and portfolio value
        df['inverse_cumulative_return'] = df['inverse_trade_return'].cumsum()
        df['inverse_portfolio_value'] = initial_capital * (1 + df['inverse_cumulative_return'])
        
        # Calculate drawdown
        running_max = df['inverse_portfolio_value'].expanding().max()
        df['inverse_drawdown'] = (df['inverse_portfolio_value'] - running_max) / running_max
        
        self.inverse_results = df
        self.inverse_trades = pd.DataFrame(inverse_trades)
        
        return df, inverse_trades
    
    def analyze_inverse_performance(self):
        """Analyze the performance of inverse signals"""
        print("\nAnalyzing inverse strategy performance...")
        
        if self.inverse_results is None:
            raise ValueError("Inverse backtest not run yet.")
        
        df = self.inverse_results
        trades = self.inverse_trades
        
        # Basic metrics
        total_return = df['inverse_cumulative_return'].iloc[-1]
        final_value = df['inverse_portfolio_value'].iloc[-1]
        max_drawdown = df['inverse_drawdown'].min()
        num_trades = len(trades)
        
        if num_trades > 0:
            winning_trades = len(trades[trades['return'] > 0])
            win_rate = winning_trades / num_trades
            avg_return = trades['return'].mean()
            avg_win = trades[trades['return'] > 0]['return'].mean() if winning_trades > 0 else 0
            avg_loss = trades[trades['return'] < 0]['return'].mean() if num_trades - winning_trades > 0 else 0
            profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')
            sharpe = avg_return / trades['return'].std() if trades['return'].std() != 0 else 0
            
            # Analyze trade durations
            avg_duration = trades['duration_bars'].mean() if 'duration_bars' in trades.columns else 0
        else:
            win_rate = avg_return = avg_win = avg_loss = profit_factor = sharpe = avg_duration = 0
        
        # Compare with original performance
        orig_total_return = df['cumulative_return'].iloc[-1]
        orig_final_value = df['portfolio_value'].iloc[-1]
        
        performance_metrics = {
            'inverse_total_return': total_return,
            'inverse_final_value': final_value,
            'inverse_max_drawdown': max_drawdown,
            'inverse_total_trades': num_trades,
            'inverse_win_rate': win_rate,
            'inverse_avg_return': avg_return,
            'inverse_avg_win': avg_win,
            'inverse_avg_loss': avg_loss,
            'inverse_profit_factor': profit_factor,
            'inverse_sharpe': sharpe,
            'inverse_avg_duration': avg_duration,
            'original_total_return': orig_total_return,
            'original_final_value': orig_final_value,
            'performance_improvement': total_return - orig_total_return
        }
        
        return performance_metrics
    
    def analyze_losing_trades_bars(self):
        """Analyze how many bars it takes for losing trades to become profitable if inverted"""
        print("\nAnalyzing bars to profitability for inverse trades...")
        
        if self.inverse_trades is None or len(self.inverse_trades) == 0:
            print("No inverse trades to analyze")
            return None
        
        # Get losing trades from inverse strategy
        losing_inverse_trades = self.inverse_trades[self.inverse_trades['return'] < 0].copy()
        
        # For original losing trades that become winners when inverted
        original_losing_now_winning = self.inverse_trades[self.inverse_trades['return'] > 0].copy()
        
        bars_analysis = {
            'inverse_losing_trades': len(losing_inverse_trades),
            'original_losing_now_winning': len(original_losing_now_winning),
            'total_inverse_trades': len(self.inverse_trades)
        }
        
        if len(original_losing_now_winning) > 0:
            avg_duration_winners = original_losing_now_winning['duration_bars'].mean()
            bars_analysis['avg_duration_new_winners'] = avg_duration_winners
        
        if len(losing_inverse_trades) > 0:
            avg_duration_losers = losing_inverse_trades['duration_bars'].mean()
            bars_analysis['avg_duration_still_losers'] = avg_duration_losers
        
        return bars_analysis
    
    def analyze_position_profitability_inverse(self):
        """Analyze profitable bars during inverse positions"""
        print("\nAnalyzing inverse position profitability by bars...")
        
        df = self.inverse_results
        
        # Track position changes for inverse strategy
        position_changes = df['inverse_position'] != df['inverse_position'].shift(1)
        position_starts = df[position_changes].index
        
        inverse_position_analysis = []
        
        for i in range(len(position_starts) - 1):
            start_idx = position_starts[i]
            end_idx = position_starts[i + 1]
            
            # Get position data
            position_data = df.loc[start_idx:end_idx].copy()
            
            if len(position_data) <= 1:
                continue
                
            position_type = position_data['inverse_position'].iloc[0]
            entry_price = position_data['close'].iloc[0]
            
            # Calculate unrealized P&L for each bar in the inverse position
            if position_type == 1:  # Long position
                position_data['unrealized_pnl'] = (position_data['close'] - entry_price) / entry_price
                profitable_bars = (position_data['close'] > entry_price).sum()
            elif position_type == -1:  # Short position
                position_data['unrealized_pnl'] = (entry_price - position_data['close']) / entry_price
                profitable_bars = (position_data['close'] < entry_price).sum()
            else:
                continue
            
            total_bars = len(position_data)
            profit_ratio = profitable_bars / total_bars if total_bars > 0 else 0
            
            inverse_position_analysis.append({
                'start_time': start_idx,
                'end_time': end_idx,
                'position_type': 'Long' if position_type == 1 else 'Short',
                'duration_bars': total_bars,
                'profitable_bars': profitable_bars,
                'profit_ratio': profit_ratio,
                'entry_price': entry_price,
                'exit_price': position_data['close'].iloc[-1],
                'final_pnl': position_data['unrealized_pnl'].iloc[-1]
            })
        
        self.inverse_position_analysis = pd.DataFrame(inverse_position_analysis)
        
        # Summary statistics
        if len(self.inverse_position_analysis) > 0:
            avg_profit_ratio = self.inverse_position_analysis['profit_ratio'].mean()
            avg_duration = self.inverse_position_analysis['duration_bars'].mean()
            positions_with_profits = (self.inverse_position_analysis['profit_ratio'] > 0.5).sum()
            
            print(f"INVERSE - Average profitable bars ratio: {avg_profit_ratio:.2f} ({avg_profit_ratio*100:.1f}%)")
            print(f"INVERSE - Average position duration: {avg_duration:.1f} bars")
            print(f"INVERSE - Positions with >50% profitable bars: {positions_with_profits}/{len(self.inverse_position_analysis)} ({positions_with_profits/len(self.inverse_position_analysis)*100:.1f}%)")
        
        return self.inverse_position_analysis
    
    def generate_comparison_report(self):
        """Generate comprehensive comparison report"""
        metrics = self.analyze_inverse_performance()
        bars_analysis = self.analyze_losing_trades_bars()
        
        print("\n" + "="*70)
        print("ELITE ALGORITHM INVERSE SIGNAL ANALYSIS")
        print("="*70)
        
        print(f"\nORIGINAL STRATEGY PERFORMANCE:")
        print(f"Total Return: {metrics['original_total_return']:.4f} ({metrics['original_total_return']*100:.2f}%)")
        print(f"Final Portfolio Value: ${metrics['original_final_value']:,.2f}")
        
        print(f"\nINVERSE STRATEGY PERFORMANCE:")
        print(f"Total Return: {metrics['inverse_total_return']:.4f} ({metrics['inverse_total_return']*100:.2f}%)")
        print(f"Final Portfolio Value: ${metrics['inverse_final_value']:,.2f}")
        print(f"Max Drawdown: {metrics['inverse_max_drawdown']:.4f} ({metrics['inverse_max_drawdown']*100:.2f}%)")
        print(f"Total Trades: {metrics['inverse_total_trades']}")
        print(f"Win Rate: {metrics['inverse_win_rate']:.2f} ({metrics['inverse_win_rate']*100:.1f}%)")
        print(f"Average Return per Trade: {metrics['inverse_avg_return']:.4f} ({metrics['inverse_avg_return']*100:.2f}%)")
        print(f"Profit Factor: {metrics['inverse_profit_factor']:.2f}")
        print(f"Sharpe Ratio: {metrics['inverse_sharpe']:.2f}")
        
        print(f"\nPERFORMANCE COMPARISON:")
        improvement = metrics['performance_improvement']
        improvement_pct = improvement * 100
        print(f"Performance Improvement: {improvement:.4f} ({improvement_pct:.2f} percentage points)")
        
        if improvement > 0:
            print("🎉 INVERSE STRATEGY OUTPERFORMS ORIGINAL!")
        else:
            print("❌ Inverse strategy still underperforms")
        
        if bars_analysis:
            print(f"\nTRADE DURATION ANALYSIS:")
            print(f"Total Inverse Trades: {bars_analysis['total_inverse_trades']}")
            print(f"Originally Losing → Now Winning: {bars_analysis['original_losing_now_winning']}")
            print(f"Still Losing (Inverse): {bars_analysis['inverse_losing_trades']}")
            
            if 'avg_duration_new_winners' in bars_analysis:
                print(f"Average Duration of New Winners: {bars_analysis['avg_duration_new_winners']:.1f} bars")
            if 'avg_duration_still_losers' in bars_analysis:
                print(f"Average Duration of Still Losers: {bars_analysis['avg_duration_still_losers']:.1f} bars")
        
        print("\n" + "="*70)
        
        return metrics
    
    def save_inverse_results(self):
        """Save inverse analysis results"""
        print("\nSaving inverse analysis results...")
        
        if self.inverse_results is not None:
            self.inverse_results.to_csv('elite_inverse_backtest_results.csv')
            print("Inverse results saved to: elite_inverse_backtest_results.csv")
        
        if hasattr(self, 'inverse_trades') and len(self.inverse_trades) > 0:
            self.inverse_trades.to_csv('elite_inverse_trades.csv', index=False)
            print("Inverse trades saved to: elite_inverse_trades.csv")
        
        if hasattr(self, 'inverse_position_analysis') and len(self.inverse_position_analysis) > 0:
            self.inverse_position_analysis.to_csv('elite_inverse_position_analysis.csv', index=False)
            print("Inverse position analysis saved to: elite_inverse_position_analysis.csv")
    
    def run_full_inverse_analysis(self):
        """Run the complete inverse analysis"""
        self.load_data()
        self.run_inverse_backtest()
        self.analyze_position_profitability_inverse()
        metrics = self.generate_comparison_report()
        self.save_inverse_results()
        
        return metrics


def main():
    """Main function to run the inverse analysis"""
    # File path
    results_file = 'renko_elite_backtest_results.csv'
    
    # Initialize and run inverse analysis
    analyzer = EliteInverseAnalyzer(results_file)
    metrics = analyzer.run_full_inverse_analysis()
    
    print("\nInverse analysis completed successfully!")
    return analyzer

if __name__ == "__main__":
    analyzer = main()
