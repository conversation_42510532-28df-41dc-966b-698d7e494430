import pandas as pd
import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt
import time
import logging
from datetime import datetime
import argparse

def setup_logging():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"boom_spike_training_{timestamp}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

class BoomDataAnalyzer:
    """Comprehensive Boom data analyzer for spike detection and uniform tick counting"""
    
    def __init__(self, csv_file, logger=None):
        self.logger = logger or logging.getLogger(__name__)
        self.logger.info("Loading Boom data...")
        
        self.data = pd.read_csv(csv_file)
        self.logger.info(f"Loaded {len(self.data)} tick records")
        
        self.data['datetime'] = pd.to_datetime(self.data['time'])
        self.data = self.data.sort_values('datetime').reset_index(drop=True)
        self.data['price'] = self.data['bid']
        
    def detect_spikes_and_uniform_periods(self, spike_threshold_pct=0.3, uniform_threshold=0.05):
        """Detect spikes and count uniform ticks after spikes"""
        
        self.logger.info("Detecting spikes and uniform periods...")
        
        self.data['price_change'] = self.data['price'].diff()
        self.data['price_change_pct'] = (self.data['price_change'] / self.data['price'].shift(1)) * 100
        
        self.data['is_spike'] = (
            (self.data['price_change_pct'] > spike_threshold_pct) | 
            (self.data['price_change_pct'] < -spike_threshold_pct)
        ).astype(int)
        
        self.data['uniform_ticks_after_spike'] = 0
        spike_indices = self.data[self.data['is_spike'] == 1].index
        
        for spike_idx in spike_indices:
            uniform_count = 0
            idx = spike_idx + 1
            
            while idx < len(self.data):
                if abs(self.data.loc[idx, 'price_change_pct']) <= uniform_threshold:
                    uniform_count += 1
                    idx += 1
                else:
                    break
                    
                if uniform_count >= 100:
                    break
            
            self.data.loc[spike_idx, 'uniform_ticks_after_spike'] = uniform_count
        
        spike_count = self.data['is_spike'].sum()
        avg_uniform_ticks = self.data[self.data['is_spike'] == 1]['uniform_ticks_after_spike'].mean()
        
        self.logger.info(f"Detected {spike_count} spikes")
        self.logger.info(f"Average uniform ticks after spike: {avg_uniform_ticks:.2f}")
        
        return spike_count, avg_uniform_ticks
    
    def create_features(self, lookback_window=20):
        """Create features for spike prediction"""
        
        self.logger.info(f"Creating features with lookback window of {lookback_window}")
        
        features = []
        
        # Price-based features
        for i in range(1, lookback_window + 1):
            self.data[f'price_lag_{i}'] = self.data['price'].shift(i)
            self.data[f'change_lag_{i}'] = self.data['price_change'].shift(i)
            self.data[f'change_pct_lag_{i}'] = self.data['price_change_pct'].shift(i)
            features.extend([f'price_lag_{i}', f'change_lag_{i}', f'change_pct_lag_{i}'])
        
        # Moving averages
        for window in [5, 10, 20]:
            self.data[f'ma_{window}'] = self.data['price'].rolling(window=window).mean()
            self.data[f'price_vs_ma_{window}'] = self.data['price'] - self.data[f'ma_{window}']
            features.extend([f'ma_{window}', f'price_vs_ma_{window}'])
        
        # Volatility features
        for window in [5, 10, 20]:
            self.data[f'volatility_{window}'] = self.data['price_change_pct'].rolling(window=window).std()
            features.append(f'volatility_{window}')
        
        # Momentum features
        for window in [3, 5, 10]:
            self.data[f'momentum_{window}'] = self.data['price'] / self.data['price'].shift(window) - 1
            features.append(f'momentum_{window}')
        
        # Time-based features
        self.data['hour'] = self.data['datetime'].dt.hour
        self.data['minute'] = self.data['datetime'].dt.minute
        self.data['day_of_week'] = self.data['datetime'].dt.dayofweek
        features.extend(['hour', 'minute', 'day_of_week'])
        
        # Recent spike activity
        for window in [10, 50, 100]:
            self.data[f'recent_spikes_{window}'] = self.data['is_spike'].rolling(window=window).sum()
            features.append(f'recent_spikes_{window}')
        
        self.feature_columns = features
        
        initial_len = len(self.data)
        self.data.dropna(inplace=True)
        final_len = len(self.data)
        
        self.logger.info(f"Features created. Data reduced from {initial_len} to {final_len} rows")
        
        return features

class BoomDataset(Dataset):
    """Dataset for Boom spike prediction"""
    
    def __init__(self, X, y_spike, y_uniform_count):
        self.X = torch.FloatTensor(X)
        self.y_spike = torch.LongTensor(y_spike)
        self.y_uniform_count = torch.FloatTensor(y_uniform_count)
    
    def __len__(self):
        return len(self.X)
    
    def __getitem__(self, idx):
        return self.X[idx], self.y_spike[idx], self.y_uniform_count[idx]

class BoomSpikeModel(nn.Module):
    """Dual-task model: spike prediction + uniform tick counting"""
    
    def __init__(self, input_size, hidden_size=128, dropout=0.2):
        super(BoomSpikeModel, self).__init__()
        
        self.shared_layers = nn.Sequential(
            nn.Linear(input_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        self.spike_head = nn.Sequential(
            nn.Linear(hidden_size // 2, 32),
            nn.ReLU(),
            nn.Linear(32, 2)  # Binary classification
        )
        
        self.count_head = nn.Sequential(
            nn.Linear(hidden_size // 2, 32),
            nn.ReLU(),
            nn.Linear(32, 1)  # Regression output
        )
    
    def forward(self, x):
        shared_features = self.shared_layers(x)
        spike_pred = self.spike_head(shared_features)
        count_pred = self.count_head(shared_features)
        return spike_pred, count_pred.squeeze()

def train_boom_model(csv_file, epochs=50, batch_size=512, learning_rate=0.001):
    """Train the comprehensive Boom spike prediction model"""
    
    logger = setup_logging()
    logger.info("Starting Boom spike prediction model training")
    
    start_time = time.time()
    
    # Initialize analyzer
    analyzer = BoomDataAnalyzer(csv_file, logger)
    
    # Detect spikes and uniform periods
    spike_count, avg_uniform_ticks = analyzer.detect_spikes_and_uniform_periods()
    
    # Create features
    features = analyzer.create_features()
    
    # Prepare data
    X = analyzer.data[features].values
    y_spike = analyzer.data['is_spike'].values
    y_uniform_count = analyzer.data['uniform_ticks_after_spike'].values
    
    logger.info(f"Dataset shape: {X.shape}")
    logger.info(f"Spike rate: {y_spike.mean():.4f}")
    logger.info(f"Features: {len(features)}")
    
    # Scale features
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # Train-test split
    X_train, X_test, y_spike_train, y_spike_test, y_count_train, y_count_test = train_test_split(
        X_scaled, y_spike, y_uniform_count, test_size=0.2, random_state=42, stratify=y_spike
    )
    
    logger.info(f"Train samples: {len(X_train)}, Test samples: {len(X_test)}")
    
    # Create datasets and loaders
    train_dataset = BoomDataset(X_train, y_spike_train, y_count_train)
    test_dataset = BoomDataset(X_test, y_spike_test, y_count_test)
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    # Initialize model
    model = BoomSpikeModel(input_size=X_train.shape[1])
    
    # Loss functions
    spike_criterion = nn.CrossEntropyLoss()
    count_criterion = nn.MSELoss()
    
    # Optimizer
    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.5)
    
    # Training history
    history = {
        'train_spike_loss': [],
        'train_count_loss': [],
        'val_spike_loss': [],
        'val_count_loss': [],
        'val_spike_acc': []
    }
    
    logger.info(f"Starting training for {epochs} epochs")
    
    best_val_acc = 0
    patience_counter = 0
    
    for epoch in range(epochs):
        epoch_start = time.time()
        
        # Training phase
        model.train()
        train_spike_loss = 0
        train_count_loss = 0
        
        for X_batch, y_spike_batch, y_count_batch in train_loader:
            optimizer.zero_grad()
            
            spike_pred, count_pred = model(X_batch)
            
            spike_loss = spike_criterion(spike_pred, y_spike_batch)
            count_loss = count_criterion(count_pred, y_count_batch)
            
            # Combined loss (weighted)
            total_loss = spike_loss + 0.1 * count_loss
            
            total_loss.backward()
            optimizer.step()
            
            train_spike_loss += spike_loss.item()
            train_count_loss += count_loss.item()
        
        train_spike_loss /= len(train_loader)
        train_count_loss /= len(train_loader)
        
        # Validation phase
        model.eval()
        val_spike_loss = 0
        val_count_loss = 0
        correct = 0
        total = 0
        
        with torch.no_grad():
            for X_batch, y_spike_batch, y_count_batch in test_loader:
                spike_pred, count_pred = model(X_batch)
                
                spike_loss = spike_criterion(spike_pred, y_spike_batch)
                count_loss = count_criterion(count_pred, y_count_batch)
                
                val_spike_loss += spike_loss.item()
                val_count_loss += count_loss.item()
                
                _, predicted = torch.max(spike_pred.data, 1)
                total += y_spike_batch.size(0)
                correct += (predicted == y_spike_batch).sum().item()
        
        val_spike_loss /= len(test_loader)
        val_count_loss /= len(test_loader)
        val_spike_acc = correct / total
        
        scheduler.step(val_spike_loss)
        
        # Save history
        history['train_spike_loss'].append(train_spike_loss)
        history['train_count_loss'].append(train_count_loss)
        history['val_spike_loss'].append(val_spike_loss)
        history['val_count_loss'].append(val_count_loss)
        history['val_spike_acc'].append(val_spike_acc)
        
        epoch_time = time.time() - epoch_start
        
        # Logging
        logger.info(f"Epoch {epoch+1}/{epochs}:")
        logger.info(f"  Train - Spike Loss: {train_spike_loss:.4f}, Count Loss: {train_count_loss:.4f}")
        logger.info(f"  Val - Spike Loss: {val_spike_loss:.4f}, Count Loss: {val_count_loss:.4f}")
        logger.info(f"  Val Spike Accuracy: {val_spike_acc:.4f}")
        logger.info(f"  Epoch Time: {epoch_time:.2f}s")
        logger.info(f"  Current LR: {optimizer.param_groups[0]['lr']:.6f}")
        
        # Early stopping
        if val_spike_acc > best_val_acc:
            best_val_acc = val_spike_acc
            patience_counter = 0
            
            # Save best model
            torch.save({
                'model_state_dict': model.state_dict(),
                'scaler': scaler,
                'features': features,
                'spike_count': spike_count,
                'avg_uniform_ticks': avg_uniform_ticks,
                'best_val_acc': best_val_acc
            }, 'boom_spike_model.pth')
            
            logger.info(f"  New best model saved! Accuracy: {best_val_acc:.4f}")
        else:
            patience_counter += 1
            
        if patience_counter >= 10:
            logger.info("Early stopping triggered")
            break
    
    # Final evaluation
    logger.info("\nFinal evaluation on test set...")
    
    model.eval()
    y_true_spike = []
    y_pred_spike = []
    y_true_count = []
    y_pred_count = []
    
    with torch.no_grad():
        for X_batch, y_spike_batch, y_count_batch in test_loader:
            spike_pred, count_pred = model(X_batch)
            
            _, predicted_spike = torch.max(spike_pred, 1)
            
            y_true_spike.extend(y_spike_batch.numpy())
            y_pred_spike.extend(predicted_spike.numpy())
            y_true_count.extend(y_count_batch.numpy())
            y_pred_count.extend(count_pred.numpy())
    
    # Classification report for spike prediction
    logger.info("\nSpike Prediction Classification Report:")
    logger.info("\n" + classification_report(y_true_spike, y_pred_spike))
    
    # Count prediction metrics
    count_mae = np.mean(np.abs(np.array(y_true_count) - np.array(y_pred_count)))
    count_rmse = np.sqrt(np.mean((np.array(y_true_count) - np.array(y_pred_count))**2))
    
    logger.info(f"\nUniform Tick Count Prediction:")
    logger.info(f"MAE: {count_mae:.2f}")
    logger.info(f"RMSE: {count_rmse:.2f}")
    
    # Create plots
    create_training_plots(history, logger)
    
    total_time = time.time() - start_time
    logger.info(f"\nTraining completed in {total_time:.2f} seconds ({total_time/60:.1f} minutes)")
    logger.info(f"Best validation accuracy: {best_val_acc:.4f}")
    
    return model, scaler, features, history

def create_training_plots(history, logger):
    """Create training progress plots"""
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # Spike loss
    ax1.plot(history['train_spike_loss'], label='Train Spike Loss', color='blue')
    ax1.plot(history['val_spike_loss'], label='Val Spike Loss', color='red')
    ax1.set_title('Spike Prediction Loss')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Count loss
    ax2.plot(history['train_count_loss'], label='Train Count Loss', color='blue')
    ax2.plot(history['val_count_loss'], label='Val Count Loss', color='red')
    ax2.set_title('Uniform Tick Count Loss')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Loss')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # Validation accuracy
    ax3.plot(history['val_spike_acc'], label='Val Spike Accuracy', color='green')
    ax3.set_title('Validation Spike Accuracy')
    ax3.set_xlabel('Epoch')
    ax3.set_ylabel('Accuracy')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # Combined losses
    ax4.plot(history['train_spike_loss'], label='Train Spike', alpha=0.7)
    ax4.plot(history['val_spike_loss'], label='Val Spike', alpha=0.7)
    ax4.plot(np.array(history['train_count_loss'])*10, label='Train Count (x10)', alpha=0.7)
    ax4.plot(np.array(history['val_count_loss'])*10, label='Val Count (x10)', alpha=0.7)
    ax4.set_title('All Losses Combined')
    ax4.set_xlabel('Epoch')
    ax4.set_ylabel('Loss')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('boom_training_results.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    logger.info("Training plots saved to: boom_training_results.png")

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Boom Spike Prediction Model')
    parser.add_argument('--csv_file', type=str, default='Boom_1000_Index_7days_20250620_20250627.csv')
    parser.add_argument('--epochs', type=int, default=50)
    parser.add_argument('--batch_size', type=int, default=512)
    parser.add_argument('--learning_rate', type=float, default=0.001)
    args = parser.parse_args()
    
    model, scaler, features, history = train_boom_model(
        csv_file=args.csv_file,
        epochs=args.epochs,
        batch_size=args.batch_size,
        learning_rate=args.learning_rate
    )
