import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import os
from collections import defaultdict

# Set up logging
log_dir = "strategy_logs"
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
log_filename = f"{log_dir}/streamlined_backtest_{timestamp}.log"

def log_message(message):
    """Log message to file and print to console"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_msg = f"[{timestamp}] {message}"
    print(log_msg)
    with open(log_filename, 'a') as f:
        f.write(log_msg + '\n')

log_message("=== STREAMLINED RENKO BACKTEST ===")

# Load the existing predictions
predictions_file = "strategy_logs/predictions_20250624_182556.csv"
log_message(f"Loading predictions from {predictions_file}...")

df = pd.read_csv(predictions_file)
df['datetime'] = pd.to_datetime(df['datetime'])
log_message(f"Loaded {len(df)} prediction rows")

# Analyze prediction distribution
log_message("\n=== PREDICTION ANALYSIS ===")
log_message(f"Prediction distribution:")
log_message(df['prediction'].value_counts().to_string())

log_message(f"\nProbability statistics:")
log_message(f"prob_short: min={df['prob_short'].min():.4f}, max={df['prob_short'].max():.4f}, mean={df['prob_short'].mean():.4f}")
log_message(f"prob_neutral: min={df['prob_neutral'].min():.4f}, max={df['prob_neutral'].max():.4f}, mean={df['prob_neutral'].mean():.4f}")
log_message(f"prob_long: min={df['prob_long'].min():.4f}, max={df['prob_long'].max():.4f}, mean={df['prob_long'].mean():.4f}")

# Find signals with different confidence thresholds
for threshold in [0.3, 0.4, 0.5, 0.6, 0.7]:
    long_signals = ((df['prediction'] == 1) & (df['prob_long'] >= threshold)).sum()
    short_signals = ((df['prediction'] == -1) & (df['prob_short'] >= threshold)).sum()
    total_signals = long_signals + short_signals
    log_message(f"Threshold {threshold}: {total_signals} signals ({long_signals} LONG, {short_signals} SHORT)")

# Use a lower confidence threshold to find some trades
CONFIDENCE_THRESHOLD = 0.02  # Lower threshold to find trades
log_message(f"\nUsing confidence threshold: {CONFIDENCE_THRESHOLD}")

# Strategy parameters
BRICK_SIZE = 0.05
SPREAD = 0.0
MIN_VOL = 0.10
MAX_VOL_PER_POS = 50.0
MAX_TOTAL_VOL = 200.0

# Account state
equity = 100.0
open_volume = 0
equity_history = [equity]
win_streak = 0
loss_streak = 0
trade_outcomes = []
win_count = 0
trade_count = 0

# Track loss patterns
loss_patterns = defaultdict(int)

# Trading results
trades = []

# Simplified backtest loop
log_message(f"\nStarting backtest with {len(df)} bars...")

i = 30  # Start after some data
while i < len(df) - 10:
    current_row = df.iloc[i]
    
    # Check for trading signals with adjusted logic
    should_trade = False
    position_type = None
    
    # Look for stronger directional signals
    if current_row['prob_long'] > current_row['prob_short'] and current_row['prob_long'] >= CONFIDENCE_THRESHOLD:
        should_trade = True
        position_type = "LONG"
    elif current_row['prob_short'] > current_row['prob_long'] and current_row['prob_short'] >= CONFIDENCE_THRESHOLD:
        should_trade = True
        position_type = "SHORT"
    
    if should_trade:
        # Simple position sizing
        risk_percentage = 0.02  # Fixed 2% risk
        risk_amount = equity * risk_percentage
        price_risk = 0.2 + SPREAD  # 2 bricks + spread
        lot_size = min(risk_amount / (price_risk * 10), MAX_VOL_PER_POS)
        
        if lot_size >= MIN_VOL and open_volume + lot_size <= MAX_TOTAL_VOL:
            # Execute trade
            entry_time = current_row['datetime']
            entry_price = current_row['close']
            open_volume += lot_size
            trade_count += 1
            
            log_message(f"Trade #{trade_count}: {position_type} at {entry_time}, price: {entry_price:.4f}, volume: {lot_size:.2f}")
            
            # Simple trade management - look ahead for profit/loss
            tp_bricks = 5  # Take profit at 5 bricks
            sl_bricks = 2  # Stop loss at 2 bricks
            
            profit = 0
            outcome = None
            exit_price = entry_price
            exit_time = entry_time
            
            # Look ahead for trade outcome
            for j in range(i + 1, min(i + 20, len(df))):
                current_direction = df.iloc[j]['direction']
                current_price = df.iloc[j]['close']
                
                if position_type == "LONG":
                    if current_direction == 'up':
                        tp_bricks -= 1
                        if tp_bricks == 0:
                            profit = (0.5 - SPREAD) * 10 * lot_size
                            outcome = 'LONG_TP'
                            exit_price = current_price
                            exit_time = df.iloc[j]['datetime']
                            break
                    elif current_direction == 'down':
                        sl_bricks -= 1
                        if sl_bricks == 0:
                            # LONG Stop Loss hit - start reversal recovery system
                            sl_loss = -(0.2 + SPREAD) * 10 * lot_size
                            reversal_exit_price = current_price
                            reversal_exit_time = df.iloc[j]['datetime']
                            
                            log_message(f"LONG SL hit at {reversal_exit_time}, starting SHORT reversal recovery")
                            
                            # Start SHORT reversal - take profit after 2 boxes (0.2)
                            reversal_tp_bricks = 2
                            reversal_profit = 0
                            
                            # Look for reversal profit
                            for k in range(j + 1, min(j + 15, len(df))):
                                rev_direction = df.iloc[k]['direction']
                                rev_price = df.iloc[k]['close']
                                
                                if rev_direction == 'down':
                                    reversal_tp_bricks -= 1
                                    if reversal_tp_bricks == 0:
                                        reversal_profit = (0.2 - SPREAD) * 10 * lot_size
                                        outcome = 'LONG_SL_SHORT_RECOVERY'
                                        exit_price = rev_price
                                        exit_time = df.iloc[k]['datetime']
                                        log_message(f"SHORT reversal recovery successful: +${reversal_profit:.2f}")
                                        break
                                elif rev_direction == 'up':
                                    # Reversal failed, exit with original loss
                                    outcome = 'LONG_SL_RECOVERY_FAILED'
                                    exit_price = reversal_exit_price
                                    exit_time = reversal_exit_time
                                    log_message(f"SHORT reversal recovery failed")
                                    break
                            
                            # Calculate total profit (original loss + reversal)
                            if reversal_profit > 0:
                                profit = sl_loss + reversal_profit
                            else:
                                profit = sl_loss
                            break
                
                elif position_type == "SHORT":
                    if current_direction == 'down':
                        tp_bricks -= 1
                        if tp_bricks == 0:
                            profit = (0.5 - SPREAD) * 10 * lot_size
                            outcome = 'SHORT_TP'
                            exit_price = current_price
                            exit_time = df.iloc[j]['datetime']
                            break
                    elif current_direction == 'up':
                        sl_bricks -= 1
                        if sl_bricks == 0:
                            # SHORT Stop Loss hit - start reversal recovery system
                            sl_loss = -(0.2 + SPREAD) * 10 * lot_size
                            reversal_exit_price = current_price
                            reversal_exit_time = df.iloc[j]['datetime']
                            
                            log_message(f"SHORT SL hit at {reversal_exit_time}, starting LONG reversal recovery")
                            
                            # Start LONG reversal - take profit after 2 boxes (0.2)
                            reversal_tp_bricks = 2
                            reversal_profit = 0
                            
                            # Look for reversal profit
                            for k in range(j + 1, min(j + 15, len(df))):
                                rev_direction = df.iloc[k]['direction']
                                rev_price = df.iloc[k]['close']
                                
                                if rev_direction == 'up':
                                    reversal_tp_bricks -= 1
                                    if reversal_tp_bricks == 0:
                                        reversal_profit = (0.2 - SPREAD) * 10 * lot_size
                                        outcome = 'SHORT_SL_LONG_RECOVERY'
                                        exit_price = rev_price
                                        exit_time = df.iloc[k]['datetime']
                                        log_message(f"LONG reversal recovery successful: +${reversal_profit:.2f}")
                                        break
                                elif rev_direction == 'down':
                                    # Reversal failed, exit with original loss
                                    outcome = 'SHORT_SL_RECOVERY_FAILED'
                                    exit_price = reversal_exit_price
                                    exit_time = reversal_exit_time
                                    log_message(f"LONG reversal recovery failed")
                                    break
                            
                            # Calculate total profit (original loss + reversal)
                            if reversal_profit > 0:
                                profit = sl_loss + reversal_profit
                            else:
                                profit = sl_loss
                            break
            
            # Time-based exit if no other exit
            if profit == 0:
                exit_idx = min(i + 10, len(df) - 1)
                exit_price = df.iloc[exit_idx]['close']
                exit_time = df.iloc[exit_idx]['datetime']
                
                if position_type == "LONG":
                    profit = (exit_price - entry_price) * 10 * lot_size
                else:
                    profit = (entry_price - exit_price) * 10 * lot_size
                outcome = 'TIME_EXIT'
            
            # Update account
            previous_equity = equity
            equity += profit
            equity_history.append(equity)
            trade_outcomes.append(profit)
            
# Analyze and react to loss patterns
            if profit <= 0:
                pattern_key = (entry_price, position_type, round(lot_size, 2), current_row['prob_long'], current_row['prob_short'])
                loss_patterns[pattern_key] += 1
                
                # Implement an opposing mechanism if specific patterns exceed a threshold
                if loss_patterns[pattern_key] > 3:  # example threshold
                    log_message(f"Pattern {pattern_key} observed. Considering opposing mechanism.")
                    # Implement a simple rule to reverse the next entry
                    opposing_position = "LONG" if position_type == "SHORT" else "SHORT"

                    next_row = df.iloc[min(i+1, len(df)-1)]
                    should_trade = True
                    position_type = opposing_position
                    entry_price = next_row['close']
                    entry_time = next_row['datetime']
                    log_message(f"Opposing trade initiated: {opposing_position} at {entry_time}, price: {entry_price:.4f}")

                    # Reset losses for this pattern
                    loss_patterns[pattern_key] = 0

            
            # Update streaks
            if profit > 0:
                win_streak += 1
                loss_streak = 0
                win_count += 1
            else:
                win_streak = 0
                loss_streak += 1
            
            # Record trade
            trades.append({
                'entry_time': entry_time,
                'entry_price': entry_price,
                'position_type': position_type,
                'volume': round(lot_size, 2),
                'exit_time': exit_time,
                'exit_price': exit_price,
                'outcome': outcome,
                'profit': round(profit, 2),
                'balance': round(equity, 2),
                'prob_long': current_row['prob_long'],
                'prob_short': current_row['prob_short']
            })
            
            open_volume -= lot_size
            
            # Jump ahead to avoid immediate re-entry
            i += 5
        else:
            i += 1
    else:
        i += 1

# Save results
if trades:
    results_df = pd.DataFrame(trades)
    results_df.to_csv(f"{log_dir}/streamlined_results_{timestamp}.csv", index=False)
    log_message(f"Saved {len(trades)} trades to streamlined_results_{timestamp}.csv")

# Calculate performance metrics
if len(trades) > 0:
    results_df = pd.DataFrame(trades)
    win_rate = (results_df['profit'] > 0).mean() * 100
    total_profit = results_df['profit'].sum()
    profitable_trades = results_df[results_df['profit'] > 0]['profit'].sum()
    losing_trades = abs(results_df[results_df['profit'] < 0]['profit'].sum())
    profit_factor = profitable_trades / (losing_trades + 1e-6)
else:
    win_rate = 0
    total_profit = 0
    profit_factor = 0

# Final report
log_message("\n=== STREAMLINED BACKTEST RESULTS ===")
log_message(f"Total Trades: {trade_count}")
log_message(f"Final Balance: ${equity:.2f}")
log_message(f"Total Profit: ${total_profit:.2f}")
log_message(f"Win Rate: {win_rate:.2f}%")
log_message(f"Win Count: {win_count} of {trade_count} trades")
log_message(f"Profit Factor: {profit_factor:.2f}")

# Plot equity curve
if len(equity_history) > 1:
    plt.figure(figsize=(12, 6))
    plt.plot(equity_history)
    plt.title('Streamlined Backtest - Equity Curve')
    plt.xlabel('Trade Number')
    plt.ylabel('Equity ($)')
    plt.grid(True)
    plt.savefig(f"{log_dir}/streamlined_equity_{timestamp}.png")
    log_message(f"Saved equity curve to streamlined_equity_{timestamp}.png")

log_message("Streamlined backtest completed!")
