import pytest
import numpy as np
import pandas as pd
from monte_carlo_simulation import monte_carlo_simulation


class TestMonteCarloSimulation:
    
    @pytest.fixture
    def normal_trade_returns(self):
        """Generate normal distributed trade returns for testing"""
        np.random.seed(42)  # For reproducible tests
        return np.random.normal(0.01, 0.05, 1000)  # 1% mean return, 5% std dev
    
    @pytest.fixture
    def known_distribution_returns(self):
        """Create a known distribution for analytical comparison"""
        # Simple case: 50% chance of +2%, 50% chance of -1%
        # Expected value = 0.5 * 0.02 + 0.5 * (-0.01) = 0.005 = 0.5%
        np.random.seed(42)
        returns = []
        for _ in range(1000):
            if np.random.random() < 0.5:
                returns.append(0.02)  # +2%
            else:
                returns.append(-0.01)  # -1%
        return np.array(returns)
    
    def test_monte_carlo_simulation_basic_functionality(self, normal_trade_returns):
        """Test that Monte Carlo simulation runs and returns expected structure"""
        results = monte_carlo_simulation(normal_trade_returns, num_simulations=1000)
        
        # Check that all expected keys are present
        expected_keys = ['final_return_distribution', 'max_drawdown_distribution', 'ruin_events_distribution']
        for key in expected_keys:
            assert key in results
        
        # Check that arrays have correct length
        assert len(results['final_return_distribution']) == 1000
        assert len(results['max_drawdown_distribution']) == 1000
        assert len(results['ruin_events_distribution']) == 1000
    
    def test_monte_carlo_mean_convergence(self, known_distribution_returns):
        """Test that Monte Carlo simulation converges to analytical expectation"""
        # Run simulation with many iterations for convergence
        results = monte_carlo_simulation(known_distribution_returns, num_simulations=5000)
        
        # Calculate sample mean of final returns
        sample_mean = np.mean(results['final_return_distribution'])
        
        # Analytical expectation for our known distribution
        # Each trade: 50% chance of +2%, 50% chance of -1%
        # Expected per-trade return = 0.5 * 0.02 + 0.5 * (-0.01) = 0.005
        # For 1000 trades with compounding: (1.005)^1000 - 1 ≈ 1.48
        analytical_expectation = (1.005 ** 1000) - 1
        
        # Monte Carlo should be close to analytical (within 10% due to sampling variation)
        relative_error = abs(sample_mean - analytical_expectation) / abs(analytical_expectation)
        assert relative_error < 0.1, f"Monte Carlo mean {sample_mean:.4f} too far from analytical {analytical_expectation:.4f}"
    
    def test_monte_carlo_statistical_properties(self, normal_trade_returns):
        """Test that Monte Carlo results have reasonable statistical properties"""
        results = monte_carlo_simulation(normal_trade_returns, num_simulations=2000)
        
        final_returns = results['final_return_distribution']
        
        # Check that we get a distribution of outcomes (not all the same)
        assert np.std(final_returns) > 0, "Monte Carlo should produce varied outcomes"
        
        # Check that some simulations are positive and some negative
        positive_outcomes = np.sum(final_returns > 0)
        negative_outcomes = np.sum(final_returns < 0)
        assert positive_outcomes > 0, "Should have some positive outcomes"
        assert negative_outcomes > 0, "Should have some negative outcomes"
        
        # Check that drawdowns are all negative or zero
        max_drawdowns = results['max_drawdown_distribution']
        assert np.all(max_drawdowns <= 0), "Max drawdowns should be negative or zero"
    
    def test_monte_carlo_with_known_profitable_strategy(self):
        """Test Monte Carlo with a strategy that should always be profitable"""
        # Create trades that always win (positive returns)
        profitable_returns = np.array([0.01] * 100)  # 1% per trade, 100 trades
        
        results = monte_carlo_simulation(profitable_returns, num_simulations=1000)
        
        # All final returns should be positive
        final_returns = results['final_return_distribution']
        assert np.all(final_returns > 0), "All outcomes should be positive for profitable strategy"
        
        # No ruin events should occur
        ruin_events = results['ruin_events_distribution']
        assert np.sum(ruin_events) == 0, "No ruin events should occur for profitable strategy"
        
        # Expected final return: (1.01)^100 - 1 ≈ 1.7048
        expected_return = (1.01 ** 100) - 1
        actual_mean = np.mean(final_returns)
        
        # Should be very close since there's no randomness in this case
        assert abs(actual_mean - expected_return) < 0.001
    
    def test_monte_carlo_with_known_losing_strategy(self):
        """Test Monte Carlo with a strategy that should always lose"""
        # Create trades that always lose
        losing_returns = np.array([-0.02] * 50)  # -2% per trade, 50 trades
        
        results = monte_carlo_simulation(losing_returns, num_simulations=1000)
        
        # All final returns should be negative
        final_returns = results['final_return_distribution']
        assert np.all(final_returns < 0), "All outcomes should be negative for losing strategy"
        
        # Expected final return: (0.98)^50 - 1 ≈ -0.636
        expected_return = (0.98 ** 50) - 1
        actual_mean = np.mean(final_returns)
        
        # Should be very close since there's no randomness
        assert abs(actual_mean - expected_return) < 0.001
    
    def test_monte_carlo_law_of_large_numbers(self):
        """Test that Monte Carlo follows law of large numbers"""
        # Create a simple symmetric random walk
        returns = np.array([0.01, -0.01] * 500)  # Alternating +1%, -1%
        
        # Run simulations with increasing sample sizes
        sample_sizes = [100, 500, 1000]
        means = []
        
        for n in sample_sizes:
            results = monte_carlo_simulation(returns, num_simulations=n)
            means.append(np.mean(results['final_return_distribution']))
        
        # Variance should decrease as sample size increases (law of large numbers)
        # We can't guarantee this for every run due to randomness, but the trend should generally hold
        # For this symmetric case, the mean should be close to 0
        final_mean = means[-1]
        assert abs(final_mean) < 0.1, f"Large sample mean {final_mean} should be close to 0 for symmetric walk"
    
    def test_monte_carlo_reproducibility(self, normal_trade_returns):
        """Test that Monte Carlo results are reproducible with same seed"""
        # Run simulation twice with same random seed
        np.random.seed(123)
        results1 = monte_carlo_simulation(normal_trade_returns, num_simulations=100)
        
        np.random.seed(123)
        results2 = monte_carlo_simulation(normal_trade_returns, num_simulations=100)
        
        # Results should be identical
        np.testing.assert_array_equal(results1['final_return_distribution'], results2['final_return_distribution'])
        np.testing.assert_array_equal(results1['max_drawdown_distribution'], results2['max_drawdown_distribution'])
        np.testing.assert_array_equal(results1['ruin_events_distribution'], results2['ruin_events_distribution'])
    
    def test_monte_carlo_central_limit_theorem(self, normal_trade_returns):
        """Test that Monte Carlo results follow central limit theorem"""
        results = monte_carlo_simulation(normal_trade_returns, num_simulations=2000)
        
        final_returns = results['final_return_distribution']
        
        # Test for approximate normality using basic statistical tests
        # (In a real scenario, you might use more sophisticated tests like Shapiro-Wilk)
        
        # Check that the distribution has reasonable kurtosis (not too far from normal)
        from scipy import stats
        try:
            kurtosis = stats.kurtosis(final_returns)
            # Normal distribution has kurtosis of 0, we allow some deviation
            assert abs(kurtosis) < 5, f"Kurtosis {kurtosis} suggests non-normal distribution"
        except ImportError:
            # Fallback test if scipy not available
            # Check that distribution isn't too skewed by comparing median to mean
            mean_return = np.mean(final_returns)
            median_return = np.median(final_returns)
            relative_diff = abs(mean_return - median_return) / (abs(mean_return) + 1e-8)
            assert relative_diff < 0.2, "Distribution appears highly skewed"


# Additional edge case tests
def test_monte_carlo_empty_input():
    """Test Monte Carlo with empty input"""
    empty_returns = np.array([])
    
    with pytest.raises((ValueError, IndexError)):
        monte_carlo_simulation(empty_returns, num_simulations=100)


def test_monte_carlo_single_trade():
    """Test Monte Carlo with single trade"""
    single_trade = np.array([0.05])  # 5% return
    
    results = monte_carlo_simulation(single_trade, num_simulations=100)
    
    # All results should be identical since there's only one trade
    final_returns = results['final_return_distribution']
    assert np.all(final_returns == 0.05), "All outcomes should be 5% for single trade"


def test_monte_carlo_zero_returns():
    """Test Monte Carlo with all zero returns"""
    zero_returns = np.zeros(100)
    
    results = monte_carlo_simulation(zero_returns, num_simulations=100)
    
    # All final returns should be zero
    final_returns = results['final_return_distribution']
    assert np.all(final_returns == 0), "All outcomes should be zero for zero returns"
    
    # No drawdowns should occur
    max_drawdowns = results['max_drawdown_distribution']
    assert np.all(max_drawdowns == 0), "No drawdowns should occur with zero returns"


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
