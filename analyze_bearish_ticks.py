import pandas as pd
import os

def analyze_bearish_ticks_per_minute(input_csv_path, output_csv_path):
    """
    Analyzes a CSV file of tick data to count the number of bearish ticks
    within each 1-minute candle.

    A tick is considered 'bearish' if its mid-price is lower than the
    previous tick's mid-price.

    Args:
        input_csv_path (str): The path to the input CSV file containing tick data.
        output_csv_path (str): The path to save the analysis results CSV file.
    """
    print(f"Starting analysis for {input_csv_path}...")

    # Check if the input file exists
    if not os.path.exists(input_csv_path):
        print(f"Error: Input file not found at {input_csv_path}")
        return

    # Load the raw data
    try:
        raw_df = pd.read_csv(input_csv_path)
        print(f"Loaded {len(raw_df)} raw rows.")
    except Exception as e:
        print(f"Error loading CSV file: {e}")
        return

    # Parse the tuple-like string data into structured columns
    parsed_data = []
    for index, row in raw_df.iterrows():
        try:
            # Assuming the data is in a column named '0' as per previous examples
            tuple_str = row['0']
            if isinstance(tuple_str, str) and tuple_str.startswith('(') and tuple_str.endswith(')'):
                values = tuple_str[1:-1].split(', ')
                if len(values) >= 3:
                    parsed_data.append({
                        'timestamp': int(values[0]),
                        'bid': float(values[1]),
                        'ask': float(values[2]),
                    })
        except (ValueError, IndexError, KeyError) as e:
            # Silently skip rows that don't parse correctly
            # print(f"Skipping row {index} due to parsing error: {e}")
            continue
    
    if not parsed_data:
        print("Error: No valid tick data could be parsed from the file.")
        return

    df = pd.DataFrame(parsed_data)
    df['datetime'] = pd.to_datetime(df['timestamp'], unit='s')
    df = df.sort_values('datetime').reset_index(drop=True)
    print(f"Parsed {len(df)} valid ticks.")

    # Calculate mid-price and identify bearish ticks
    df['mid_price'] = (df['bid'] + df['ask']) / 2
    df['price_change'] = df['mid_price'].diff()
    df['is_bearish'] = df['price_change'] < 0
    
    # Set datetime as the index for resampling
    df.set_index('datetime', inplace=True)

    # Resample to 1-minute intervals and aggregate results
    print("Aggregating ticks into 1-minute candles...")
    analysis_df = df['is_bearish'].resample('1T').agg(
        total_ticks='count',
        bearish_ticks='sum'
    ).reset_index()

    # Calculate the percentage of bearish ticks
    analysis_df['bearish_ticks_pct'] = (analysis_df['bearish_ticks'] / analysis_df['total_ticks'] * 100).round(2)
    
    # Save the results to a new CSV
    analysis_df.to_csv(output_csv_path, index=False)
    print(f"Analysis complete! Results saved to {output_csv_path}")
    print("\n--- Sample of the results ---")
    print(analysis_df.head())
    print("--------------------------")

if __name__ == "__main__":
    # Define file paths using raw strings to avoid backslash issues
    INPUT_FILE = r'C:\Users\<USER>\META\Boom_1000_Index_7days_20250619_20250626.csv'
    OUTPUT_FILE = r'C:\Users\<USER>\META\bearish_ticks_per_minute_analysis.csv'
    
    # Run the analysis
    analyze_bearish_ticks_per_minute(INPUT_FILE, OUTPUT_FILE)