"""
MT5 Tick Data Downloader
Complete script to download tick data from MetaTrader 5 terminal via Python API
"""

import MetaTrader5 as mt5
import pandas as pd
from datetime import datetime, timedelta
import pytz
import os

class MT5TickDownloader:
    def __init__(self):
        self.initialized = False
    
    def initialize_mt5(self, path=None, login=None, password=None, server=None):
        """Initialize connection to MT5 terminal"""
        if path and login and password and server:
            # Full initialization with credentials
            if not mt5.initialize(path, login=login, password=password, server=server):
                print(f"initialize() failed, error code = {mt5.last_error()}")
                return False
        elif path:
            # Initialize with specific MT5 path
            if not mt5.initialize(path):
                print(f"initialize() failed, error code = {mt5.last_error()}")
                return False
        else:
            # Simple initialization (use default/already logged in terminal)
            if not mt5.initialize():
                print(f"initialize() failed, error code = {mt5.last_error()}")
                return False
        
        self.initialized = True
        print("OK: MT5 connection established")
        
        # Display connection info
        print(f"Info: Terminal info: {mt5.terminal_info()}")
        print(f"Info: Account info: {mt5.account_info()}")
        
        return True
    
    def get_available_symbols(self):
        """Get list of available symbols"""
        if not self.initialized:
            print("Error: MT5 not initialized")
            return None
        
        symbols = mt5.symbols_get()
        if symbols is None:
            print("Error: No symbols found")
            return None
        
        symbol_names = [symbol.name for symbol in symbols]
        print("Info: Available symbols")
        return symbol_names
    
    def download_tick_data(self, symbol, date_from, count=100000, tick_type="ALL", save_to_csv=True):
        """
        Download tick data from MT5
        
        Parameters:
        - symbol: str - Symbol name (e.g., "EURUSD", "GBPUSD")
        - date_from: datetime - Start date for tick data
        - count: int - Number of ticks to download
        - tick_type: str - "ALL", "INFO" (bid/ask), or "TRADE" (last/volume)
        - save_to_csv: bool - Save data to CSV file
        """
        if not self.initialized:
            print("Error: MT5 not initialized")
            return None
        
        # Set timezone to UTC
        timezone = pytz.timezone("Etc/UTC")
        
        # Ensure date_from is UTC
        if date_from.tzinfo is None:
            utc_from = timezone.localize(date_from)
        else:
            utc_from = date_from.astimezone(timezone)
        
        # Set tick flag
        if tick_type.upper() == "ALL":
            flag = mt5.COPY_TICKS_ALL
        elif tick_type.upper() == "INFO":
            flag = mt5.COPY_TICKS_INFO
        elif tick_type.upper() == "TRADE":
            flag = mt5.COPY_TICKS_TRADE
        else:
            flag = mt5.COPY_TICKS_ALL
        
        print(f"Info: Downloading {count} ticks for {symbol} from {utc_from}")
        
        # Download ticks
        ticks = mt5.copy_ticks_from(symbol, utc_from, count, flag)
        
        if ticks is None:
            print(f"Error: No ticks received for {symbol}, error: {mt5.last_error()}")
            return None
        
        print(f"OK: Downloaded {len(ticks)} ticks for {symbol}")
        
        # Convert to DataFrame
        ticks_frame = pd.DataFrame(ticks)
        
        # Convert time columns to readable format
        ticks_frame['time'] = pd.to_datetime(ticks_frame['time'], unit='s')
        ticks_frame['time_msc'] = pd.to_datetime(ticks_frame['time_msc'], unit='ms')
        
        # Add symbol column
        ticks_frame['symbol'] = symbol
        
        # Reorder columns for better readability
        column_order = ['symbol', 'time', 'time_msc', 'bid', 'ask', 'last', 'volume', 'flags', 'volume_real']
        ticks_frame = ticks_frame[[col for col in column_order if col in ticks_frame.columns]]
        
        if save_to_csv:
            filename = f"{symbol}_ticks_{date_from.strftime('%Y%m%d')}_{count}.csv"
            ticks_frame.to_csv(filename, index=False)
            print(f"💾 Data saved to {filename}")
        
        return ticks_frame
    
    def download_tick_range(self, symbol, date_from, date_to, tick_type="ALL", save_to_csv=True):
        """
        Download tick data for a specific date range
        
        Parameters:
        - symbol: str - Symbol name
        - date_from: datetime - Start date
        - date_to: datetime - End date
        - tick_type: str - Type of ticks
        - save_to_csv: bool - Save to CSV
        """
        if not self.initialized:
            print("Error: MT5 not initialized")
            return None
        
        # Set timezone to UTC
        timezone = pytz.timezone("Etc/UTC")
        
        # Ensure dates are UTC
        if date_from.tzinfo is None:
            utc_from = timezone.localize(date_from)
        else:
            utc_from = date_from.astimezone(timezone)
            
        if date_to.tzinfo is None:
            utc_to = timezone.localize(date_to)
        else:
            utc_to = date_to.astimezone(timezone)
        
        # Set tick flag
        if tick_type.upper() == "ALL":
            flag = mt5.COPY_TICKS_ALL
        elif tick_type.upper() == "INFO":
            flag = mt5.COPY_TICKS_INFO
        elif tick_type.upper() == "TRADE":
            flag = mt5.COPY_TICKS_TRADE
        else:
            flag = mt5.COPY_TICKS_ALL
        
        print(f"📥 Downloading ticks for {symbol} from {utc_from} to {utc_to}")
        
        # Download ticks
        ticks = mt5.copy_ticks_range(symbol, utc_from, utc_to, flag)
        
        if ticks is None:
            print(f"Error: No ticks received for {symbol}, error: {mt5.last_error()}")
            return None
        
        print(f"OK: Downloaded {len(ticks)} ticks for {symbol}")
        
        # Convert to DataFrame
        ticks_frame = pd.DataFrame(ticks)
        
        # Convert time columns
        ticks_frame['time'] = pd.to_datetime(ticks_frame['time'], unit='s')
        ticks_frame['time_msc'] = pd.to_datetime(ticks_frame['time_msc'], unit='ms')
        
        # Add symbol column
        ticks_frame['symbol'] = symbol
        
        if save_to_csv:
            filename = f"{symbol}_ticks_{date_from.strftime('%Y%m%d')}_{date_to.strftime('%Y%m%d')}.csv"
            ticks_frame.to_csv(filename, index=False)
            print(f"💾 Data saved to {filename}")
        
        return ticks_frame
    
    def get_current_tick(self, symbol):
        """Get current tick for a symbol"""
        if not self.initialized:
            print("Error: MT5 not initialized")
            return None
        
        tick = mt5.symbol_info_tick(symbol)
        if tick is None:
            print(f"Error: Failed to get tick for {symbol}")
            return None
        
        return {
            'symbol': symbol,
            'time': pd.to_datetime(tick.time, unit='s'),
            'bid': tick.bid,
            'ask': tick.ask,
            'last': tick.last,
            'volume': tick.volume
        }
    
    def shutdown(self):
        """Close MT5 connection"""
        mt5.shutdown()
        self.initialized = False
        print("🔌 MT5 connection closed")

# Example usage
def main():
    """Downloads 7 days of tick data for Boom 1000 Index.0"""
    
    # Initialize downloader
    downloader = MT5TickDownloader()
    
    # Connect to MT5 (adjust path if needed)
    if not downloader.initialize_mt5():
        return
    
    try:
        symbol = "Boom 1000 Index.0"
        date_to = datetime.now()
        date_from = date_to - timedelta(days=7)
        
        print(f"\n📊 Downloading 7 days of tick data for {symbol}")
        ticks_df = downloader.download_tick_range(
            symbol=symbol,
            date_from=date_from,
            date_to=date_to,
            tick_type="ALL"
        )
        
        if ticks_df is not None:
            print(f"Successfully downloaded {len(ticks_df)} ticks.")
            print(f"Sample data (first 5 rows):")
            print(ticks_df.head())
            print(f"Data saved to {symbol}_ticks_{date_from.strftime('%Y%m%d')}_{date_to.strftime('%Y%m%d')}.csv")
        
    except Exception as e:
        print(f"Error: Error during download: {e}")
    
    finally:
        # Always close connection
        downloader.shutdown()

if __name__ == "__main__":
    main()
