import numpy as np

# Monte Carlo Simulation Function
def monte_carlo_simulation(trade_results, num_simulations=10000):
    """
    Performs a Monte Carlo simulation on the trade results.

    Parameters:
    trade_results (ndarray): The initial trade results to resample.
    num_simulations (int): Number of simulation runs.

    Returns:
    dict: Contains the results of the simulation.
    """
    n_trades = len(trade_results)
    final_returns = []
    max_drawdowns = []
    ruin_events = []

    for _ in range(num_simulations):
        # Resample with replacement
        sample = np.random.choice(trade_results, size=n_trades, replace=True)

        # Build synthetic equity curve
        equity_curve = np.cumprod(1 + sample)

        # Record statistics
        final_return = equity_curve[-1] - 1
        max_drawdown = np.min(equity_curve) - np.max(equity_curve)
        ruin_event = np.any(equity_curve <= 0.5 * equity_curve[0])

        final_returns.append(final_return)
        max_drawdowns.append(max_drawdown)
        ruin_events.append(ruin_event)

    # Statistical summary
    results = {
        'final_return_distribution': np.array(final_returns),
        'max_drawdown_distribution': np.array(max_drawdowns),
        'ruin_events_distribution': np.array(ruin_events)
    }

    return results

# Example Usage
if __name__ == "__main__":
    example_trade_results = np.random.normal(0, 0.01, 1000)  # Simulate 1000 trades with small returns
    results = monte_carlo_simulation(example_trade_results)
    
    # Detailed statistical analysis
    final_returns = results['final_return_distribution']
    max_drawdowns = results['max_drawdown_distribution']
    ruin_events = results['ruin_events_distribution']
    
    print("\n=== MONTE CARLO SIMULATION RESULTS ===")
    print(f"Number of simulations: {len(final_returns)}")
    print(f"\nFINAL RETURNS:")
    print(f"  Positive returns: {np.sum(final_returns > 0)} ({np.sum(final_returns > 0)/len(final_returns)*100:.1f}%)")
    print(f"  Negative returns: {np.sum(final_returns < 0)} ({np.sum(final_returns < 0)/len(final_returns)*100:.1f}%)")
    print(f"  Break-even: {np.sum(final_returns == 0)} ({np.sum(final_returns == 0)/len(final_returns)*100:.1f}%)")
    print(f"  Average return: {np.mean(final_returns)*100:.2f}%")
    print(f"  Best case: {np.max(final_returns)*100:.2f}%")
    print(f"  Worst case: {np.min(final_returns)*100:.2f}%")
    
    print(f"\nDRAWDOWNS:")
    print(f"  Average max drawdown: {np.mean(max_drawdowns)*100:.2f}%")
    print(f"  Worst drawdown: {np.min(max_drawdowns)*100:.2f}%")
    print(f"  Best drawdown: {np.max(max_drawdowns)*100:.2f}%")
    
    print(f"\nRUIN EVENTS:")
    print(f"  Total ruin events: {np.sum(ruin_events)} ({np.sum(ruin_events)/len(ruin_events)*100:.2f}%)")
    print(f"  Account survived: {np.sum(~ruin_events)} ({np.sum(~ruin_events)/len(ruin_events)*100:.2f}%)")

