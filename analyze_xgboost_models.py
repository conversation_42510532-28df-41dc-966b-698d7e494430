import pandas as pd
import numpy as np
import xgboost as xgb
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, roc_curve
from sklearn.metrics import precision_recall_curve, average_precision_score
import shap
import warnings
warnings.filterwarnings('ignore')

class XGBoostModelAnalyzer:
    """
    Comprehensive analyzer for XGBoost spike and range models used in Range Break Index trading
    """
    
    def __init__(self, data_file, spike_model_file, range_model_file):
        self.data_file = data_file
        self.spike_model_file = spike_model_file
        self.range_model_file = range_model_file
        self.df = None
        self.minute_data = None
        self.features = None
        self.X = None
        self.spike_model = None
        self.range_model = None
        
    def load_data_and_models(self):
        """Load data and trained models"""
        print("Loading data and models...")
        
        # Load data
        try:
            self.df = pd.read_csv(self.data_file)
            print(f"Loaded {len(self.df)} tick records")
        except Exception as e:
            print(f"Error loading data: {e}")
            return False
            
        # Load models
        try:
            self.spike_model = xgb.XGBClassifier()
            self.spike_model.load_model(self.spike_model_file)
            print("Spike model loaded successfully")
            
            self.range_model = xgb.XGBClassifier()
            self.range_model.load_model(self.range_model_file)
            print("Range model loaded successfully")
        except Exception as e:
            print(f"Error loading models: {e}")
            return False
            
        return True
    
    def prepare_features(self):
        """Prepare features exactly as done in training"""
        print("Preparing features...")
        
        # Ensure 'time' column is datetime and sort
        self.df['time'] = pd.to_datetime(self.df['time'])
        self.df = self.df.sort_values('time').reset_index(drop=True)
        
        # Create minute-level OHLCV data
        self.df['minute'] = self.df['time'].dt.floor('1min')
        self.minute_data = self.df.groupby('minute').agg(
            open=('bid', 'first'),
            high=('bid', 'max'),
            low=('bid', 'min'),
            close=('bid', 'last'),
            volume=('bid', 'size'),
            ask_open=('ask', 'first'),
            ask_high=('ask', 'max'),
            ask_low=('ask', 'min'),
            ask_close=('ask', 'last'),
        )
        
        self.minute_data['range'] = self.minute_data['high'] - self.minute_data['low']
        self.minute_data['spread'] = self.minute_data['ask_close'] - self.minute_data['close']
        
        # Lagged features
        for lag in range(1, 6):
            self.minute_data[f'close_lag_{lag}'] = self.minute_data['close'].shift(lag)
            self.minute_data[f'range_lag_{lag}'] = self.minute_data['range'].shift(lag)
            self.minute_data[f'volume_lag_{lag}'] = self.minute_data['volume'].shift(lag)
            self.minute_data[f'spread_lag_{lag}'] = self.minute_data['spread'].shift(lag)
        
        # Rolling features
        self.minute_data['bid_std_5min'] = self.minute_data['close'].rolling(window=5).std()
        self.minute_data['range_mean_5min'] = self.minute_data['range'].rolling(window=5).mean()
        
        # Time-based features
        self.minute_data['hour'] = self.minute_data.index.hour
        self.minute_data['day_of_week'] = self.minute_data.index.dayofweek
        
        # Fill NaN values
        self.minute_data = self.minute_data.fillna(0).reset_index()
        
        # Define features
        self.features = [
            'open', 'high', 'low', 'close', 'volume', 'range', 'spread',
            'ask_open', 'ask_high', 'ask_low', 'ask_close',
            'hour', 'day_of_week',
        ]
        for lag in range(1, 6):
            self.features.extend([f'close_lag_{lag}', f'range_lag_{lag}', f'volume_lag_{lag}', f'spread_lag_{lag}'])
        self.features.extend(['bid_std_5min', 'range_mean_5min'])
        
        self.X = self.minute_data[self.features]
        print(f"Prepared {len(self.X)} samples with {len(self.features)} features")
        
    def create_true_labels(self, spike_threshold_pct=0.01, min_range_threshold=10.0):
        """Create true labels for validation (same logic as training)"""
        print("Creating true labels for validation...")
        
        # Spike identification
        self.df['bid_change_pct'] = self.df['bid'].pct_change() * 100
        self.df['is_spike_tick'] = self.df['bid_change_pct'].abs() > spike_threshold_pct
        
        # Aggregate to minute level
        spike_by_minute = self.df.groupby('minute')['is_spike_tick'].max()
        self.minute_data['has_spike'] = self.minute_data['minute'].map(spike_by_minute).fillna(0).astype(int)
        
        # Tradable range identification
        self.minute_data['is_tradable_range'] = (self.minute_data['range'] >= min_range_threshold).astype(int)
        
        print(f"Spike distribution: {self.minute_data['has_spike'].value_counts().to_dict()}")
        print(f"Tradable range distribution: {self.minute_data['is_tradable_range'].value_counts().to_dict()}")
        
    def analyze_model_performance(self):
        """Comprehensive model performance analysis"""
        print("\n" + "="*60)
        print("MODEL PERFORMANCE ANALYSIS")
        print("="*60)
        
        # Get predictions
        spike_pred = self.spike_model.predict(self.X)
        spike_pred_proba = self.spike_model.predict_proba(self.X)
        
        range_pred = self.range_model.predict(self.X)
        range_pred_proba = self.range_model.predict_proba(self.X)
        
        # True labels
        y_spike_true = self.minute_data['has_spike']
        y_range_true = self.minute_data['is_tradable_range']
        
        print("\n--- SPIKE MODEL PERFORMANCE ---")
        print("Classification Report:")
        print(classification_report(y_spike_true, spike_pred))
        print("\nConfusion Matrix:")
        print(confusion_matrix(y_spike_true, spike_pred))
        
        if len(np.unique(y_spike_true)) > 1:
            spike_auc = roc_auc_score(y_spike_true, spike_pred_proba[:, 1])
            print(f"ROC AUC Score: {spike_auc:.4f}")
        
        print("\n--- RANGE MODEL PERFORMANCE ---")
        print("Classification Report:")
        print(classification_report(y_range_true, range_pred))
        print("\nConfusion Matrix:")
        print(confusion_matrix(y_range_true, range_pred))
        
        if len(np.unique(y_range_true)) > 1:
            range_auc = roc_auc_score(y_range_true, range_pred_proba[:, 1])
            print(f"ROC AUC Score: {range_auc:.4f}")
        
        return {
            'spike_pred': spike_pred,
            'spike_pred_proba': spike_pred_proba,
            'range_pred': range_pred,
            'range_pred_proba': range_pred_proba,
            'y_spike_true': y_spike_true,
            'y_range_true': y_range_true
        }

    def analyze_feature_importance(self):
        """Analyze feature importance for both models"""
        print("\n" + "="*60)
        print("FEATURE IMPORTANCE ANALYSIS")
        print("="*60)

        # Get feature importance
        spike_importance = self.spike_model.feature_importances_
        range_importance = self.range_model.feature_importances_

        # Create importance dataframes
        spike_imp_df = pd.DataFrame({
            'feature': self.features,
            'importance': spike_importance
        }).sort_values('importance', ascending=False)

        range_imp_df = pd.DataFrame({
            'feature': self.features,
            'importance': range_importance
        }).sort_values('importance', ascending=False)

        print("\n--- TOP 10 FEATURES FOR SPIKE MODEL ---")
        print(spike_imp_df.head(10))

        print("\n--- TOP 10 FEATURES FOR RANGE MODEL ---")
        print(range_imp_df.head(10))

        # Plot feature importance
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))

        # Spike model importance
        top_spike_features = spike_imp_df.head(15)
        ax1.barh(range(len(top_spike_features)), top_spike_features['importance'])
        ax1.set_yticks(range(len(top_spike_features)))
        ax1.set_yticklabels(top_spike_features['feature'])
        ax1.set_title('Top 15 Features - Spike Model')
        ax1.set_xlabel('Feature Importance')

        # Range model importance
        top_range_features = range_imp_df.head(15)
        ax2.barh(range(len(top_range_features)), top_range_features['importance'])
        ax2.set_yticks(range(len(top_range_features)))
        ax2.set_yticklabels(top_range_features['feature'])
        ax2.set_title('Top 15 Features - Range Model')
        ax2.set_xlabel('Feature Importance')

        plt.tight_layout()
        plt.savefig('model_feature_importance.png', dpi=300, bbox_inches='tight')
        plt.show()

        return spike_imp_df, range_imp_df

    def analyze_prediction_patterns(self, results):
        """Analyze prediction patterns and trading signals"""
        print("\n" + "="*60)
        print("PREDICTION PATTERN ANALYSIS")
        print("="*60)

        # Add predictions to minute_data
        self.minute_data['spike_pred'] = results['spike_pred']
        self.minute_data['range_pred'] = results['range_pred']
        self.minute_data['spike_prob'] = results['spike_pred_proba'][:, 1]
        self.minute_data['range_prob'] = results['range_pred_proba'][:, 1]

        # Trading signal analysis
        trading_signals = (self.minute_data['spike_pred'] == 0) & (self.minute_data['range_pred'] == 1)
        self.minute_data['trading_signal'] = trading_signals

        print(f"Total minutes analyzed: {len(self.minute_data)}")
        print(f"Trading signals generated: {trading_signals.sum()}")
        print(f"Trading signal rate: {trading_signals.sum() / len(self.minute_data) * 100:.2f}%")

        # Analyze signal distribution by hour
        signal_by_hour = self.minute_data.groupby('hour')['trading_signal'].agg(['sum', 'count', 'mean'])
        signal_by_hour['signal_rate'] = signal_by_hour['mean'] * 100

        print("\n--- TRADING SIGNALS BY HOUR ---")
        print(signal_by_hour)

        # Analyze prediction probabilities
        print("\n--- PREDICTION PROBABILITY STATISTICS ---")
        print("Spike Probabilities:")
        print(self.minute_data['spike_prob'].describe())
        print("\nRange Probabilities:")
        print(self.minute_data['range_prob'].describe())

        # Plot prediction distributions
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # Spike predictions
        axes[0,0].hist(self.minute_data['spike_prob'], bins=50, alpha=0.7, edgecolor='black')
        axes[0,0].set_title('Spike Probability Distribution')
        axes[0,0].set_xlabel('Probability')
        axes[0,0].set_ylabel('Frequency')

        # Range predictions
        axes[0,1].hist(self.minute_data['range_prob'], bins=50, alpha=0.7, edgecolor='black')
        axes[0,1].set_title('Range Probability Distribution')
        axes[0,1].set_xlabel('Probability')
        axes[0,1].set_ylabel('Frequency')

        # Trading signals by hour
        axes[1,0].bar(signal_by_hour.index, signal_by_hour['signal_rate'])
        axes[1,0].set_title('Trading Signal Rate by Hour')
        axes[1,0].set_xlabel('Hour of Day')
        axes[1,0].set_ylabel('Signal Rate (%)')

        # Combined probability scatter
        scatter = axes[1,1].scatter(self.minute_data['spike_prob'], self.minute_data['range_prob'],
                                   c=self.minute_data['trading_signal'], alpha=0.6, cmap='RdYlBu')
        axes[1,1].set_title('Spike vs Range Probabilities')
        axes[1,1].set_xlabel('Spike Probability')
        axes[1,1].set_ylabel('Range Probability')
        plt.colorbar(scatter, ax=axes[1,1], label='Trading Signal')

        plt.tight_layout()
        plt.savefig('prediction_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

        return signal_by_hour

    def analyze_model_robustness(self, results):
        """Analyze model robustness and potential issues"""
        print("\n" + "="*60)
        print("MODEL ROBUSTNESS ANALYSIS")
        print("="*60)

        # Check for perfect predictions (potential overfitting)
        spike_perfect = (results['spike_pred'] == results['y_spike_true']).all()
        range_perfect = (results['range_pred'] == results['y_range_true']).all()

        print(f"Spike model perfect accuracy: {spike_perfect}")
        print(f"Range model perfect accuracy: {range_perfect}")

        if spike_perfect or range_perfect:
            print("⚠️  WARNING: Perfect accuracy detected - potential overfitting!")

        # Check prediction distribution
        spike_pred_dist = pd.Series(results['spike_pred']).value_counts()
        range_pred_dist = pd.Series(results['range_pred']).value_counts()

        print(f"\nSpike prediction distribution: {spike_pred_dist.to_dict()}")
        print(f"Range prediction distribution: {range_pred_dist.to_dict()}")

        # Check for extreme probability values
        spike_extreme_probs = ((results['spike_pred_proba'][:, 1] < 0.01) |
                              (results['spike_pred_proba'][:, 1] > 0.99)).sum()
        range_extreme_probs = ((results['range_pred_proba'][:, 1] < 0.01) |
                              (results['range_pred_proba'][:, 1] > 0.99)).sum()

        print(f"\nExtreme spike probabilities (< 0.01 or > 0.99): {spike_extreme_probs}")
        print(f"Extreme range probabilities (< 0.01 or > 0.99): {range_extreme_probs}")

        if spike_extreme_probs > len(results['spike_pred']) * 0.5:
            print("⚠️  WARNING: High number of extreme spike probabilities!")
        if range_extreme_probs > len(results['range_pred']) * 0.5:
            print("⚠️  WARNING: High number of extreme range probabilities!")

    def generate_model_summary(self):
        """Generate comprehensive model summary"""
        print("\n" + "="*60)
        print("MODEL SUMMARY REPORT")
        print("="*60)

        # Model parameters
        print("\n--- SPIKE MODEL PARAMETERS ---")
        spike_params = self.spike_model.get_params()
        for key, value in spike_params.items():
            print(f"{key}: {value}")

        print("\n--- RANGE MODEL PARAMETERS ---")
        range_params = self.range_model.get_params()
        for key, value in range_params.items():
            print(f"{key}: {value}")

        # Feature statistics
        print(f"\n--- FEATURE STATISTICS ---")
        print(f"Total features: {len(self.features)}")
        print(f"OHLCV features: 11")
        print(f"Lagged features: 20")
        print(f"Rolling features: 2")
        print(f"Time features: 2")

        # Data statistics
        print(f"\n--- DATA STATISTICS ---")
        print(f"Total tick records: {len(self.df)}")
        print(f"Total minute intervals: {len(self.minute_data)}")
        print(f"Date range: {self.df['time'].min()} to {self.df['time'].max()}")
        print(f"Duration: {(self.df['time'].max() - self.df['time'].min()).days} days")

    def run_complete_analysis(self):
        """Run complete model analysis"""
        print("Starting comprehensive XGBoost model analysis...")

        # Load data and models
        if not self.load_data_and_models():
            return

        # Prepare features
        self.prepare_features()

        # Create true labels
        self.create_true_labels()

        # Analyze model performance
        results = self.analyze_model_performance()

        # Analyze feature importance
        spike_imp, range_imp = self.analyze_feature_importance()

        # Analyze prediction patterns
        signal_analysis = self.analyze_prediction_patterns(results)

        # Analyze model robustness
        self.analyze_model_robustness(results)

        # Generate summary
        self.generate_model_summary()

        print("\n" + "="*60)
        print("ANALYSIS COMPLETE")
        print("="*60)
        print("Generated files:")
        print("- model_feature_importance.png")
        print("- prediction_analysis.png")

        return {
            'results': results,
            'spike_importance': spike_imp,
            'range_importance': range_imp,
            'signal_analysis': signal_analysis
        }

def main():
    """Main execution function"""
    # Configuration
    data_file = "Range_Break_100_7days_20250623_20250630.csv"
    spike_model_file = "xgboost_spike_model.ubj"
    range_model_file = "xgboost_range_model.ubj"

    # Create analyzer
    analyzer = XGBoostModelAnalyzer(data_file, spike_model_file, range_model_file)

    # Run complete analysis
    analysis_results = analyzer.run_complete_analysis()

    return analysis_results

if __name__ == "__main__":
    results = main()
