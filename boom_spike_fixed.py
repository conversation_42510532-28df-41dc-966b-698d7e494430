import pandas as pd
import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report, confusion_matrix, precision_recall_curve, roc_auc_score
import matplotlib.pyplot as plt
import time
import logging
from datetime import datetime
import argparse

def setup_logging():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"boom_spike_fixed_{timestamp}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

class BoomDataAnalyzerFixed:
    """Fixed Boom data analyzer WITHOUT data leakage"""
    
    def __init__(self, csv_file, logger=None):
        self.logger = logger or logging.getLogger(__name__)
        self.logger.info("Loading Boom data...")
        
        self.data = pd.read_csv(csv_file)
        self.logger.info(f"Loaded {len(self.data)} tick records")
        
        self.data['datetime'] = pd.to_datetime(self.data['time'])
        self.data = self.data.sort_values('datetime').reset_index(drop=True)
        self.data['price'] = self.data['bid']
        
    def detect_spikes_properly(self, spike_threshold_pct=0.3):
        """Detect spikes WITHOUT using future data"""
        
        self.logger.info("Detecting spikes (without data leakage)...")
        
        # Calculate price changes
        self.data['price_change'] = self.data['price'].diff()
        self.data['price_change_pct'] = (self.data['price_change'] / self.data['price'].shift(1)) * 100
        
        # Detect spikes (current tick only)
        self.data['is_spike'] = (
            (self.data['price_change_pct'] > spike_threshold_pct) | 
            (self.data['price_change_pct'] < -spike_threshold_pct)
        ).astype(int)
        
        # Create target: will NEXT tick be uniform? (no future counting!)
        uniform_threshold = 0.05
        self.data['next_tick_uniform'] = 0
        
        # For each tick, check if the NEXT tick will be uniform
        for i in range(len(self.data) - 1):
            next_change = abs(self.data.loc[i + 1, 'price_change_pct'])
            if next_change <= uniform_threshold:
                self.data.loc[i, 'next_tick_uniform'] = 1
        
        spike_count = self.data['is_spike'].sum()
        self.logger.info(f"Detected {spike_count} spikes")
        
        # Analyze relationship between spikes and next tick behavior
        spike_indices = self.data[self.data['is_spike'] == 1].index
        uniform_after_spike = 0
        
        for spike_idx in spike_indices:
            if spike_idx < len(self.data) - 1:
                if self.data.loc[spike_idx, 'next_tick_uniform'] == 1:
                    uniform_after_spike += 1
        
        uniform_rate = uniform_after_spike / len(spike_indices) if len(spike_indices) > 0 else 0
        self.logger.info(f"Rate of uniform ticks immediately after spikes: {uniform_rate:.2%}")
        
        return spike_count, uniform_rate
    
    def create_features_without_leakage(self, lookback_window=20):
        """Create features using ONLY past data"""
        
        self.logger.info(f"Creating features without data leakage (lookback_window={lookback_window})")
        
        features = []
        
        # Price-based features (past data only)
        for i in range(1, lookback_window + 1):
            self.data[f'price_lag_{i}'] = self.data['price'].shift(i)
            self.data[f'change_lag_{i}'] = self.data['price_change'].shift(i)
            self.data[f'change_pct_lag_{i}'] = self.data['price_change_pct'].shift(i)
            features.extend([f'price_lag_{i}', f'change_lag_{i}', f'change_pct_lag_{i}'])
        
        # Moving averages (past data only)
        for window in [5, 10, 20]:
            self.data[f'ma_{window}'] = self.data['price'].rolling(window=window).mean()
            self.data[f'price_vs_ma_{window}'] = self.data['price'] - self.data[f'ma_{window}']
            features.extend([f'ma_{window}', f'price_vs_ma_{window}'])
        
        # Volatility features (past data only)
        for window in [5, 10, 20]:
            self.data[f'volatility_{window}'] = self.data['price_change_pct'].rolling(window=window).std()
            features.append(f'volatility_{window}')
        
        # Momentum features (past data only)
        for window in [3, 5, 10]:
            self.data[f'momentum_{window}'] = self.data['price'] / self.data['price'].shift(window) - 1
            features.append(f'momentum_{window}')
        
        # Time-based features
        self.data['hour'] = self.data['datetime'].dt.hour
        self.data['minute'] = self.data['datetime'].dt.minute
        self.data['day_of_week'] = self.data['datetime'].dt.dayofweek
        features.extend(['hour', 'minute', 'day_of_week'])
        
        # Recent spike activity (past data only)
        for window in [10, 50, 100]:
            self.data[f'recent_spikes_{window}'] = self.data['is_spike'].rolling(window=window).sum()
            features.append(f'recent_spikes_{window}')
        
        # Statistical features of recent price changes
        for window in [5, 10, 20]:
            self.data[f'price_min_{window}'] = self.data['price'].rolling(window=window).min()
            self.data[f'price_max_{window}'] = self.data['price'].rolling(window=window).max()
            self.data[f'price_range_{window}'] = self.data[f'price_max_{window}'] - self.data[f'price_min_{window}']
            features.extend([f'price_min_{window}', f'price_max_{window}', f'price_range_{window}'])
        
        # Price acceleration features
        self.data['price_change_acceleration'] = self.data['price_change'].diff()
        self.data['price_change_acceleration_lag1'] = self.data['price_change_acceleration'].shift(1)
        features.extend(['price_change_acceleration', 'price_change_acceleration_lag1'])
        
        self.feature_columns = features
        
        initial_len = len(self.data)
        self.data.dropna(inplace=True)
        final_len = len(self.data)
        
        self.logger.info(f"Features created. Data reduced from {initial_len} to {final_len} rows")
        self.logger.info(f"Total features: {len(features)}")
        
        return features

class BoomDataset(Dataset):
    """Dataset for Boom spike prediction"""
    
    def __init__(self, X, y_spike, y_next_uniform):
        self.X = torch.FloatTensor(X)
        self.y_spike = torch.LongTensor(y_spike)
        self.y_next_uniform = torch.LongTensor(y_next_uniform)
    
    def __len__(self):
        return len(self.X)
    
    def __getitem__(self, idx):
        return self.X[idx], self.y_spike[idx], self.y_next_uniform[idx]

class BoomSpikeModelFixed(nn.Module):
    """Fixed dual-task model: spike prediction + next tick uniform prediction"""
    
    def __init__(self, input_size, hidden_size=256, dropout=0.3):
        super(BoomSpikeModelFixed, self).__init__()
        
        # Larger network to handle complex patterns
        self.shared_layers = nn.Sequential(
            nn.Linear(input_size, hidden_size),
            nn.BatchNorm1d(hidden_size),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size, hidden_size // 2),
            nn.BatchNorm1d(hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size // 2, hidden_size // 4),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # Spike prediction head (binary classification)
        self.spike_head = nn.Sequential(
            nn.Linear(hidden_size // 4, 64),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(64, 2)
        )
        
        # Next tick uniform prediction head (binary classification)
        self.uniform_head = nn.Sequential(
            nn.Linear(hidden_size // 4, 64),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(64, 2)
        )
    
    def forward(self, x):
        shared_features = self.shared_layers(x)
        spike_pred = self.spike_head(shared_features)
        uniform_pred = self.uniform_head(shared_features)
        return spike_pred, uniform_pred

def time_based_split(data, features, train_ratio=0.6, val_ratio=0.2):
    """Proper time-based split for time series data"""
    
    total_len = len(data)
    train_end = int(total_len * train_ratio)
    val_end = int(total_len * (train_ratio + val_ratio))
    
    # Features
    X = data[features].values
    y_spike = data['is_spike'].values
    y_next_uniform = data['next_tick_uniform'].values
    
    # Time-based splits
    X_train = X[:train_end]
    X_val = X[train_end:val_end]
    X_test = X[val_end:]
    
    y_spike_train = y_spike[:train_end]
    y_spike_val = y_spike[train_end:val_end]
    y_spike_test = y_spike[val_end:]
    
    y_uniform_train = y_next_uniform[:train_end]
    y_uniform_val = y_next_uniform[train_end:val_end]
    y_uniform_test = y_next_uniform[val_end:]
    
    return (X_train, X_val, X_test, 
            y_spike_train, y_spike_val, y_spike_test,
            y_uniform_train, y_uniform_val, y_uniform_test)

def walk_forward_validation(analyzer, features, n_splits=5):
    """Walk-forward validation for time series"""
    
    data = analyzer.data
    total_len = len(data)
    
    results = []
    
    for i in range(n_splits):
        # Calculate split points
        train_start = 0
        train_end = int(total_len * (0.5 + i * 0.1))  # Growing training set
        test_start = train_end
        test_end = min(test_start + int(total_len * 0.1), total_len)  # Fixed test size
        
        if test_end - test_start < 1000:  # Minimum test size
            break
            
        print(f"\nWalk-forward fold {i+1}:")
        print(f"  Train: {train_start} to {train_end} ({train_end - train_start} samples)")
        print(f"  Test: {test_start} to {test_end} ({test_end - test_start} samples)")
        
        # Extract data
        X_train = data[features].iloc[train_start:train_end].values
        X_test = data[features].iloc[test_start:test_end].values
        
        y_spike_train = data['is_spike'].iloc[train_start:train_end].values
        y_spike_test = data['is_spike'].iloc[test_start:test_end].values
        
        y_uniform_train = data['next_tick_uniform'].iloc[train_start:train_end].values
        y_uniform_test = data['next_tick_uniform'].iloc[test_start:test_end].values
        
        # Check for spikes in sets
        train_spikes = np.sum(y_spike_train)
        test_spikes = np.sum(y_spike_test)
        
        print(f"  Train spikes: {train_spikes}, Test spikes: {test_spikes}")
        
        if train_spikes == 0:
            print(f"  Skipping fold {i+1} - no spikes in training set")
            continue
            
        results.append({
            'fold': i + 1,
            'train_spikes': train_spikes,
            'test_spikes': test_spikes,
            'train_size': train_end - train_start,
            'test_size': test_end - test_start
        })
    
    return results

def train_boom_model_fixed(csv_file, epochs=100, batch_size=1024, learning_rate=0.001):
    """Train the fixed Boom spike prediction model"""
    
    logger = setup_logging()
    logger.info("Starting FIXED Boom spike prediction model training")
    logger.info("This version eliminates data leakage and uses proper validation")
    
    start_time = time.time()
    
    # Initialize analyzer
    analyzer = BoomDataAnalyzerFixed(csv_file, logger)
    
    # Detect spikes properly (no data leakage)
    spike_count, uniform_rate = analyzer.detect_spikes_properly()
    
    # Create features without leakage
    features = analyzer.create_features_without_leakage()
    
    # Check data quality
    total_samples = len(analyzer.data)
    spike_samples = analyzer.data['is_spike'].sum()
    uniform_samples = analyzer.data['next_tick_uniform'].sum()
    
    logger.info(f"Dataset quality check:")
    logger.info(f"  Total samples: {total_samples}")
    logger.info(f"  Spike samples: {spike_samples} ({spike_samples/total_samples:.4%})")
    logger.info(f"  Uniform next tick samples: {uniform_samples} ({uniform_samples/total_samples:.4%})")
    
    if spike_samples < 10:
        logger.warning("Very few spike samples - results may not be reliable")
    
    # Time-based split (NO random shuffling!)
    logger.info("Performing time-based train/validation/test split...")
    
    splits = time_based_split(analyzer.data, features, train_ratio=0.6, val_ratio=0.2)
    (X_train, X_val, X_test, 
     y_spike_train, y_spike_val, y_spike_test,
     y_uniform_train, y_uniform_val, y_uniform_test) = splits
    
    logger.info(f"Time-based split results:")
    logger.info(f"  Train: {len(X_train)} samples, {np.sum(y_spike_train)} spikes")
    logger.info(f"  Val: {len(X_val)} samples, {np.sum(y_spike_val)} spikes")
    logger.info(f"  Test: {len(X_test)} samples, {np.sum(y_spike_test)} spikes")
    
    # Scale features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_val_scaled = scaler.transform(X_val)
    X_test_scaled = scaler.transform(X_test)
    
    # Create datasets and loaders
    train_dataset = BoomDataset(X_train_scaled, y_spike_train, y_uniform_train)
    val_dataset = BoomDataset(X_val_scaled, y_spike_val, y_uniform_val)
    test_dataset = BoomDataset(X_test_scaled, y_spike_test, y_uniform_test)
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    # Initialize model
    model = BoomSpikeModelFixed(input_size=X_train.shape[1])
    
    # Loss functions with class weights (handle imbalanced data)
    spike_weights = torch.FloatTensor([1.0, 10.0])  # Weight rare spikes higher
    uniform_weights = torch.FloatTensor([1.0, 2.0])  # Weight uniform ticks higher
    
    spike_criterion = nn.CrossEntropyLoss(weight=spike_weights)
    uniform_criterion = nn.CrossEntropyLoss(weight=uniform_weights)
    
    # Optimizer with weight decay
    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate, weight_decay=1e-5)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
    
    # Training history
    history = {
        'train_spike_loss': [], 'train_uniform_loss': [],
        'val_spike_loss': [], 'val_uniform_loss': [],
        'val_spike_acc': [], 'val_uniform_acc': []
    }
    
    logger.info(f"Starting training for {epochs} epochs")
    
    best_val_spike_acc = 0
    patience_counter = 0
    
    for epoch in range(epochs):
        epoch_start = time.time()
        
        # Training phase
        model.train()
        train_spike_loss = 0
        train_uniform_loss = 0
        
        for X_batch, y_spike_batch, y_uniform_batch in train_loader:
            optimizer.zero_grad()
            
            spike_pred, uniform_pred = model(X_batch)
            
            spike_loss = spike_criterion(spike_pred, y_spike_batch)
            uniform_loss = uniform_criterion(uniform_pred, y_uniform_batch)
            
            # Combined loss
            total_loss = spike_loss + 0.5 * uniform_loss
            
            total_loss.backward()
            optimizer.step()
            
            train_spike_loss += spike_loss.item()
            train_uniform_loss += uniform_loss.item()
        
        train_spike_loss /= len(train_loader)
        train_uniform_loss /= len(train_loader)
        
        # Validation phase
        model.eval()
        val_spike_loss = 0
        val_uniform_loss = 0
        spike_correct = 0
        uniform_correct = 0
        total_samples = 0
        
        with torch.no_grad():
            for X_batch, y_spike_batch, y_uniform_batch in val_loader:
                spike_pred, uniform_pred = model(X_batch)
                
                spike_loss = spike_criterion(spike_pred, y_spike_batch)
                uniform_loss = uniform_criterion(uniform_pred, y_uniform_batch)
                
                val_spike_loss += spike_loss.item()
                val_uniform_loss += uniform_loss.item()
                
                # Calculate accuracy
                _, spike_predicted = torch.max(spike_pred.data, 1)
                _, uniform_predicted = torch.max(uniform_pred.data, 1)
                
                total_samples += y_spike_batch.size(0)
                spike_correct += (spike_predicted == y_spike_batch).sum().item()
                uniform_correct += (uniform_predicted == y_uniform_batch).sum().item()
        
        val_spike_loss /= len(val_loader)
        val_uniform_loss /= len(val_loader)
        val_spike_acc = spike_correct / total_samples
        val_uniform_acc = uniform_correct / total_samples
        
        scheduler.step(val_spike_loss)
        
        # Save history
        history['train_spike_loss'].append(train_spike_loss)
        history['train_uniform_loss'].append(train_uniform_loss)
        history['val_spike_loss'].append(val_spike_loss)
        history['val_uniform_loss'].append(val_uniform_loss)
        history['val_spike_acc'].append(val_spike_acc)
        history['val_uniform_acc'].append(val_uniform_acc)
        
        epoch_time = time.time() - epoch_start
        
        # Logging
        if epoch % 10 == 0 or epoch < 5:
            logger.info(f"Epoch {epoch+1}/{epochs}:")
            logger.info(f"  Train - Spike Loss: {train_spike_loss:.4f}, Uniform Loss: {train_uniform_loss:.4f}")
            logger.info(f"  Val - Spike Loss: {val_spike_loss:.4f}, Uniform Loss: {val_uniform_loss:.4f}")
            logger.info(f"  Val - Spike Acc: {val_spike_acc:.4f}, Uniform Acc: {val_uniform_acc:.4f}")
            logger.info(f"  Epoch Time: {epoch_time:.2f}s, LR: {optimizer.param_groups[0]['lr']:.6f}")
        
        # Early stopping
        if val_spike_acc > best_val_spike_acc:
            best_val_spike_acc = val_spike_acc
            patience_counter = 0
            
            # Save best model
            torch.save({
                'model_state_dict': model.state_dict(),
                'scaler': scaler,
                'features': features,
                'spike_count': spike_count,
                'uniform_rate': uniform_rate,
                'best_val_spike_acc': best_val_spike_acc
            }, 'boom_spike_model_fixed.pth')
            
            logger.info(f"  New best model saved! Spike Acc: {best_val_spike_acc:.4f}")
        else:
            patience_counter += 1
            
        if patience_counter >= 20:
            logger.info("Early stopping triggered")
            break
    
    # Final evaluation on test set
    logger.info("\nFinal evaluation on test set...")
    
    model.eval()
    y_true_spike = []
    y_pred_spike = []
    y_true_uniform = []
    y_pred_uniform = []
    y_prob_spike = []
    y_prob_uniform = []
    
    with torch.no_grad():
        for X_batch, y_spike_batch, y_uniform_batch in test_loader:
            spike_pred, uniform_pred = model(X_batch)
            
            # Get probabilities
            spike_probs = torch.softmax(spike_pred, dim=1)
            uniform_probs = torch.softmax(uniform_pred, dim=1)
            
            _, predicted_spike = torch.max(spike_pred, 1)
            _, predicted_uniform = torch.max(uniform_pred, 1)
            
            y_true_spike.extend(y_spike_batch.numpy())
            y_pred_spike.extend(predicted_spike.numpy())
            y_prob_spike.extend(spike_probs[:, 1].numpy())  # Probability of spike
            
            y_true_uniform.extend(y_uniform_batch.numpy())
            y_pred_uniform.extend(predicted_uniform.numpy())
            y_prob_uniform.extend(uniform_probs[:, 1].numpy())  # Probability of uniform
    
    # Comprehensive evaluation
    logger.info("\nSpike Prediction Results:")
    logger.info("\n" + classification_report(y_true_spike, y_pred_spike, 
                                            target_names=['No Spike', 'Spike']))
    
    logger.info("\nNext Tick Uniform Prediction Results:")
    logger.info("\n" + classification_report(y_true_uniform, y_pred_uniform,
                                            target_names=['Not Uniform', 'Uniform']))
    
    # AUC scores
    if len(np.unique(y_true_spike)) > 1:
        spike_auc = roc_auc_score(y_true_spike, y_prob_spike)
        logger.info(f"\nSpike Prediction AUC: {spike_auc:.4f}")
    
    if len(np.unique(y_true_uniform)) > 1:
        uniform_auc = roc_auc_score(y_true_uniform, y_prob_uniform)
        logger.info(f"Uniform Prediction AUC: {uniform_auc:.4f}")
    
    # Walk-forward validation
    logger.info("\nPerforming walk-forward validation...")
    wf_results = walk_forward_validation(analyzer, features)
    logger.info(f"Walk-forward validation completed: {len(wf_results)} folds")
    
    # Create plots
    create_fixed_training_plots(history, logger)
    
    total_time = time.time() - start_time
    logger.info(f"\nTraining completed in {total_time:.2f} seconds ({total_time/60:.1f} minutes)")
    logger.info(f"Best validation spike accuracy: {best_val_spike_acc:.4f}")
    
    return model, scaler, features, history

def create_fixed_training_plots(history, logger):
    """Create training progress plots for the fixed model"""
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # Spike loss
    ax1.plot(history['train_spike_loss'], label='Train Spike Loss', color='blue')
    ax1.plot(history['val_spike_loss'], label='Val Spike Loss', color='red')
    ax1.set_title('Spike Prediction Loss (Fixed Model)')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Uniform loss
    ax2.plot(history['train_uniform_loss'], label='Train Uniform Loss', color='blue')
    ax2.plot(history['val_uniform_loss'], label='Val Uniform Loss', color='red')
    ax2.set_title('Next Tick Uniform Prediction Loss (Fixed Model)')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Loss')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # Spike accuracy
    ax3.plot(history['val_spike_acc'], label='Val Spike Accuracy', color='green')
    ax3.set_title('Validation Spike Accuracy (Fixed Model)')
    ax3.set_xlabel('Epoch')
    ax3.set_ylabel('Accuracy')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # Uniform accuracy
    ax4.plot(history['val_uniform_acc'], label='Val Uniform Accuracy', color='orange')
    ax4.set_title('Validation Uniform Prediction Accuracy (Fixed Model)')
    ax4.set_xlabel('Epoch')
    ax4.set_ylabel('Accuracy')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('boom_fixed_training_results.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    logger.info("Fixed model training plots saved to: boom_fixed_training_results.png")

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Fixed Boom Spike Prediction Model')
    parser.add_argument('--csv_file', type=str, default='Boom_1000_Index_7days_20250620_20250627.csv')
    parser.add_argument('--epochs', type=int, default=100)
    parser.add_argument('--batch_size', type=int, default=1024)
    parser.add_argument('--learning_rate', type=float, default=0.001)
    args = parser.parse_args()
    
    model, scaler, features, history = train_boom_model_fixed(
        csv_file=args.csv_file,
        epochs=args.epochs,
        batch_size=args.batch_size,
        learning_rate=args.learning_rate
    )
