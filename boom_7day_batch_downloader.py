"""
Batch download 7 days of Boom 1000 Index.0 tick data
Downloads in chunks to avoid MT5 limitations
"""

import MetaTrader5 as mt5
import pandas as pd
from datetime import datetime, timedelta
import pytz
import os
import time

def download_boom_7days():
    """Download 7 days of Boom 1000 Index.0 tick data in batches"""
    
    # Initialize MT5
    if not mt5.initialize():
        print(f"initialize() failed, error code = {mt5.last_error()}")
        return None
    
    print("MT5 connection established")
    
    try:
        symbol = "Boom 1000 Index.0"
        
        # Check if symbol is available
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info is None:
            print(f"Symbol {symbol} not found")
            return None
        
        print(f"Symbol {symbol} found")
        
        # Set timezone to UTC
        timezone = pytz.timezone("Etc/UTC")
        
        # Set up 7-day range
        days = 1  # Currently set to 1 day
        
        print(f"Downloading 7 days of data from {start_time} to {end_time}")
        
        # Break into 6-hour chunks to avoid MT5 limitations
        chunk_hours = 6
        all_ticks = []
        
        current_time = start_time
        chunk_count = 0
        
        while current_time < end_time:
            chunk_end = min(current_time + timedelta(hours=chunk_hours), end_time)
            chunk_count += 1
            
            print(f"Downloading chunk {chunk_count}: {current_time} to {chunk_end}")
            
            # Try to download this chunk
            ticks = mt5.copy_ticks_range(symbol, current_time, chunk_end, mt5.COPY_TICKS_ALL)
            
            # Convert to DataFrame
        ticks_df = pd.DataFrame(all_ticks)
        
        # Convert time columns
        ticks_df['time'] = pd.to_datetime(ticks_df['time'], unit='s')
        ticks_df['time_msc'] = pd.to_datetime(ticks_df['time_msc'], unit='ms')
        
        # Add symbol column
        ticks_df['symbol'] = symbol
        
        # Remove duplicates based on time_msc (in case of overlapping chunks)
        ticks_df = ticks_df.drop_duplicates(subset=['time_msc']).sort_values('time_msc').reset_index(drop=True)
        
        print(f"After removing duplicates: {len(ticks_df)} ticks")
        
        # Save to CSV
        filename = f"Boom_1000_Index_7days_{start_time.strftime('%Y%m%d')}_{end_time.strftime('%Y%m%d')}.csv"
        ticks_df.to_csv(filename, index=False)
        print(f"Data saved to {filename}")
        
        print("\nData Summary:")
        print(f"Shape: {ticks_df.shape}")
        print(f"Date range: {ticks_df['time'].min()} to {ticks_df['time'].max()}")
        print(f"Duration: {ticks_df['time'].max() - ticks_df['time'].min()}")
        print(f"Average ticks per hour: {len(ticks_df) / ((ticks_df['time'].max() - ticks_df['time'].min()).total_seconds() / 3600):.0f}")
        
        print("\nSample data:")
        print(ticks_df.head())
        
        return ticks_df
        
    except Exception as e:
        print(f"Error during download: {e}")
        return None
    
    finally:
        mt5.shutdown()
        print("MT5 connection closed")

if __name__ == "__main__":
    print("Starting 7-day batch download for Boom 1000 Index.0...")
    ticks_data = download_boom_7days()
