import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score
import warnings
warnings.filterwarnings('ignore')

class ModelParameterChecker:
    def __init__(self, data_file, uniform_threshold=0.0005):
        self.data_file = data_file
        self.uniform_threshold = uniform_threshold
        self.model = None
        self.data = None
        self.features = None
        self.targets = None
        
    def load_and_prepare_data(self):
        """Load tick data and prepare features"""
        print("Loading tick data...")
        
        # Try different delimiters
        for delimiter in [',', '\t', ';']:
            try:
                self.data = pd.read_csv(self.data_file, delimiter=delimiter)
                if len(self.data.columns) > 1:
                    break
            except:
                continue
        
        print(f"Loaded {len(self.data)} tick records")
        
        # Find price column (use bid price)
        price_col = None
        for col in ['Bid', 'bid', 'Price', 'price', 'Close', 'close']:
            if col in self.data.columns:
                price_col = col
                break
        
        if price_col is None:
            numeric_cols = self.data.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) > 0:
                price_col = numeric_cols[0]
        
        self.data['price'] = pd.to_numeric(self.data[price_col], errors='coerce')
        self.data = self.data.dropna(subset=['price']).reset_index(drop=True)
        
        print(f"Clean data: {len(self.data)} records using {price_col} as price")
        
    def create_uniform_targets(self):
        """Create uniform tick targets for prediction"""
        print("Creating uniform tick targets...")
        
        # Calculate price changes
        self.data['price_change'] = self.data['price'].diff()
        self.data['price_change_pct'] = self.data['price_change'] / self.data['price'].shift(1)
        
        # Detect uniform ticks (small price changes)
        self.data['is_uniform'] = (abs(self.data['price_change_pct']) <= self.uniform_threshold).astype(int)
        
        # Create forward-looking targets - only predict next uniform tick
        self.data['next_uniform'] = self.data['is_uniform'].shift(-1)
        
        # Create price direction for uniform ticks only
        self.data['next_price_direction'] = np.where(
            self.data['price'].shift(-1) > self.data['price'], 1,
            np.where(self.data['price'].shift(-1) < self.data['price'], -1, 0)
        )
        
        print(f"Uniform tick rate: {self.data['is_uniform'].mean():.4f}")
        
    def create_features(self):
        """Create focused trading features"""
        print("Creating trading features...")
        
        # Price-based features (keep minimal for uniform tick prediction)
        for lag in [1, 2, 3]:
            self.data[f'price_lag_{lag}'] = self.data['price'].shift(lag)
            self.data[f'price_change_lag_{lag}'] = self.data['price_change'].shift(lag)
        
        # Rolling uniform behavior
        for window in [5, 10]:
            self.data[f'uniform_rate_{window}'] = self.data['is_uniform'].rolling(window).mean()
        
        # Recent uniform behavior
        for lag in [1, 2, 3]:
            self.data[f'uniform_lag_{lag}'] = self.data['is_uniform'].shift(lag)
        
        # Small volatility features
        self.data['volatility_5'] = self.data['price_change'].rolling(5).std()
        
        # Feature columns
        feature_cols = [col for col in self.data.columns if any(x in col for x in 
                       ['lag_', 'rate_', 'volatility', 'uniform_lag'])]
        
        self.features = self.data[feature_cols].copy()
        self.targets = self.data[['next_uniform', 'next_price_direction']].copy()
        
        # Remove NaN rows
        valid_mask = self.features.notna().all(axis=1) & self.targets.notna().all(axis=1)
        self.features = self.features[valid_mask]
        self.targets = self.targets[valid_mask]
        self.data = self.data[valid_mask].reset_index(drop=True)
        
        print(f"Features: {self.features.shape}")
        print(f"Feature columns: {list(self.features.columns)}")
        
    def train_uniform_model(self):
        """Train model to predict uniform ticks only"""
        print("Training uniform tick prediction model...")
        
        # Split data (70% train, 30% test)
        split_idx = int(len(self.features) * 0.7)
        
        X_train = self.features.iloc[:split_idx]
        X_test = self.features.iloc[split_idx:]
        y_train = self.targets['next_uniform'].iloc[:split_idx]
        y_test = self.targets['next_uniform'].iloc[split_idx:]
        
        # Train model
        self.model = RandomForestClassifier(n_estimators=50, random_state=42, max_depth=8)
        self.model.fit(X_train, y_train)
        
        # Evaluate
        y_pred = self.model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        
        print(f"Uniform tick prediction accuracy: {accuracy:.4f}")
        
        # Calculate model parameters
        print("\n=== MODEL PARAMETERS ===")
        print(f"Model Type: RandomForestClassifier")
        print(f"Number of Trees (n_estimators): {self.model.n_estimators}")
        print(f"Max Depth: {self.model.max_depth}")
        print(f"Number of Features: {self.model.n_features_in_}")
        print(f"Feature Names: {list(self.features.columns)}")
        
        # Calculate total parameters
        total_params = 0
        for tree in self.model.estimators_:
            # Each tree has nodes, and each node has split conditions
            tree_params = tree.tree_.node_count
            total_params += tree_params
        
        print(f"Total Tree Nodes: {total_params}")
        print(f"Training Samples: {len(X_train)}")
        print(f"Test Samples: {len(X_test)}")
        
        # Feature importance
        print(f"\n=== FEATURE IMPORTANCE ===")
        feature_importance = self.model.feature_importances_
        for i, (feature, importance) in enumerate(zip(self.features.columns, feature_importance)):
            print(f"{feature}: {importance:.4f}")
            
        return self.model

if __name__ == "__main__":
    # Initialize and run the model parameter checker
    checker = ModelParameterChecker(
        data_file="Boom_1000_Index_7days_20250620_20250627.csv",
        uniform_threshold=0.0005
    )
    
    checker.load_and_prepare_data()
    checker.create_uniform_targets()
    checker.create_features()
    model = checker.train_uniform_model()
