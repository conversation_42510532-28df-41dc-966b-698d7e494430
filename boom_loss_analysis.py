"""
BOOM 1000 LOSS ANALYSIS - DEEP DIVE INTO LOSING TRADES
=====================================================
Analyzes the 89 losing trades (0.1%) to understand:
- What features characterize losses
- When losses occur (timing patterns)
- How big losses are vs normal losses
- Market conditions during losses
- Spike patterns that fail
"""

import pandas as pd
import numpy as np
import xgboost as xgb
import pickle
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class BoomLossAnalyzer:
    def __init__(self, model_file, scaler_file, features_file, data_file):
        self.model_file = model_file
        self.scaler_file = scaler_file
        self.features_file = features_file
        self.data_file = data_file
        
        # Model components
        self.model = None
        self.scaler = None
        self.feature_names = None
        self.df = None
        self.trades_df = None
        
    def load_components(self):
        """Load model and data components"""
        print("🔧 Loading analysis components...")
        
        # Load model
        self.model = xgb.XGBClassifier()
        self.model.load_model(self.model_file)
        
        # Load scaler
        with open(self.scaler_file, 'rb') as f:
            self.scaler = pickle.load(f)
        
        # Load feature names
        with open(self.features_file, 'rb') as f:
            self.feature_names = pickle.load(f)
            
        # Load pre-computed features
        self.df = pd.read_csv("boom_features_computed_20250627_080457.csv")
        self.df['datetime'] = pd.to_datetime(self.df['timestamp'], unit='s')
        
        print(f"✅ Loaded {len(self.df)} ticks with {len(self.feature_names)} features")
        
    def generate_trades_with_features(self):
        """Generate trades with full feature data for analysis"""
        print("📊 Generating trades with feature data...")
        
        # Prepare data for prediction
        feature_data = self.df[self.feature_names].dropna()
        feature_data_scaled = self.scaler.transform(feature_data)
        
        # Get predictions
        probabilities = self.model.predict_proba(feature_data_scaled)[:, 1]
        trade_signals = probabilities >= 0.6
        
        # Generate trades with full feature data
        trades = []
        clean_df = self.df[['mid_price', 'datetime'] + self.feature_names].dropna()
        
        for i, (_, row) in enumerate(clean_df.iterrows()):
            if i >= len(trade_signals):
                break
                
            if trade_signals[i] and i < len(clean_df) - 100:
                # Entry with 1 tick delay
                entry_idx = min(i + 1, len(clean_df) - 1)
                entry_price = clean_df.iloc[entry_idx]['mid_price']
                entry_time = clean_df.iloc[entry_idx]['datetime']
                
                # Exit after 50 ticks
                exit_idx = min(i + 50, len(clean_df) - 1)
                exit_price = clean_df.iloc[exit_idx]['mid_price']
                exit_time = clean_df.iloc[exit_idx]['datetime']
                
                # P&L calculation
                price_change = entry_price - exit_price
                ticks_gained = price_change / 0.001
                pnl = ticks_gained * 0.001 * 0.20
                
                # Collect all features at entry time
                trade_data = {
                    'entry_time': entry_time,
                    'exit_time': exit_time,
                    'entry_price': entry_price,
                    'exit_price': exit_price,
                    'pnl': pnl,
                    'probability': probabilities[i],
                    'is_loss': pnl < 0,
                    'loss_magnitude': abs(pnl) if pnl < 0 else 0,
                    'signal_idx': i,
                    'entry_idx': entry_idx,
                    'exit_idx': exit_idx
                }
                
                # Add all features at the signal time
                for feature in self.feature_names:
                    trade_data[f'signal_{feature}'] = row[feature]
                
                # Add features at entry time (1 tick later)
                entry_row = clean_df.iloc[entry_idx]
                for feature in self.feature_names:
                    trade_data[f'entry_{feature}'] = entry_row[feature]
                
                # Add price movement during the trade
                trade_data['price_movement'] = exit_price - entry_price
                trade_data['max_price_during_trade'] = clean_df.iloc[entry_idx:exit_idx+1]['mid_price'].max()
                trade_data['min_price_during_trade'] = clean_df.iloc[entry_idx:exit_idx+1]['mid_price'].min()
                trade_data['max_favorable_move'] = entry_price - trade_data['min_price_during_trade']
                trade_data['max_adverse_move'] = trade_data['max_price_during_trade'] - entry_price
                
                trades.append(trade_data)
        
        self.trades_df = pd.DataFrame(trades)
        print(f"✅ Generated {len(self.trades_df)} trades")
        print(f"📉 Found {self.trades_df['is_loss'].sum()} losing trades ({self.trades_df['is_loss'].sum()/len(self.trades_df)*100:.2f}%)")
        
    def analyze_loss_characteristics(self):
        """Deep analysis of losing trade characteristics"""
        print("\n🔍 ANALYZING LOSS CHARACTERISTICS")
        print("=" * 60)
        
        losing_trades = self.trades_df[self.trades_df['is_loss']].copy()
        winning_trades = self.trades_df[~self.trades_df['is_loss']].copy()
        
        print(f"📊 LOSS STATISTICS:")
        print(f"Total losing trades: {len(losing_trades)}")
        print(f"Average loss: ${losing_trades['pnl'].mean():.4f}")
        print(f"Median loss: ${losing_trades['pnl'].median():.4f}")
        print(f"Largest loss: ${losing_trades['pnl'].min():.4f}")
        print(f"Smallest loss: ${losing_trades['pnl'].max():.4f}")
        
        # Loss magnitude distribution
        loss_ranges = [
            (0, 0.001, "Tiny losses (0-0.001)"),
            (0.001, 0.01, "Small losses (0.001-0.01)"),
            (0.01, 0.1, "Medium losses (0.01-0.1)"),
            (0.1, 1.0, "Large losses (0.1-1.0)"),
            (1.0, 10.0, "Huge losses (1.0-10.0)")
        ]
        
        print(f"\n📈 LOSS MAGNITUDE DISTRIBUTION:")
        for min_loss, max_loss, label in loss_ranges:
            count = ((losing_trades['loss_magnitude'] >= min_loss) & 
                    (losing_trades['loss_magnitude'] < max_loss)).sum()
            if count > 0:
                avg_loss = losing_trades[(losing_trades['loss_magnitude'] >= min_loss) & 
                                       (losing_trades['loss_magnitude'] < max_loss)]['pnl'].mean()
                print(f"{label}: {count} trades (avg: ${avg_loss:.4f})")
        
        return losing_trades, winning_trades
    
    def analyze_loss_features(self, losing_trades, winning_trades):
        """Compare features between losing and winning trades"""
        print(f"\n🎯 FEATURE ANALYSIS - LOSSES vs WINS")
        print("=" * 60)
        
        # Get numerical features only
        feature_cols = [col for col in losing_trades.columns if col.startswith('signal_')]
        
        significant_diffs = []
        
        for feature in feature_cols:
            loss_mean = losing_trades[feature].mean()
            win_mean = winning_trades[feature].mean()
            
            # Skip if both are NaN or very small values
            if pd.isna(loss_mean) or pd.isna(win_mean) or abs(win_mean) < 1e-10:
                continue
                
            # Calculate percentage difference
            if abs(win_mean) > 1e-10:
                pct_diff = ((loss_mean - win_mean) / abs(win_mean)) * 100
            else:
                pct_diff = 0
            
            # Only show significant differences
            if abs(pct_diff) > 5:  # More than 5% difference
                feature_name = feature.replace('signal_', '')
                significant_diffs.append({
                    'feature': feature_name,
                    'loss_mean': loss_mean,
                    'win_mean': win_mean,
                    'pct_diff': pct_diff,
                    'abs_diff': abs(pct_diff)
                })
        
        # Sort by absolute percentage difference
        significant_diffs.sort(key=lambda x: x['abs_diff'], reverse=True)
        
        print(f"🔥 TOP FEATURES THAT DIFFER IN LOSING TRADES:")
        print(f"{'Feature':<25} {'Loss Avg':<12} {'Win Avg':<12} {'% Diff':<10}")
        print("-" * 65)
        
        for diff in significant_diffs[:20]:  # Top 20
            print(f"{diff['feature']:<25} {diff['loss_mean']:<12.4f} {diff['win_mean']:<12.4f} {diff['pct_diff']:<10.1f}%")
        
        return significant_diffs
    
    def analyze_spike_patterns_in_losses(self, losing_trades):
        """Analyze spike patterns when losses occur"""
        print(f"\n🌋 SPIKE PATTERN ANALYSIS IN LOSSES")
        print("=" * 60)
        
        # Spike-related features
        spike_features = [
            'signal_is_spike', 'signal_spike_magnitude', 'signal_ticks_since_spike',
            'signal_is_double_spike', 'signal_resistance_20', 'signal_resistance_50'
        ]
        
        for feature in spike_features:
            if feature in losing_trades.columns:
                values = losing_trades[feature].dropna()
                if len(values) > 0:
                    print(f"{feature.replace('signal_', '')}:")
                    print(f"  Mean: {values.mean():.4f}")
                    print(f"  Min: {values.min():.4f}")
                    print(f"  Max: {values.max():.4f}")
                    if feature == 'signal_is_spike':
                        spike_count = values.sum()
                        print(f"  Losses during actual spikes: {spike_count}/{len(values)} ({spike_count/len(values)*100:.1f}%)")
                    print()
        
        # Analyze ticks since spike distribution
        if 'signal_ticks_since_spike' in losing_trades.columns:
            ticks_since = losing_trades['signal_ticks_since_spike'].dropna()
            print(f"📊 TICKS SINCE SPIKE DISTRIBUTION IN LOSSES:")
            ranges = [(0, 100), (100, 500), (500, 1000), (1000, 2000), (2000, 10000)]
            for min_ticks, max_ticks in ranges:
                count = ((ticks_since >= min_ticks) & (ticks_since < max_ticks)).sum()
                if count > 0:
                    pct = count / len(ticks_since) * 100
                    print(f"  {min_ticks}-{max_ticks} ticks: {count} trades ({pct:.1f}%)")
    
    def analyze_temporal_patterns(self, losing_trades):
        """Analyze when losses occur (time patterns)"""
        print(f"\n⏰ TEMPORAL ANALYSIS OF LOSSES")
        print("=" * 60)
        
        # Hour distribution
        losing_trades['hour'] = pd.to_datetime(losing_trades['entry_time']).dt.hour
        hour_dist = losing_trades['hour'].value_counts().sort_index()
        
        print(f"📅 LOSSES BY HOUR:")
        for hour, count in hour_dist.items():
            pct = count / len(losing_trades) * 100
            print(f"  Hour {hour:02d}: {count} losses ({pct:.1f}%)")
        
        # Day of week (if data spans multiple days)
        losing_trades['day_of_week'] = pd.to_datetime(losing_trades['entry_time']).dt.day_name()
        dow_dist = losing_trades['day_of_week'].value_counts()
        
        if len(dow_dist) > 1:
            print(f"\n📅 LOSSES BY DAY OF WEEK:")
            for day, count in dow_dist.items():
                pct = count / len(losing_trades) * 100
                print(f"  {day}: {count} losses ({pct:.1f}%)")
    
    def analyze_market_conditions_during_losses(self, losing_trades):
        """Analyze market conditions when losses occur"""
        print(f"\n📈 MARKET CONDITIONS DURING LOSSES")
        print("=" * 60)
        
        # Volatility analysis
        vol_features = ['signal_volatility_5', 'signal_volatility_10', 'signal_volatility_20']
        print(f"📊 VOLATILITY DURING LOSSES:")
        for vol_feature in vol_features:
            if vol_feature in losing_trades.columns:
                vol_values = losing_trades[vol_feature].dropna()
                if len(vol_values) > 0:
                    window = vol_feature.split('_')[-1]
                    print(f"  {window}-tick volatility: {vol_values.mean():.6f} (min: {vol_values.min():.6f}, max: {vol_values.max():.6f})")
        
        # Price position analysis
        pos_features = ['signal_price_position_5', 'signal_price_position_10', 'signal_price_position_20']
        print(f"\n📍 PRICE POSITION DURING LOSSES:")
        for pos_feature in pos_features:
            if pos_feature in losing_trades.columns:
                pos_values = losing_trades[pos_feature].dropna()
                if len(pos_values) > 0:
                    window = pos_feature.split('_')[-1]
                    print(f"  {window}-tick position: {pos_values.mean():.4f} (0=bottom, 1=top of range)")
        
        # RSI analysis
        rsi_features = ['signal_rsi_7', 'signal_rsi_14', 'signal_rsi_21']
        print(f"\n📈 RSI DURING LOSSES:")
        for rsi_feature in rsi_features:
            if rsi_feature in losing_trades.columns:
                rsi_values = losing_trades[rsi_feature].dropna()
                if len(rsi_values) > 0:
                    period = rsi_feature.split('_')[-1]
                    print(f"  RSI-{period}: {rsi_values.mean():.2f} (min: {rsi_values.min():.2f}, max: {rsi_values.max():.2f})")
    
    def analyze_trade_execution_during_losses(self, losing_trades):
        """Analyze what happens during the losing trades"""
        print(f"\n⚡ TRADE EXECUTION ANALYSIS FOR LOSSES")
        print("=" * 60)
        
        print(f"📊 PRICE MOVEMENT DURING LOSING TRADES:")
        print(f"Average price movement: {losing_trades['price_movement'].mean():.6f}")
        print(f"Average max favorable move: {losing_trades['max_favorable_move'].mean():.6f}")
        print(f"Average max adverse move: {losing_trades['max_adverse_move'].mean():.6f}")
        
        # Analyze how many losses could have been winners with better exits
        recoverable_losses = losing_trades[losing_trades['max_favorable_move'] > 0.001]  # 1 tick profit was possible
        print(f"\n🎯 RECOVERABLE LOSSES:")
        print(f"Trades that had >1 tick favorable move: {len(recoverable_losses)}/{len(losing_trades)} ({len(recoverable_losses)/len(losing_trades)*100:.1f}%)")
        
        if len(recoverable_losses) > 0:
            print(f"Average max favorable in recoverable: {recoverable_losses['max_favorable_move'].mean():.6f}")
            potential_profits = recoverable_losses['max_favorable_move'] * 0.001 * 0.20
            print(f"Average potential profit if perfect exit: ${potential_profits.mean():.4f}")
    
    def generate_loss_themes(self, losing_trades, significant_diffs):
        """Identify common themes in losing trades"""
        print(f"\n🎭 LOSS THEMES IDENTIFICATION")
        print("=" * 60)
        
        themes = {}
        
        # Theme 1: High volatility losses
        high_vol_threshold = losing_trades['signal_volatility_10'].quantile(0.75) if 'signal_volatility_10' in losing_trades.columns else None
        if high_vol_threshold:
            high_vol_losses = losing_trades[losing_trades['signal_volatility_10'] > high_vol_threshold]
            themes['High Volatility'] = {
                'count': len(high_vol_losses),
                'avg_loss': high_vol_losses['pnl'].mean(),
                'description': f"Losses during high volatility periods (>{high_vol_threshold:.6f})"
            }
        
        # Theme 2: Recent spike losses
        recent_spike_losses = losing_trades[losing_trades['signal_ticks_since_spike'] < 100] if 'signal_ticks_since_spike' in losing_trades.columns else pd.DataFrame()
        if not recent_spike_losses.empty:
            themes['Recent Spike'] = {
                'count': len(recent_spike_losses),
                'avg_loss': recent_spike_losses['pnl'].mean(),
                'description': "Losses occurring within 100 ticks of previous spike"
            }
        
        # Theme 3: Double spike losses
        double_spike_losses = losing_trades[losing_trades['signal_is_double_spike'] == 1] if 'signal_is_double_spike' in losing_trades.columns else pd.DataFrame()
        if not double_spike_losses.empty:
            themes['Double Spike'] = {
                'count': len(double_spike_losses),
                'avg_loss': double_spike_losses['pnl'].mean(),
                'description': "Losses during double spike patterns"
            }
        
        # Theme 4: High RSI losses
        high_rsi_losses = losing_trades[losing_trades['signal_rsi_14'] > 70] if 'signal_rsi_14' in losing_trades.columns else pd.DataFrame()
        if not high_rsi_losses.empty:
            themes['Overbought RSI'] = {
                'count': len(high_rsi_losses),
                'avg_loss': high_rsi_losses['pnl'].mean(),
                'description': "Losses when RSI > 70 (overbought)"
            }
        
        # Theme 5: Low probability losses
        low_prob_losses = losing_trades[losing_trades['probability'] < 0.70]
        themes['Low Confidence'] = {
            'count': len(low_prob_losses),
            'avg_loss': low_prob_losses['pnl'].mean(),
            'description': f"Losses with model confidence < 70%"
        }
        
        print(f"🎯 IDENTIFIED LOSS THEMES:")
        for theme_name, theme_data in themes.items():
            if theme_data['count'] > 0:
                pct_of_losses = theme_data['count'] / len(losing_trades) * 100
                print(f"\n{theme_name}:")
                print(f"  Count: {theme_data['count']} ({pct_of_losses:.1f}% of all losses)")
                print(f"  Avg Loss: ${theme_data['avg_loss']:.4f}")
                print(f"  Description: {theme_data['description']}")
        
        return themes

def main():
    """Run comprehensive loss analysis"""
    print("🔍 BOOM 1000 LOSS ANALYSIS - DEEP DIVE")
    print("=" * 60)
    
    # File paths
    model_file = "boom_comprehensive_xgboost_target_any_profit_20250627_075549.ubj"
    scaler_file = "boom_comprehensive_scaler_target_any_profit_20250627_075549.pkl"
    features_file = "boom_comprehensive_features_target_any_profit_20250627_075549.pkl"
    data_file = "Boom_1000_Index_7days_20250619_20250626.csv"
    
    # Initialize analyzer
    analyzer = BoomLossAnalyzer(model_file, scaler_file, features_file, data_file)
    
    try:
        # Load components
        analyzer.load_components()
        
        # Generate trades with full feature data
        analyzer.generate_trades_with_features()
        
        # Analyze loss characteristics
        losing_trades, winning_trades = analyzer.analyze_loss_characteristics()
        
        # Feature analysis
        significant_diffs = analyzer.analyze_loss_features(losing_trades, winning_trades)
        
        # Spike pattern analysis
        analyzer.analyze_spike_patterns_in_losses(losing_trades)
        
        # Temporal analysis
        analyzer.analyze_temporal_patterns(losing_trades)
        
        # Market conditions analysis
        analyzer.analyze_market_conditions_during_losses(losing_trades)
        
        # Trade execution analysis
        analyzer.analyze_trade_execution_during_losses(losing_trades)
        
        # Generate themes
        themes = analyzer.generate_loss_themes(losing_trades, significant_diffs)
        
        # Save detailed results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        losing_trades.to_csv(f"boom_losing_trades_analysis_{timestamp}.csv", index=False)
        print(f"\n💾 Detailed losing trades data saved to boom_losing_trades_analysis_{timestamp}.csv")
        
        print(f"\n✅ Loss analysis completed!")
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
