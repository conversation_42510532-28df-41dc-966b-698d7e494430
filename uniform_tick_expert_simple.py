#!/usr/bin/env python3
"""
Simplified Uniform Tick Expert - Working Version
A streamlined machine learning system for predicting uniform tick behavior
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime
import argparse
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'uniform_tick_expert_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SimpleUniformTickAnalyzer:
    """Simplified analyzer for uniform tick behavior"""
    
    def __init__(self, csv_file):
        self.csv_file = csv_file
        self.data = None
        self.features = None
        
    def load_data(self):
        """Load and preprocess the tick data"""
        logger.info("Loading tick data...")
        
        try:
            # Try different separators
            try:
                self.data = pd.read_csv(self.csv_file)
            except:
                self.data = pd.read_csv(self.csv_file, sep='|')
            
            logger.info(f"Loaded {len(self.data)} tick records")
            logger.info(f"Columns: {list(self.data.columns)}")
            
            # Use bid price as the main price
            if 'bid' in self.data.columns:
                self.data['price'] = self.data['bid']
            elif 'close' in self.data.columns:
                self.data['price'] = self.data['close']
            elif 'last' in self.data.columns:
                self.data['price'] = self.data['last']
            else:
                # Use the first numeric column
                numeric_cols = self.data.select_dtypes(include=[np.number]).columns
                if len(numeric_cols) > 0:
                    self.data['price'] = self.data[numeric_cols[0]]
                else:
                    raise ValueError("No suitable price column found")
            
            # Remove rows with invalid prices
            self.data = self.data[self.data['price'] > 0].copy()
            self.data.reset_index(drop=True, inplace=True)
            
            logger.info(f"Data after cleaning: {len(self.data)} records")
            
        except Exception as e:
            logger.error(f"Error loading data: {e}")
            raise
    
    def create_uniform_targets(self, thresholds=[0.02, 0.05, 0.10]):
        """Create uniform tick targets with multiple thresholds"""
        logger.info("Creating uniform tick targets...")
        
        # Calculate price changes
        self.data['price_change'] = self.data['price'].pct_change() * 100
        
        # Create uniform targets for different thresholds
        for threshold in thresholds:
            uniform_col = f'uniform_{str(threshold).replace(".", "_")}'
            self.data[uniform_col] = (abs(self.data['price_change']) <= threshold).astype(int)
            
            # Calculate and log uniform rate
            uniform_rate = self.data[uniform_col].mean()
            logger.info(f"Uniform rate at {threshold}% threshold: {uniform_rate:.4f}")
        
        # Create sequence-based targets
        for lookback in [2, 3, 5]:
            seq_col = f'uniform_seq_{lookback}'
            # Check if next N ticks are all uniform (using 0.02% threshold as base)
            base_uniform = self.data['uniform_0_02'].values
            self.data[seq_col] = 0
            
            for i in range(len(self.data) - lookback):
                if all(base_uniform[i+j] for j in range(1, lookback+1)):
                    self.data.loc[i, seq_col] = 1
            
            seq_rate = self.data[seq_col].mean()
            logger.info(f"Rate of {lookback} consecutive uniform ticks: {seq_rate:.4f}")
    
    def create_simple_features(self, lookback_window=5):
        """Create simplified feature set that won't cause NaN issues"""
        logger.info(f"Creating features with lookback window: {lookback_window}")
        
        features = []
        
        # 1. Basic price features
        self.data['price_lag_1'] = self.data['price'].shift(1)
        self.data['price_lag_2'] = self.data['price'].shift(2)
        features.extend(['price_lag_1', 'price_lag_2'])
        
        # 2. Price change features
        for lag in range(1, min(4, lookback_window)):
            col_name = f'price_change_lag_{lag}'
            self.data[col_name] = self.data['price_change'].shift(lag)
            features.append(col_name)
        
        # 3. Simple rolling statistics (small windows to avoid too many NaNs)
        for window in [3, 5]:
            if window <= lookback_window:
                # Rolling mean and std
                self.data[f'price_mean_{window}'] = self.data['price'].rolling(window).mean()
                self.data[f'price_std_{window}'] = self.data['price'].rolling(window).std()
                features.extend([f'price_mean_{window}', f'price_std_{window}'])
        
        # 4. Uniform behavior features
        for lag in range(1, min(4, lookback_window)):
            col_name = f'uniform_0_02_lag_{lag}'
            self.data[col_name] = self.data['uniform_0_02'].shift(lag)
            features.append(col_name)
        
        # 5. Simple volatility measure
        self.data['volatility_3'] = self.data['price_change'].rolling(3).std()
        features.append('volatility_3')
        
        # Drop rows with NaN values (only the first few rows should have NaNs)
        initial_len = len(self.data)
        self.data.dropna(inplace=True)
        final_len = len(self.data)
        
        logger.info(f"Features created. Data reduced from {initial_len} to {final_len} rows")
        logger.info(f"Total features: {len(features)}")
        
        self.features = features
        return features
    
    def get_feature_data(self):
        """Get the feature matrix and targets"""
        if self.features is None:
            raise ValueError("Features not created yet. Call create_simple_features() first.")
        
        # Get feature matrix
        X = self.data[self.features].values
        
        # Get targets
        targets = {}
        target_cols = [col for col in self.data.columns if col.startswith('uniform_')]
        
        for col in target_cols:
            targets[col] = self.data[col].values
        
        return X, targets

def simple_analysis(csv_file='Boom_1000_Index_7days_20250620_20250627.csv'):
    """Run a simple analysis of uniform tick behavior"""
    
    logger.info("Starting Simple Uniform Tick Analysis")
    logger.info("This is a simplified version focused on basic uniform tick prediction")
    
    try:
        # Initialize analyzer
        analyzer = SimpleUniformTickAnalyzer(csv_file)
        
        # Load and process data
        analyzer.load_data()
        analyzer.create_uniform_targets()
        features = analyzer.create_simple_features()
        
        # Get feature data
        X, targets = analyzer.get_feature_data()
        
        logger.info("\n" + "="*50)
        logger.info("ANALYSIS SUMMARY")
        logger.info("="*50)
        logger.info(f"Total samples: {len(X)}")
        logger.info(f"Number of features: {len(features)}")
        
        # Print target distribution
        for target_name, target_values in targets.items():
            positive_rate = np.mean(target_values)
            logger.info(f"{target_name}: {positive_rate:.4f} positive rate ({np.sum(target_values)} out of {len(target_values)})")
        
        # Basic statistics
        logger.info(f"\nFeature statistics:")
        logger.info(f"Features shape: {X.shape}")
        logger.info(f"Feature means: {np.mean(X, axis=0)[:5]}...")  # Show first 5
        logger.info(f"Feature stds: {np.std(X, axis=0)[:5]}...")    # Show first 5
        
        # Check for any remaining NaN values
        nan_count = np.isnan(X).sum()
        if nan_count > 0:
            logger.warning(f"Found {nan_count} NaN values in features")
        else:
            logger.info("No NaN values found in features ✓")
        
        logger.info("\nAnalysis completed successfully!")
        return analyzer, X, targets
        
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        raise

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Simple Uniform Tick Expert Analysis')
    parser.add_argument('--csv_file', 
                       default='Boom_1000_Index_7days_20250620_20250627.csv',
                       help='CSV file containing tick data')
    
    args = parser.parse_args()
    
    # Run analysis
    analyzer, X, targets = simple_analysis(args.csv_file)
    
    logger.info("Simple uniform tick analysis completed successfully!")

if __name__ == "__main__":
    main()
