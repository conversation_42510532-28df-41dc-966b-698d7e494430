"""
IMPROVED Boom 1000 Index Post-Spike Strategy Backtest
Strategy: Use TDI signals AFTER spikes for optimal entry/exit timing
Key insight: After spike, wait for favorable TDI conditions, then trade the reversion
"""

import pandas as pd
import numpy as np
from datetime import datetime

class ImprovedBoomBacktester:
    def __init__(self, csv_file, starting_capital=20):
        self.csv_file = csv_file
        self.starting_capital = starting_capital
        self.current_capital = starting_capital
        
        # Boom 1000 Index specifications
        self.min_volume = 0.20  # lots
        self.max_volume = 50.0  # lots
        self.volume_limit = 120.0  # lots for multiple entries
        self.tick_size = 0.001  # 0.001 for Boom 1000
        self.tick_value = 0.001  # $0.001 per tick per lot
        self.spread = 0.0  # Zero spread as specified
        
        # Strategy parameters
        self.post_spike_window = 100  # ticks to analyze after spike
        
        # Results tracking
        self.trades = []
        self.equity_curve = []
        self.df = None
        self.spikes = []
        
    def load_and_parse_data(self):
        """Load and parse the tick data"""
        print("Loading tick data for improved backtesting...")
        
        raw_df = pd.read_csv(self.csv_file)
        parsed_data = []
        
        for _, row in raw_df.iterrows():
            try:
                tuple_str = row['0']
                if tuple_str.startswith('(') and tuple_str.endswith(')'):
                    values = tuple_str[1:-1].split(', ')
                    
                    tick_data = {
                        'timestamp': int(values[0]),
                        'bid': float(values[1]),
                        'ask': float(values[2]),
                        'last': float(values[3]),
                        'volume': int(values[4]),
                        'timestamp_msc': int(values[5]),
                        'flags': int(values[6]),
                        'volume_real': float(values[7]),
                        'symbol': row['symbol']
                    }
                    parsed_data.append(tick_data)
            except:
                continue
        
        self.df = pd.DataFrame(parsed_data)
        self.df['datetime'] = pd.to_datetime(self.df['timestamp'], unit='s')
        self.df['datetime_msc'] = pd.to_datetime(self.df['timestamp_msc'], unit='ms')
        self.df['mid_price'] = (self.df['bid'] + self.df['ask']) / 2
        self.df = self.df.sort_values('timestamp_msc').reset_index(drop=True)
        
        print(f"Loaded {len(self.df)} ticks for backtesting")
        return self.df
    
    def detect_spikes(self):
        """Detect spikes using the same enhanced method"""
        print("Detecting spikes for backtesting...")
        
        # Calculate price changes
        self.df['price_change'] = self.df['mid_price'].diff()
        self.df['price_change_pct'] = (self.df['price_change'] / self.df['mid_price'].shift(1)) * 100
        
        # Rolling statistics
        window = 100
        self.df['rolling_mean'] = self.df['price_change'].rolling(window=window).mean()
        self.df['rolling_std'] = self.df['price_change'].rolling(window=window).std()
        
        # Multiple spike detection methods
        pct_spikes = self.df[
            (self.df['price_change_pct'] > 0.1) & 
            (self.df['price_change'] > 0)
        ].index.tolist()
        
        std_threshold = 2.5
        std_spikes = self.df[
            (self.df['price_change'] > self.df['rolling_mean'] + std_threshold * self.df['rolling_std']) &
            (self.df['price_change'] > 0)
        ].index.tolist()
        
        abs_threshold = 0.5
        abs_spikes = self.df[
            (self.df['price_change'] > abs_threshold)
        ].index.tolist()
        
        # Combine and filter
        all_spike_candidates = list(set(pct_spikes + std_spikes + abs_spikes))
        all_spike_candidates.sort()
        
        filtered_spikes = []
        min_distance = 5
        
        for spike_idx in all_spike_candidates:
            if not filtered_spikes or spike_idx - filtered_spikes[-1] >= min_distance:
                filtered_spikes.append(spike_idx)
        
        self.spikes = filtered_spikes
        self.df['is_spike'] = False
        self.df.loc[self.spikes, 'is_spike'] = True
        
        print(f"Detected {len(self.spikes)} spikes for backtesting")
        return self.spikes
    
    def calculate_tdi(self, period=13):
        """Calculate TDI indicators for the entire dataset"""
        print("Calculating TDI indicators...")
        
        # Calculate RSI
        delta = self.df['mid_price'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        # TDI components
        self.df['rsi'] = rsi
        self.df['rsi_ma'] = rsi.rolling(window=period).mean()
        self.df['rsi_signal'] = rsi.rolling(window=7).mean()
        
        # Bollinger Bands on RSI
        rsi_std = rsi.rolling(window=period).std()
        self.df['rsi_upper_bb'] = self.df['rsi_ma'] + (2 * rsi_std)
        self.df['rsi_lower_bb'] = self.df['rsi_ma'] - (2 * rsi_std)
        
        # Additional momentum indicators
        self.df['momentum'] = self.df['mid_price'] - self.df['mid_price'].shift(10)
        self.df['rate_of_change'] = ((self.df['mid_price'] / self.df['mid_price'].shift(10)) - 1) * 100
        
        return True
    
    def calculate_position_size(self):
        """Calculate position size based on current capital"""
        # More conservative position sizing
        risk_per_trade = min(self.current_capital * 0.01, 1.0)  # Risk max $1 or 1% of capital
        
        # Estimate risk per lot (10 ticks max loss)
        risk_per_lot = 10 * self.tick_value  # $0.01 per lot
        
        if risk_per_lot > 0 and risk_per_trade > 0:
            optimal_lots = risk_per_trade / risk_per_lot
        else:
            optimal_lots = self.min_volume
        
        # Apply constraints
        optimal_lots = max(self.min_volume, min(optimal_lots, self.max_volume))
        optimal_lots = min(optimal_lots, self.volume_limit)
        
        return round(optimal_lots, 2)
    
    def execute_improved_strategy(self):
        """Execute improved post-spike strategy using TDI signals"""
        print("Executing improved post-spike strategy...")
        
        self.current_capital = self.starting_capital
        self.equity_curve = [self.current_capital]
        
        for spike_idx in self.spikes:
            # Check if we have enough data after the spike
            if spike_idx + self.post_spike_window >= len(self.df):
                continue
            
            # Analyze the post-spike window
            start_idx = spike_idx + 5  # Wait a few ticks after spike
            end_idx = min(spike_idx + self.post_spike_window, len(self.df) - 1)
            
            post_spike_data = self.df.iloc[start_idx:end_idx + 1].copy()
            
            if len(post_spike_data) < 20:  # Need sufficient data
                continue
            
            # Strategy: Trade the reversion after spike
            # Look for oversold conditions (RSI < 30) to buy, or overbought (RSI > 70) to sell
            
            # Find favorable entry points
            oversold_signals = post_spike_data[
                (post_spike_data['rsi'] < 30) & 
                (post_spike_data['rsi'].notna())
            ]
            
            overbought_signals = post_spike_data[
                (post_spike_data['rsi'] > 70) & 
                (post_spike_data['rsi'].notna())
            ]
            
            # Trade the most favorable signal
            if len(oversold_signals) > 0:
                # Buy signal (expecting bounce from oversold)
                entry_idx = oversold_signals.index[0]
                direction = 'buy'
                
                # Find exit - either RSI crosses back above 50 or end of window
                exit_candidates = post_spike_data[
                    (post_spike_data.index > entry_idx) &
                    (post_spike_data['rsi'] > 50)
                ]
                
                if len(exit_candidates) > 0:
                    exit_idx = exit_candidates.index[0]
                else:
                    exit_idx = post_spike_data.index[-1]  # Exit at end of window
                    
            elif len(overbought_signals) > 0:
                # Sell signal (expecting decline from overbought)
                entry_idx = overbought_signals.index[0]
                direction = 'sell'
                
                # Find exit - either RSI crosses back below 50 or end of window
                exit_candidates = post_spike_data[
                    (post_spike_data.index > entry_idx) &
                    (post_spike_data['rsi'] < 50)
                ]
                
                if len(exit_candidates) > 0:
                    exit_idx = exit_candidates.index[0]
                else:
                    exit_idx = post_spike_data.index[-1]  # Exit at end of window
            else:
                # No clear signal, skip this spike
                continue
            
            # Execute the trade
            entry_price = self.df.iloc[entry_idx]['mid_price']
            exit_price = self.df.iloc[exit_idx]['mid_price']
            position_size = self.calculate_position_size()
            
            # Calculate P&L based on direction
            if direction == 'buy':
                price_change = exit_price - entry_price
            else:  # sell
                price_change = entry_price - exit_price
            
            ticks_gained = price_change / self.tick_size
            pnl = ticks_gained * self.tick_value * position_size
            
            # Update capital
            self.current_capital += pnl
            
            # Record trade
            trade = {
                'trade_number': len(self.trades) + 1,
                'spike_idx': spike_idx,
                'direction': direction,
                'entry_time': self.df.iloc[entry_idx]['datetime'],
                'exit_time': self.df.iloc[exit_idx]['datetime'],
                'entry_price': entry_price,
                'exit_price': exit_price,
                'position_size': position_size,
                'price_change': price_change,
                'ticks_gained': ticks_gained,
                'pnl': pnl,
                'capital_after': self.current_capital,
                'entry_rsi': self.df.iloc[entry_idx]['rsi'],
                'exit_rsi': self.df.iloc[exit_idx]['rsi'],
                'duration_ticks': exit_idx - entry_idx
            }
            
            self.trades.append(trade)
            self.equity_curve.append(self.current_capital)
            
            # Print progress every 10 trades
            if len(self.trades) % 10 == 0:
                print(f"Completed {len(self.trades)} trades, Capital: ${self.current_capital:.2f}")
        
        print(f"Improved backtesting completed: {len(self.trades)} trades executed")
        return self.trades
    
    def calculate_statistics(self):
        """Calculate comprehensive backtest statistics"""
        if not self.trades:
            print("No trades to analyze")
            return None
        
        trades_df = pd.DataFrame(self.trades)
        
        # Basic statistics
        total_trades = len(trades_df)
        winning_trades = len(trades_df[trades_df['pnl'] > 0])
        losing_trades = len(trades_df[trades_df['pnl'] < 0])
        
        win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
        
        total_pnl = trades_df['pnl'].sum()
        total_return = ((self.current_capital - self.starting_capital) / self.starting_capital) * 100
        
        avg_win = trades_df[trades_df['pnl'] > 0]['pnl'].mean() if winning_trades > 0 else 0
        avg_loss = trades_df[trades_df['pnl'] < 0]['pnl'].mean() if losing_trades > 0 else 0
        
        max_win = trades_df['pnl'].max()
        max_loss = trades_df['pnl'].min()
        
        # Calculate drawdown
        equity_series = pd.Series(self.equity_curve)
        running_max = equity_series.cummax()
        drawdown = equity_series - running_max
        max_drawdown = drawdown.min()
        max_drawdown_pct = (max_drawdown / running_max.loc[drawdown.idxmin()]) * 100 if len(running_max) > 0 else 0
        
        # Sharpe ratio (simplified)
        returns = trades_df['pnl'] / self.starting_capital
        sharpe_ratio = returns.mean() / returns.std() if returns.std() > 0 else 0
        
        # Trade direction analysis
        buy_trades = trades_df[trades_df['direction'] == 'buy']
        sell_trades = trades_df[trades_df['direction'] == 'sell']
        
        stats = {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'total_pnl': total_pnl,
            'total_return': total_return,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'max_win': max_win,
            'max_loss': max_loss,
            'max_drawdown': max_drawdown,
            'max_drawdown_pct': max_drawdown_pct,
            'sharpe_ratio': sharpe_ratio,
            'starting_capital': self.starting_capital,
            'final_capital': self.current_capital,
            'buy_trades': len(buy_trades),
            'sell_trades': len(sell_trades),
            'buy_win_rate': (len(buy_trades[buy_trades['pnl'] > 0]) / len(buy_trades) * 100) if len(buy_trades) > 0 else 0,
            'sell_win_rate': (len(sell_trades[sell_trades['pnl'] > 0]) / len(sell_trades) * 100) if len(sell_trades) > 0 else 0
        }
        
        return stats
    
    def print_results(self):
        """Print comprehensive backtest results"""
        stats = self.calculate_statistics()
        if not stats:
            return
        
        print("\n" + "="*80)
        print("IMPROVED BOOM 1000 INDEX POST-SPIKE TDI STRATEGY BACKTEST")
        print("="*80)
        
        print(f"\n📊 STRATEGY OVERVIEW:")
        print(f"{'Strategy:':<25} Post-spike TDI reversion signals")
        print(f"{'Starting Capital:':<25} ${stats['starting_capital']:.2f}")
        print(f"{'Final Capital:':<25} ${stats['final_capital']:.2f}")
        print(f"{'Total Return:':<25} {stats['total_return']:.2f}%")
        print(f"{'Total P&L:':<25} ${stats['total_pnl']:.4f}")
        
        print(f"\n📈 TRADE STATISTICS:")
        print(f"{'Total Trades:':<25} {stats['total_trades']}")
        print(f"{'Winning Trades:':<25} {stats['winning_trades']}")
        print(f"{'Losing Trades:':<25} {stats['losing_trades']}")
        print(f"{'Win Rate:':<25} {stats['win_rate']:.2f}%")
        print(f"{'Buy Trades:':<25} {stats['buy_trades']} (Win Rate: {stats['buy_win_rate']:.1f}%)")
        print(f"{'Sell Trades:':<25} {stats['sell_trades']} (Win Rate: {stats['sell_win_rate']:.1f}%)")
        
        print(f"\n💰 P&L ANALYSIS:")
        print(f"{'Average Win:':<25} ${stats['avg_win']:.4f}")
        print(f"{'Average Loss:':<25} ${stats['avg_loss']:.4f}")
        print(f"{'Best Trade:':<25} ${stats['max_win']:.4f}")
        print(f"{'Worst Trade:':<25} ${stats['max_loss']:.4f}")
        
        print(f"\n📉 RISK METRICS:")
        print(f"{'Max Drawdown:':<25} ${stats['max_drawdown']:.4f}")
        print(f"{'Max Drawdown %:':<25} {stats['max_drawdown_pct']:.2f}%")
        print(f"{'Sharpe Ratio:':<25} {stats['sharpe_ratio']:.4f}")
        
        # Sample trades
        if self.trades:
            print(f"\n🔍 SAMPLE TRADES (First 10):")
            for trade in self.trades[:10]:
                print(f"Trade {trade['trade_number']} ({trade['direction'].upper()}): "
                      f"{trade['entry_time'].strftime('%m-%d %H:%M:%S')} → "
                      f"{trade['exit_time'].strftime('%H:%M:%S')} | "
                      f"RSI: {trade['entry_rsi']:.1f}→{trade['exit_rsi']:.1f} | "
                      f"P&L: ${trade['pnl']:.4f} | "
                      f"Capital: ${trade['capital_after']:.2f}")
        
        print("\n" + "="*80)
        
        return stats

def main():
    """Run the improved backtest"""
    print("Starting IMPROVED Boom 1000 Index Post-Spike TDI Strategy Backtest")
    print("Strategy: Use TDI signals after spikes for reversion trading")
    print("Starting Capital: $20")
    
    # Initialize backtester
    csv_file = "C:\\Users\\<USER>\\META\\Boom_1000_Index_7days_20250619_20250626.csv"
    backtester = ImprovedBoomBacktester(csv_file, starting_capital=20)
    
    # Load data
    df = backtester.load_and_parse_data()
    
    # Detect spikes
    spikes = backtester.detect_spikes()
    
    # Calculate TDI
    backtester.calculate_tdi()
    
    # Execute improved strategy
    trades = backtester.execute_improved_strategy()
    
    # Print results
    results = backtester.print_results()
    
    return backtester, results

if __name__ == "__main__":
    backtester, results = main()
