import pandas as pd
import numpy as np
import pickle
import os

# Load the best model
MODEL_PATH = "xgboost_best_model_qtrend_smote_20250609_214811.pkl"
if not os.path.isfile(MODEL_PATH):
    print(f"Model file not found: {MODEL_PATH}")
    print("Available pkl files:")
    for file in os.listdir("."):
        if file.endswith('.pkl'):
            print(f"  {file}")
    exit()

with open(MODEL_PATH, 'rb') as f:
    best_model = pickle.load(f)

print(f"Loaded model: {MODEL_PATH}")

# Load and prepare data (same as training script)
DATA_PATH = "stpRNG_1min.csv"
df = pd.read_csv(DATA_PATH)
df = df.dropna().reset_index(drop=True)

# Parameters
p = 200  # Trend period
atr_p = 3  # ATR Period  
mult = 1.6  # ATR Multiplier
mode = "Type A"  # Signal mode

# Source selection
df["src"] = df["close"]

# Calculate ATR manually
def calculate_atr(df, period):
    df["tr1"] = df["high"] - df["low"]
    df["tr2"] = abs(df["high"] - df["close"].shift(1))
    df["tr3"] = abs(df["low"] - df["close"].shift(1))
    df["tr"] = df[["tr1", "tr2", "tr3"]].max(axis=1)
    df["atr"] = df["tr"].rolling(window=period).mean()
    return df["atr"]

# Q-Trend Calculations
df["h"] = df["src"].rolling(window=p).max()
df["l"] = df["src"].rolling(window=p).min()
df["d"] = df["h"] - df["l"]
df["m"] = (df["h"] + df["l"]) / 2

df["atr"] = calculate_atr(df, atr_p).shift(1)
df["epsilon"] = mult * df["atr"]

# Initialize columns
df["trend"] = np.nan
df["last_signal"] = ""
df["signal"] = ""
df["change_up"] = False
df["change_down"] = False

# Process each bar for Q-Trend signals
for i in range(len(df)):
    if i < p:
        df.at[i, "trend"] = df.at[i, "m"] if not pd.isna(df.at[i, "m"]) else np.nan
        df.at[i, "last_signal"] = ""
        continue
    
    src = df.at[i, "src"]
    prev_trend = df.at[i-1, "trend"] if i > 0 and not pd.isna(df.at[i-1, "trend"]) else df.at[i, "m"]
    epsilon = df.at[i, "epsilon"]
    
    if pd.isna(epsilon):
        df.at[i, "trend"] = prev_trend
        df.at[i, "last_signal"] = df.at[i-1, "last_signal"] if i > 0 else ""
        continue
    
    # Type A mode logic
    change_up = src > prev_trend + epsilon
    change_down = src < prev_trend - epsilon
    
    df.at[i, "change_up"] = change_up
    df.at[i, "change_down"] = change_down
    
    # Strong signals logic
    h = df.at[i, "h"]
    l = df.at[i, "l"]
    d = df.at[i, "d"]
    
    sb = False
    ss = False
    for j in range(max(0, i-4), i+1):
        if j < len(df):
            open_price = df.at[j, "open"]
            if open_price < l + d / 8 and open_price >= l:
                sb = True
            if open_price > h - d / 8 and open_price <= h:
                ss = True
    
    strong_buy = sb
    strong_sell = ss
    
    # Update trend line
    if change_up or change_down:
        if change_up:
            new_trend = prev_trend + epsilon
        elif change_down:
            new_trend = prev_trend - epsilon
        else:
            new_trend = prev_trend
    else:
        new_trend = prev_trend
    
    df.at[i, "trend"] = new_trend
    
    # Signal logic
    prev_last_signal = df.at[i-1, "last_signal"] if i > 0 else ""
    
    if change_up and prev_last_signal != "B":
        if strong_buy:
            signal = "strong_buy"
        else:
            signal = "buy"
        df.at[i, "last_signal"] = "B"
    elif change_down and prev_last_signal != "S":
        if strong_sell:
            signal = "strong_sell"
        else:
            signal = "sell"
        df.at[i, "last_signal"] = "S"
    else:
        signal = ""
        df.at[i, "last_signal"] = prev_last_signal
    
    df.at[i, "signal"] = signal

# Add technical features (same as training)
def add_technical_features(df):
    df['price_vs_trend'] = (df['close'] - df['trend']) / df['trend']
    df['price_vs_high'] = (df['close'] - df['h']) / df['h']
    df['price_vs_low'] = (df['close'] - df['l']) / df['l']
    df['price_position'] = (df['close'] - df['l']) / (df['h'] - df['l'])
    
    df['atr_normalized'] = df['atr'] / df['close']
    df['epsilon_normalized'] = df['epsilon'] / df['close']
    df['range_normalized'] = (df['high'] - df['low']) / df['close']
    
    for period in [5, 10, 20]:
        df[f'return_{period}'] = df['close'].pct_change(period)
        df[f'volatility_{period}'] = df['close'].rolling(period).std() / df['close']
    
    for period in [10, 20, 50]:
        df[f'ma_{period}'] = df['close'].rolling(period).mean()
        df[f'price_vs_ma_{period}'] = (df['close'] - df[f'ma_{period}']) / df[f'ma_{period}']
    
    delta = df['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    
    if 'volume' in df.columns:
        df['volume_ma'] = df['volume'].rolling(20).mean()
        df['volume_ratio'] = df['volume'] / df['volume_ma']
    
    return df

df = add_technical_features(df)

# Feature columns (same as training)
feature_columns = [
    'price_vs_trend', 'price_vs_high', 'price_vs_low', 'price_position',
    'atr_normalized', 'epsilon_normalized', 'range_normalized',
    'return_5', 'return_10', 'return_20',
    'volatility_5', 'volatility_10', 'volatility_20',
    'price_vs_ma_10', 'price_vs_ma_20', 'price_vs_ma_50',
    'rsi', 'change_up', 'change_down'
]

if 'volume' in df.columns:
    feature_columns.extend(['volume_ratio'])

# Prepare clean data
valid_idx = df.dropna(subset=feature_columns).index
df_clean = df.loc[valid_idx].copy()

print(f"Clean data: {len(df_clean)} rows")

# Get model predictions
X = df_clean[feature_columns]
ml_predictions = best_model.predict_proba(X)[:, 1]
df_clean['ml_prediction'] = ml_predictions

# Create Q-Trend signals for comparison
df_clean['qtrend_signal'] = (df_clean['signal'] != "").astype(int)

print(f"\n=== SIGNAL COMPARISON ===")
print(f"Q-Trend signals generated: {df_clean['qtrend_signal'].sum()}")

# Test different probability thresholds for ML model
thresholds = [0.3, 0.4, 0.5, 0.6, 0.7, 0.8]

print(f"\nML signals at different thresholds:")
for threshold in thresholds:
    ml_signals = (df_clean['ml_prediction'] > threshold).sum()
    print(f"  Threshold {threshold}: {ml_signals} signals")

# Detailed trading simulation
def run_trading_simulation(df, signal_column, signal_name, threshold=None):
    """Run trading simulation and return detailed results"""
    
    balance = 100_000
    risk_per_trade = 0.004
    position = 0
    entry_price = 0
    entry_index = 0
    trades = []
    
    for i in range(1, len(df)):
        current_row = df.iloc[i]
        price = current_row['open']
        
        # Determine signal
        if threshold is not None:
            # ML model with threshold
            signal = current_row['ml_prediction'] > threshold
            prev_signal = df.iloc[i-1]['ml_prediction'] > threshold if i > 0 else False
        else:
            # Q-Trend signals
            signal = current_row[signal_column] == 1
            prev_signal = df.iloc[i-1][signal_column] == 1 if i > 0 else False
        
        # Exit position when signal changes
        if position != 0 and signal != prev_signal:
            pnl = 0
            if position == 1:  # Long position
                pnl = (price - entry_price) / entry_price
            elif position == -1:  # Short position  
                pnl = (entry_price - price) / entry_price
            
            # Record trade
            trades.append({
                'entry_index': entry_index,
                'exit_index': i,
                'entry_price': entry_price,
                'exit_price': price,
                'position': 'long' if position == 1 else 'short',
                'duration': i - entry_index,
                'pnl_pct': pnl * 100,
                'result': 'win' if pnl > 0 else 'loss'
            })
            
            # Update balance
            if pnl > 0:
                balance *= (1 + risk_per_trade)
            else:
                balance *= (1 - risk_per_trade)
            
            position = 0
        
        # Enter new position on signal
        if position == 0 and signal and not prev_signal:
            position = 1  # Always go long for simplicity
            entry_price = price
            entry_index = i
    
    # Calculate results
    total_trades = len(trades)
    wins = len([t for t in trades if t['result'] == 'win'])
    losses = total_trades - wins
    win_rate = wins / total_trades * 100 if total_trades > 0 else 0
    total_return = (balance - 100_000) / 100_000 * 100
    
    return {
        'signal_name': signal_name,
        'total_trades': total_trades,
        'wins': wins,
        'losses': losses,
        'win_rate': win_rate,
        'final_balance': balance,
        'total_return': total_return,
        'trades': trades
    }

# Run simulations
print(f"\n=== TRADING SIMULATION RESULTS ===")

# Q-Trend baseline
qtrend_results = run_trading_simulation(df_clean, 'qtrend_signal', 'Q-Trend')
print(f"\nQ-Trend Strategy:")
print(f"  Trades: {qtrend_results['total_trades']}")
print(f"  Win Rate: {qtrend_results['win_rate']:.2f}%")
print(f"  Return: {qtrend_results['total_return']:+.2f}%")

# ML model at different thresholds
best_ml_result = None
best_ml_return = -float('inf')

for threshold in thresholds:
    ml_results = run_trading_simulation(df_clean, 'ml_prediction', f'ML-{threshold}', threshold)
    print(f"\nML Model (threshold {threshold}):")
    print(f"  Trades: {ml_results['total_trades']}")
    print(f"  Win Rate: {ml_results['win_rate']:.2f}%")
    print(f"  Return: {ml_results['total_return']:+.2f}%")
    
    if ml_results['total_return'] > best_ml_return:
        best_ml_return = ml_results['total_return']
        best_ml_result = ml_results

print(f"\n=== BEST PERFORMANCE COMPARISON ===")
print(f"Q-Trend: {qtrend_results['total_trades']} trades, {qtrend_results['total_return']:+.2f}% return")
if best_ml_result:
    print(f"Best ML: {best_ml_result['total_trades']} trades, {best_ml_result['total_return']:+.2f}% return ({best_ml_result['signal_name']})")

# Save detailed results
results_summary = {
    'qtrend': qtrend_results,
    'best_ml': best_ml_result
}

# Export trade details
if qtrend_results['trades']:
    qtrend_trades_df = pd.DataFrame(qtrend_results['trades'])
    qtrend_trades_df.to_csv('qtrend_strategy_trades.csv', index=False)
    print(f"\nQ-Trend trades saved to 'qtrend_strategy_trades.csv'")

if best_ml_result and best_ml_result['trades']:
    ml_trades_df = pd.DataFrame(best_ml_result['trades'])
    ml_trades_df.to_csv('ml_strategy_trades.csv', index=False)
    print(f"ML trades saved to 'ml_strategy_trades.csv'")

print(f"\n=== SIGNAL ANALYSIS ===")
print(f"Total Q-Trend signals in dataset: {df_clean['qtrend_signal'].sum()}")
print(f"ML model signals at best threshold: {(df_clean['ml_prediction'] > 0.5).sum()}")  # Assuming 0.5 was decent

# Show some example predictions
print(f"\n=== SAMPLE ML PREDICTIONS ===")
sample_data = df_clean[['ml_prediction', 'qtrend_signal', 'signal']].tail(20)
sample_data['ml_signal_0.5'] = (sample_data['ml_prediction'] > 0.5).astype(int)
print(sample_data)
