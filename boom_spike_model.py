import pandas as pd
import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt
import time
import logging
from datetime import datetime
import argparse

# Setup logging
def setup_logging():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"boom_spike_training_{timestamp}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

class BoomDataAnalyzer:
    """Comprehensive Boom data analyzer for spike detection and uniform tick counting"""
    
    def __init__(self, csv_file, logger=None):
        self.logger = logger or logging.getLogger(__name__)
        self.logger.info("Loading Boom data...")
        
        # Load data with proper column handling
        self.data = pd.read_csv(csv_file)
        self.logger.info(f"Loaded {len(self.data)} tick records")
        
        # Parse time column
        self.data['datetime'] = pd.to_datetime(self.data['time'])
        self.data = self.data.sort_values('datetime').reset_index(drop=True)
        
        # Use bid price as main price
        self.data['price'] = self.data['bid']
        
    def detect_spikes_and_uniform_periods(self, spike_threshold_pct=0.3, uniform_threshold=0.05):
        """Detect spikes and count uniform ticks after spikes"""
        
        self.logger.info("Detecting spikes and uniform periods...")
        
        # Calculate price changes
        self.data['price_change'] = self.data['price'].diff()
        self.data['price_change_pct'] = (self.data['price_change'] / self.data['price'].shift(1)) * 100
        
        # Detect spikes (both positive and negative)
        self.data['is_spike'] = (
            (self.data['price_change_pct'] > spike_threshold_pct) | 
            (self.data['price_change_pct'] < -spike_threshold_pct)
        ).astype(int)
        
        # Count uniform ticks after each spike
        self.data['uniform_ticks_after_spike'] = 0
        
        spike_indices = self.data[self.data['is_spike'] == 1].index
        
        for spike_idx in spike_indices:
            uniform_count = 0
            idx = spike_idx + 1
            
            while idx < len(self.data):
                if abs(self.data.loc[idx, 'price_change_pct']) <= uniform_threshold:
                    uniform_count += 1
                    idx += 1
                else:
                    break
                    
                # Cap at reasonable limit
                if uniform_count >= 100:
                    break
            
            self.data.loc[spike_idx, 'uniform_ticks_after_spike'] = uniform_count
        
        spike_count = self.data['is_spike'].sum()
        avg_uniform_ticks = self.data[self.data['is_spike'] == 1]['uniform_ticks_after_spike'].mean()
        
        self.logger.info(f"Detected {spike_count} spikes")
        self.logger.info(f"Average uniform ticks after spike: {avg_uniform_ticks:.2f}")
        
        return spike_count, avg_uniform_ticks
    
    def create_features(self, lookback_window=20):
        """Create features for spike prediction"""
        
        self.logger.info(f"Creating features with lookback window of {lookback_window}")
        
        features = []
        
        # Price-based features
        for i in range(1, lookback_window + 1):
            self.data[f'price_lag_{i}'] = self.data['price'].shift(i)
            self.data[f'change_lag_{i}'] = self.data['price_change'].shift(i)
            self.data[f'change_pct_lag_{i}'] = self.data['price_change_pct'].shift(i)
            features.extend([f'price_lag_{i}', f'change_lag_{i}', f'change_pct_lag_{i}'])
        
        # Moving averages
        for window in [5, 10, 20]:
            self.data[f'ma_{window}'] = self.data['price'].rolling(window=window).mean()
            self.data[f'price_vs_ma_{window}'] = self.data['price'] - self.data[f'ma_{window}']
            features.extend([f'ma_{window}', f'price_vs_ma_{window}'])\n        \n        # Volatility features\n        for window in [5, 10, 20]:\n            self.data[f'volatility_{window}'] = self.data['price_change_pct'].rolling(window=window).std()\n            features.append(f'volatility_{window}')\n        \n        # Momentum features\n        for window in [3, 5, 10]:\n            self.data[f'momentum_{window}'] = self.data['price'] / self.data['price'].shift(window) - 1\n            features.append(f'momentum_{window}')\n        \n        # Time-based features\n        self.data['hour'] = self.data['datetime'].dt.hour\n        self.data['minute'] = self.data['datetime'].dt.minute\n        self.data['day_of_week'] = self.data['datetime'].dt.dayofweek\n        features.extend(['hour', 'minute', 'day_of_week'])\n        \n        # Recent spike activity\n        for window in [10, 50, 100]:\n            self.data[f'recent_spikes_{window}'] = self.data['is_spike'].rolling(window=window).sum()\n            features.append(f'recent_spikes_{window}')\n        \n        self.feature_columns = features\n        \n        # Drop NaN rows\n        initial_len = len(self.data)\n        self.data.dropna(inplace=True)\n        final_len = len(self.data)\n        \n        self.logger.info(f\"Features created. Data reduced from {initial_len} to {final_len} rows\")\n        \n        return features\n\nclass BoomDataset(Dataset):\n    \"\"\"Dataset for Boom spike prediction\"\"\"\n    \n    def __init__(self, X, y_spike, y_uniform_count):\n        self.X = torch.FloatTensor(X)\n        self.y_spike = torch.LongTensor(y_spike)\n        self.y_uniform_count = torch.FloatTensor(y_uniform_count)\n    \n    def __len__(self):\n        return len(self.X)\n    \n    def __getitem__(self, idx):\n        return self.X[idx], self.y_spike[idx], self.y_uniform_count[idx]\n\nclass BoomSpikeModel(nn.Module):\n    \"\"\"Dual-task model: spike prediction + uniform tick counting\"\"\"\n    \n    def __init__(self, input_size, hidden_size=128, dropout=0.2):\n        super(BoomSpikeModel, self).__init__()\n        \n        # Shared layers\n        self.shared_layers = nn.Sequential(\n            nn.Linear(input_size, hidden_size),\n            nn.ReLU(),\n            nn.Dropout(dropout),\n            nn.Linear(hidden_size, hidden_size // 2),\n            nn.ReLU(),\n            nn.Dropout(dropout)\n        )\n        \n        # Spike prediction head (classification)\n        self.spike_head = nn.Sequential(\n            nn.Linear(hidden_size // 2, 32),\n            nn.ReLU(),\n            nn.Linear(32, 2)  # Binary classification\n        )\n        \n        # Uniform tick count head (regression)\n        self.count_head = nn.Sequential(\n            nn.Linear(hidden_size // 2, 32),\n            nn.ReLU(),\n            nn.Linear(32, 1)  # Regression output\n        )\n    \n    def forward(self, x):\n        shared_features = self.shared_layers(x)\n        spike_pred = self.spike_head(shared_features)\n        count_pred = self.count_head(shared_features)\n        return spike_pred, count_pred.squeeze()\n\ndef train_boom_model(csv_file, epochs=50, batch_size=512, learning_rate=0.001):\n    \"\"\"Train the comprehensive Boom spike prediction model\"\"\"\n    \n    logger = setup_logging()\n    logger.info(\"Starting Boom spike prediction model training\")\n    \n    start_time = time.time()\n    \n    # Initialize analyzer\n    analyzer = BoomDataAnalyzer(csv_file, logger)\n    \n    # Detect spikes and uniform periods\n    spike_count, avg_uniform_ticks = analyzer.detect_spikes_and_uniform_periods()\n    \n    # Create features\n    features = analyzer.create_features()\n    \n    # Prepare data\n    X = analyzer.data[features].values\n    y_spike = analyzer.data['is_spike'].values\n    y_uniform_count = analyzer.data['uniform_ticks_after_spike'].values\n    \n    logger.info(f\"Dataset shape: {X.shape}\")\n    logger.info(f\"Spike rate: {y_spike.mean():.4f}\")\n    logger.info(f\"Features: {len(features)}\")\n    \n    # Scale features\n    scaler = StandardScaler()\n    X_scaled = scaler.fit_transform(X)\n    \n    # Train-test split\n    X_train, X_test, y_spike_train, y_spike_test, y_count_train, y_count_test = train_test_split(\n        X_scaled, y_spike, y_uniform_count, test_size=0.2, random_state=42, stratify=y_spike\n    )\n    \n    logger.info(f\"Train samples: {len(X_train)}, Test samples: {len(X_test)}\")\n    \n    # Create datasets and loaders\n    train_dataset = BoomDataset(X_train, y_spike_train, y_count_train)\n    test_dataset = BoomDataset(X_test, y_spike_test, y_count_test)\n    \n    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)\n    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)\n    \n    # Initialize model\n    model = BoomSpikeModel(input_size=X_train.shape[1])\n    \n    # Loss functions\n    spike_criterion = nn.CrossEntropyLoss()\n    count_criterion = nn.MSELoss()\n    \n    # Optimizer\n    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)\n    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.5)\n    \n    # Training history\n    history = {\n        'train_spike_loss': [],\n        'train_count_loss': [],\n        'val_spike_loss': [],\n        'val_count_loss': [],\n        'val_spike_acc': []\n    }\n    \n    logger.info(f\"Starting training for {epochs} epochs\")\n    \n    best_val_acc = 0\n    patience_counter = 0\n    \n    for epoch in range(epochs):\n        epoch_start = time.time()\n        \n        # Training phase\n        model.train()\n        train_spike_loss = 0\n        train_count_loss = 0\n        \n        for X_batch, y_spike_batch, y_count_batch in train_loader:\n            optimizer.zero_grad()\n            \n            spike_pred, count_pred = model(X_batch)\n            \n            # Calculate losses\n            spike_loss = spike_criterion(spike_pred, y_spike_batch)\n            count_loss = count_criterion(count_pred, y_count_batch)\n            \n            # Combined loss (weighted)\n            total_loss = spike_loss + 0.1 * count_loss  # Weight count loss lower\n            \n            total_loss.backward()\n            optimizer.step()\n            \n            train_spike_loss += spike_loss.item()\n            train_count_loss += count_loss.item()\n        \n        train_spike_loss /= len(train_loader)\n        train_count_loss /= len(train_loader)\n        \n        # Validation phase\n        model.eval()\n        val_spike_loss = 0\n        val_count_loss = 0\n        correct = 0\n        total = 0\n        \n        with torch.no_grad():\n            for X_batch, y_spike_batch, y_count_batch in test_loader:\n                spike_pred, count_pred = model(X_batch)\n                \n                spike_loss = spike_criterion(spike_pred, y_spike_batch)\n                count_loss = count_criterion(count_pred, y_count_batch)\n                \n                val_spike_loss += spike_loss.item()\n                val_count_loss += count_loss.item()\n                \n                # Calculate accuracy\n                _, predicted = torch.max(spike_pred.data, 1)\n                total += y_spike_batch.size(0)\n                correct += (predicted == y_spike_batch).sum().item()\n        \n        val_spike_loss /= len(test_loader)\n        val_count_loss /= len(test_loader)\n        val_spike_acc = correct / total\n        \n        # Update learning rate\n        scheduler.step(val_spike_loss)\n        \n        # Save history\n        history['train_spike_loss'].append(train_spike_loss)\n        history['train_count_loss'].append(train_count_loss)\n        history['val_spike_loss'].append(val_spike_loss)\n        history['val_count_loss'].append(val_count_loss)\n        history['val_spike_acc'].append(val_spike_acc)\n        \n        epoch_time = time.time() - epoch_start\n        \n        # Logging\n        logger.info(f\"Epoch {epoch+1}/{epochs}:\")\n        logger.info(f\"  Train - Spike Loss: {train_spike_loss:.4f}, Count Loss: {train_count_loss:.4f}\")\n        logger.info(f\"  Val - Spike Loss: {val_spike_loss:.4f}, Count Loss: {val_count_loss:.4f}\")\n        logger.info(f\"  Val Spike Accuracy: {val_spike_acc:.4f}\")\n        logger.info(f\"  Epoch Time: {epoch_time:.2f}s\")\n        logger.info(f\"  Current LR: {optimizer.param_groups[0]['lr']:.6f}\")\n        \n        # Early stopping\n        if val_spike_acc > best_val_acc:\n            best_val_acc = val_spike_acc\n            patience_counter = 0\n            \n            # Save best model\n            torch.save({\n                'model_state_dict': model.state_dict(),\n                'scaler': scaler,\n                'features': features,\n                'spike_count': spike_count,\n                'avg_uniform_ticks': avg_uniform_ticks,\n                'best_val_acc': best_val_acc\n            }, 'boom_spike_model.pth')\n            \n            logger.info(f\"  New best model saved! Accuracy: {best_val_acc:.4f}\")\n        else:\n            patience_counter += 1\n            \n        if patience_counter >= 10:\n            logger.info(\"Early stopping triggered\")\n            break\n    \n    # Final evaluation\n    logger.info(\"\\nFinal evaluation on test set...\")\n    \n    model.eval()\n    y_true_spike = []\n    y_pred_spike = []\n    y_true_count = []\n    y_pred_count = []\n    \n    with torch.no_grad():\n        for X_batch, y_spike_batch, y_count_batch in test_loader:\n            spike_pred, count_pred = model(X_batch)\n            \n            _, predicted_spike = torch.max(spike_pred, 1)\n            \n            y_true_spike.extend(y_spike_batch.numpy())\n            y_pred_spike.extend(predicted_spike.numpy())\n            y_true_count.extend(y_count_batch.numpy())\n            y_pred_count.extend(count_pred.numpy())\n    \n    # Classification report for spike prediction\n    logger.info(\"\\nSpike Prediction Classification Report:\")\n    logger.info(\"\\n\" + classification_report(y_true_spike, y_pred_spike))\n    \n    # Count prediction metrics\n    count_mae = np.mean(np.abs(np.array(y_true_count) - np.array(y_pred_count)))\n    count_rmse = np.sqrt(np.mean((np.array(y_true_count) - np.array(y_pred_count))**2))\n    \n    logger.info(f\"\\nUniform Tick Count Prediction:\")\n    logger.info(f\"MAE: {count_mae:.2f}\")\n    logger.info(f\"RMSE: {count_rmse:.2f}\")\n    \n    # Create plots\n    create_training_plots(history, logger)\n    \n    total_time = time.time() - start_time\n    logger.info(f\"\\nTraining completed in {total_time:.2f} seconds ({total_time/60:.1f} minutes)\")\n    logger.info(f\"Best validation accuracy: {best_val_acc:.4f}\")\n    \n    return model, scaler, features, history\n\ndef create_training_plots(history, logger):\n    \"\"\"Create training progress plots\"\"\"\n    \n    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))\n    \n    # Spike loss\n    ax1.plot(history['train_spike_loss'], label='Train Spike Loss', color='blue')\n    ax1.plot(history['val_spike_loss'], label='Val Spike Loss', color='red')\n    ax1.set_title('Spike Prediction Loss')\n    ax1.set_xlabel('Epoch')\n    ax1.set_ylabel('Loss')\n    ax1.legend()\n    ax1.grid(True, alpha=0.3)\n    \n    # Count loss\n    ax2.plot(history['train_count_loss'], label='Train Count Loss', color='blue')\n    ax2.plot(history['val_count_loss'], label='Val Count Loss', color='red')\n    ax2.set_title('Uniform Tick Count Loss')\n    ax2.set_xlabel('Epoch')\n    ax2.set_ylabel('Loss')\n    ax2.legend()\n    ax2.grid(True, alpha=0.3)\n    \n    # Validation accuracy\n    ax3.plot(history['val_spike_acc'], label='Val Spike Accuracy', color='green')\n    ax3.set_title('Validation Spike Accuracy')\n    ax3.set_xlabel('Epoch')\n    ax3.set_ylabel('Accuracy')\n    ax3.legend()\n    ax3.grid(True, alpha=0.3)\n    \n    # Combined losses\n    ax4.plot(history['train_spike_loss'], label='Train Spike', alpha=0.7)\n    ax4.plot(history['val_spike_loss'], label='Val Spike', alpha=0.7)\n    ax4.plot(np.array(history['train_count_loss'])*10, label='Train Count (x10)', alpha=0.7)\n    ax4.plot(np.array(history['val_count_loss'])*10, label='Val Count (x10)', alpha=0.7)\n    ax4.set_title('All Losses Combined')\n    ax4.set_xlabel('Epoch')\n    ax4.set_ylabel('Loss')\n    ax4.legend()\n    ax4.grid(True, alpha=0.3)\n    \n    plt.tight_layout()\n    plt.savefig('boom_training_results.png', dpi=150, bbox_inches='tight')\n    plt.show()\n    \n    logger.info(\"Training plots saved to: boom_training_results.png\")\n\nif __name__ == '__main__':\n    parser = argparse.ArgumentParser(description='Boom Spike Prediction Model')\n    parser.add_argument('--csv_file', type=str, default='Boom_1000_Index_7days_20250620_20250627.csv')\n    parser.add_argument('--epochs', type=int, default=50)\n    parser.add_argument('--batch_size', type=int, default=512)\n    parser.add_argument('--learning_rate', type=float, default=0.001)\n    args = parser.parse_args()\n    \n    model, scaler, features, history = train_boom_model(\n        csv_file=args.csv_file,\n        epochs=args.epochs,\n        batch_size=args.batch_size,\n        learning_rate=args.learning_rate\n    )
