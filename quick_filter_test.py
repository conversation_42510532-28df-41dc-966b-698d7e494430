import pandas as pd
import numpy as np
import os

# Load OHLC data
DATA_PATH = "stpRNG_1min.csv"
df = pd.read_csv(DATA_PATH).dropna().reset_index(drop=True)
print(f"Loaded {len(df)} rows of data")

def calculate_ema(series, period):
    return series.ewm(span=period, adjust=False).mean()

def calculate_rsi(series, period):
    delta = series.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))

def calculate_atr(df, period):
    tr1 = df["high"] - df["low"]
    tr2 = abs(df["high"] - df["close"].shift(1))
    tr3 = abs(df["low"] - df["close"].shift(1))
    tr = pd.DataFrame([tr1, tr2, tr3]).max()
    return tr.rolling(window=period).mean()

def backtest_signals(df, buy_signals, sell_signals, strategy_name):
    """Quick backtest function"""
    capital = 100_000
    risk = 0.004
    balance = capital
    position = 0
    entry_price = 0
    trades = []
    
    for i in range(1, len(df)):
        price = df.iloc[i]['open']
        
        # Exit positions
        if position > 0 and sell_signals.iloc[i]:
            pnl = (price - entry_price) / entry_price
            trades.append(pnl)
            balance *= (1 + risk) if pnl > 0 else (1 - risk)
            position = 0
        elif position < 0 and buy_signals.iloc[i]:
            pnl = (entry_price - price) / entry_price
            trades.append(pnl)
            balance *= (1 + risk) if pnl > 0 else (1 - risk)
            position = 0
        
        # Enter positions
        if position == 0:
            if buy_signals.iloc[i]:
                position = 1
                entry_price = price
            elif sell_signals.iloc[i]:
                position = -1
                entry_price = price
    
    total_trades = len(trades)
    wins = len([t for t in trades if t > 0])
    win_rate = wins / total_trades * 100 if total_trades > 0 else 0
    total_return = (balance - capital) / capital * 100
    
    return {
        'strategy': strategy_name,
        'trades': total_trades,
        'win_rate': win_rate,
        'return': total_return,
        'balance': balance
    }

# Calculate base Q-Trend signals (best known parameters)
print("Calculating Q-Trend base signals...")

# Q-Trend with best parameters
trend_period = 200
atr_period = 3
atr_mult = 1.6

df["src"] = df["close"]
df["h"] = df["src"].rolling(window=trend_period).max()
df["l"] = df["src"].rolling(window=trend_period).min()
df["m"] = (df["h"] + df["l"]) / 2
df["atr"] = calculate_atr(df, atr_period).shift(1)
df["epsilon"] = atr_mult * df["atr"]

# Quick Q-Trend signal calculation
df["trend"] = df["m"]  # Simplified
df["qtrend_buy"] = False
df["qtrend_sell"] = False

for i in range(trend_period, len(df)):
    if pd.notna(df.at[i, "epsilon"]):
        prev_trend = df.at[i-1, "trend"]
        src = df.at[i, "src"]
        epsilon = df.at[i, "epsilon"]
        
        if src > prev_trend + epsilon:
            df.at[i, "qtrend_buy"] = True
            df.at[i, "trend"] = prev_trend + epsilon
        elif src < prev_trend - epsilon:
            df.at[i, "qtrend_sell"] = True
            df.at[i, "trend"] = prev_trend - epsilon
        else:
            df.at[i, "trend"] = prev_trend

# Calculate additional indicators
print("Calculating additional indicators...")

# EMAs
df['ema12'] = calculate_ema(df['close'], 12)
df['ema25'] = calculate_ema(df['close'], 25)
df['ema50'] = calculate_ema(df['close'], 50)
df['ema200'] = calculate_ema(df['close'], 200)

# EMA conditions
df['ema_bull'] = df['ema12'] > df['ema25']
df['ema_bear'] = df['ema12'] < df['ema25']
df['above_ema50'] = df['close'] > df['ema50']
df['above_ema200'] = df['close'] > df['ema200']

# RSI
df['rsi'] = calculate_rsi(df['close'], 14)
df['rsi_oversold'] = df['rsi'] < 30
df['rsi_overbought'] = df['rsi'] > 70
df['rsi_neutral'] = (df['rsi'] >= 30) & (df['rsi'] <= 70)

# Momentum
df['momentum_5'] = df['close'].pct_change(5)
df['momentum_10'] = df['close'].pct_change(10)
df['positive_momentum'] = df['momentum_5'] > 0.002
df['negative_momentum'] = df['momentum_5'] < -0.002

# Volume (if available)
if 'volume' in df.columns:
    df['volume_ma'] = df['volume'].rolling(20).mean()
    df['high_volume'] = df['volume'] > df['volume_ma'] * 1.5
else:
    df['high_volume'] = True

# Test filter combinations
print("\n=== TESTING FILTER COMBINATIONS ===")

test_configs = [
    # Base Q-Trend
    {
        'name': 'Q-Trend Only',
        'buy': df['qtrend_buy'],
        'sell': df['qtrend_sell']
    },
    
    # Q-Trend + EMA trend filter
    {
        'name': 'Q-Trend + EMA Trend',
        'buy': df['qtrend_buy'] & df['ema_bull'],
        'sell': df['qtrend_sell'] & df['ema_bear']
    },
    
    # Q-Trend + EMA 50 filter
    {
        'name': 'Q-Trend + Above EMA50',
        'buy': df['qtrend_buy'] & df['above_ema50'],
        'sell': df['qtrend_sell'] & ~df['above_ema50']
    },
    
    # Q-Trend + EMA 200 filter
    {
        'name': 'Q-Trend + Above EMA200',
        'buy': df['qtrend_buy'] & df['above_ema200'],
        'sell': df['qtrend_sell'] & ~df['above_ema200']
    },
    
    # Q-Trend + RSI filter
    {
        'name': 'Q-Trend + RSI Oversold/Overbought',
        'buy': df['qtrend_buy'] & df['rsi_oversold'],
        'sell': df['qtrend_sell'] & df['rsi_overbought']
    },
    
    # Q-Trend + RSI neutral filter
    {
        'name': 'Q-Trend + RSI Neutral',
        'buy': df['qtrend_buy'] & df['rsi_neutral'],
        'sell': df['qtrend_sell'] & df['rsi_neutral']
    },
    
    # Q-Trend + Momentum filter
    {
        'name': 'Q-Trend + Momentum',
        'buy': df['qtrend_buy'] & df['positive_momentum'],
        'sell': df['qtrend_sell'] & df['negative_momentum']
    },
    
    # Q-Trend + Volume filter
    {
        'name': 'Q-Trend + High Volume',
        'buy': df['qtrend_buy'] & df['high_volume'],
        'sell': df['qtrend_sell'] & df['high_volume']
    },
    
    # Multi-filter combinations
    {
        'name': 'Q-Trend + EMA + RSI',
        'buy': df['qtrend_buy'] & df['ema_bull'] & df['rsi_oversold'],
        'sell': df['qtrend_sell'] & df['ema_bear'] & df['rsi_overbought']
    },
    
    {
        'name': 'Q-Trend + EMA200 + Momentum',
        'buy': df['qtrend_buy'] & df['above_ema200'] & df['positive_momentum'],
        'sell': df['qtrend_sell'] & ~df['above_ema200'] & df['negative_momentum']
    },
    
    {
        'name': 'Q-Trend + EMA + Volume',
        'buy': df['qtrend_buy'] & df['ema_bull'] & df['high_volume'],
        'sell': df['qtrend_sell'] & df['ema_bear'] & df['high_volume']
    },
    
    # Conservative (multiple filters)
    {
        'name': 'Conservative (EMA+RSI+Mom+Vol)',
        'buy': df['qtrend_buy'] & df['ema_bull'] & df['rsi_oversold'] & df['positive_momentum'] & df['high_volume'],
        'sell': df['qtrend_sell'] & df['ema_bear'] & df['rsi_overbought'] & df['negative_momentum'] & df['high_volume']
    }
]

# Run tests
results = []
for config in test_configs:
    buy_signals = config['buy']
    sell_signals = config['sell']
    
    # Count signals
    buy_count = buy_signals.sum()
    sell_count = sell_signals.sum()
    total_signals = buy_count + sell_count
    
    if total_signals > 0:
        result = backtest_signals(df, buy_signals, sell_signals, config['name'])
        result['buy_signals'] = buy_count
        result['sell_signals'] = sell_count
        result['total_signals'] = total_signals
        results.append(result)
        
        print(f"{result['strategy']:25s} | {result['return']:+6.2f}% | {result['trades']:3d} trades | {result['win_rate']:5.1f}% WR | {total_signals:3d} signals")
    else:
        print(f"{config['name']:25s} | NO SIGNALS")

# Sort by return
results.sort(key=lambda x: x['return'], reverse=True)

print(f"\n=== TOP PERFORMERS ===")
for i, result in enumerate(results[:5]):
    print(f"{i+1}. {result['strategy']:25s} | {result['return']:+6.2f}% | {result['trades']:3d} trades | {result['win_rate']:5.1f}% WR")

# Find best trade frequency vs performance balance
print(f"\n=== TRADE FREQUENCY ANALYSIS ===")
high_frequency = [r for r in results if r['trades'] >= 100]
medium_frequency = [r for r in results if 20 <= r['trades'] < 100]
low_frequency = [r for r in results if r['trades'] < 20]

if high_frequency:
    best_high = max(high_frequency, key=lambda x: x['return'])
    print(f"Best High Frequency (100+ trades): {best_high['strategy']} | {best_high['return']:+.2f}%")

if medium_frequency:
    best_medium = max(medium_frequency, key=lambda x: x['return'])
    print(f"Best Medium Frequency (20-99 trades): {best_medium['strategy']} | {best_medium['return']:+.2f}%")

if low_frequency:
    best_low = max(low_frequency, key=lambda x: x['return'])
    print(f"Best Low Frequency (<20 trades): {best_low['strategy']} | {best_low['return']:+.2f}%")

# Filter effectiveness analysis
print(f"\n=== FILTER EFFECTIVENESS ===")
base_performance = next((r['return'] for r in results if r['strategy'] == 'Q-Trend Only'), 0)

filter_effects = []
for result in results:
    if result['strategy'] != 'Q-Trend Only':
        improvement = result['return'] - base_performance
        trade_reduction = (results[0]['trades'] - result['trades']) / results[0]['trades'] * 100 if results[0]['trades'] > 0 else 0
        
        filter_effects.append({
            'filter': result['strategy'].replace('Q-Trend + ', '').replace('Q-Trend ', ''),
            'improvement': improvement,
            'trade_reduction': trade_reduction,
            'final_return': result['return'],
            'trades': result['trades']
        })

# Sort by improvement
filter_effects.sort(key=lambda x: x['improvement'], reverse=True)

print("Filter effectiveness (vs Q-Trend Only):")
for effect in filter_effects[:8]:
    print(f"  {effect['filter']:20s}: {effect['improvement']:+6.2f}% improvement | {effect['trade_reduction']:5.1f}% fewer trades")

# Save results
results_df = pd.DataFrame(results)
results_df.to_csv('quick_filter_results.csv', index=False)
print(f"\nResults saved to 'quick_filter_results.csv'")

print(f"\n=== SUMMARY ===")
print(f"Tested {len(test_configs)} filter combinations")
print(f"Best overall performance: {results[0]['strategy']} with {results[0]['return']:+.2f}% return")
print(f"Base Q-Trend performance: {base_performance:+.2f}% return")
