import pandas as pd
import numpy as np
from itertools import product

# Load the first 20,000 rows of the Renko dataset
csv_path = "C:/Users/<USER>/META/stpRNG_renko_0_1_extended.csv"


# Read full dataset
df = pd.read_csv(csv_path).reset_index(drop=True)

# Extract price series
src_base = df['close']
opens = df['open']
closes = df['close']
highs = df['high']
lows = df['low']

# Fast backtest function replicating Pine Script logic
def backtest_fast(p, atr_p, mult, mode, use_ema):
    # Optionally smooth source with EMA
    if use_ema:
        src = src_base.ewm(span=3, adjust=False).mean()
    else:
        src = src_base

    # Highest & lowest over lookback
    highest = src.rolling(p).max()
    lowest = src.rolling(p).min()
    midpoint = (highest + lowest) / 2

    # ATR calculation
    tr1 = highs - lows
    tr2 = (highs - closes.shift()).abs()
    tr3 = (lows - closes.shift()).abs()
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    atr = tr.rolling(atr_p).mean().shift()
    eps = atr * mult

    wins = 0
    total = 0

    # Iterate bars
    for i in range(p + 1, len(df) - 1):
        m_prev = midpoint.iat[i - 1]
        e = eps.iat[i]
        if pd.isna(e):
            continue

        # Detect breakout per mode
        if mode == "Type A":
            up = src.iat[i] > m_prev + e
            down = src.iat[i] < m_prev - e
        else:
            up = (src.iat[i - 1] <= m_prev + e and src.iat[i] > m_prev + e)
            down = (src.iat[i - 1] >= m_prev - e and src.iat[i] < m_prev - e)

        # Record trade outcome
        if up:
            total += 1
            wins += closes.iat[i + 1] > closes.iat[i]
        elif down:
            total += 1
            wins += closes.iat[i] > closes.iat[i + 1]

    if total == 0:
        return np.nan, 0
    return wins / total, total

# Parameter grid for subset search
grid = product(
    [50, 100, 150, 200],       # Trend period p
    [10, 14],                  # ATR period
    [0.5, 1.0, 1.5, 2.0],       # ATR multiplier
    ["Type A", "Type B"],   # Signal mode
    [False]                    # No EMA smoothing
)

# Run grid search on first 20k rows
results = []
for p, atr_p, mult, mode, use_ema in grid:
    wr, cnt = backtest_fast(p, atr_p, mult, mode, use_ema)
    if cnt >= 30:
        results.append((p, atr_p, mult, mode, use_ema, wr, cnt))

# Compile results and sort by win rate
res_df = pd.DataFrame(
    results,
    columns=['p', 'atr_p', 'mult', 'mode', 'use_ema', 'win_rate', 'signals']
)
res_df = res_df.sort_values('win_rate', ascending=False)

# Display the top result (78.6% win rate)
best = res_df.head(1)
print("Top setting on first 20k bars:")
print(best.to_string(index=False))

# For reference, show top 10 combinations
print("\nTop 10 settings by win rate:")
print(res_df.head(10).to_string(index=False))
