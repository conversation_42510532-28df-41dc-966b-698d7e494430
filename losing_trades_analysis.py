"""
DETAILED Boom 1000 Index Losing Trades Analysis
Focus on accurate balance deterioration and price movements during losses
"""

import pandas as pd
import numpy as np
from datetime import datetime

class LosingTradesAnalyzer:
    def __init__(self, csv_file, starting_capital=20):
        self.csv_file = csv_file
        self.starting_capital = starting_capital
        self.current_capital = starting_capital
        
        # Boom 1000 specifications
        self.min_volume = 0.20
        self.max_volume = 50.0
        self.tick_size = 0.001
        self.tick_value = 0.001
        
        # Fixed position size for analysis
        self.position_size = 20.0  # lots
        self.collection_window = 100  # ticks
        
        self.trades = []
        self.df = None
        self.spikes = []
        
    def load_and_parse_data(self):
        """Load tick data"""
        print("Loading data for losing trades analysis...")
        
        raw_df = pd.read_csv(self.csv_file)
        parsed_data = []
        
        for _, row in raw_df.iterrows():
            try:
                tuple_str = row['0']
                if tuple_str.startswith('(') and tuple_str.endswith(')'):
                    values = tuple_str[1:-1].split(', ')
                    
                    tick_data = {
                        'timestamp': int(values[0]),
                        'bid': float(values[1]),
                        'ask': float(values[2]),
                        'mid_price': (float(values[1]) + float(values[2])) / 2,
                        'datetime': None  # Will set below
                    }
                    parsed_data.append(tick_data)
            except:
                continue
        
        self.df = pd.DataFrame(parsed_data)
        self.df['datetime'] = pd.to_datetime(self.df['timestamp'], unit='s')
        self.df = self.df.sort_values('timestamp').reset_index(drop=True)
        
        print(f"Loaded {len(self.df)} ticks")
        return self.df
    
    def detect_spikes(self):
        """Detect price spikes"""
        print("Detecting spikes...")
        
        self.df['price_change'] = self.df['mid_price'].diff()
        self.df['price_change_pct'] = (self.df['price_change'] / self.df['mid_price'].shift(1)) * 100
        
        # Multiple detection methods
        pct_spikes = self.df[
            (self.df['price_change_pct'] > 0.1) & 
            (self.df['price_change'] > 0)
        ].index.tolist()
        
        abs_spikes = self.df[self.df['price_change'] > 0.5].index.tolist()
        
        all_spikes = list(set(pct_spikes + abs_spikes))
        all_spikes.sort()
        
        # Filter close spikes
        filtered_spikes = []
        for spike_idx in all_spikes:
            if not filtered_spikes or spike_idx - filtered_spikes[-1] >= 5:
                filtered_spikes.append(spike_idx)
        
        self.spikes = filtered_spikes
        print(f"Detected {len(self.spikes)} spikes")
        return self.spikes
    
    def execute_and_analyze_trades(self):
        """Execute trades and track detailed P&L"""
        print("Executing trades with detailed tracking...")
        
        self.current_capital = self.starting_capital
        
        for i, spike_idx in enumerate(self.spikes):
            if spike_idx + self.collection_window >= len(self.df):
                continue
            
            # Trade setup: SELL after spike
            entry_idx = spike_idx + 1
            exit_idx = spike_idx + self.collection_window
            
            entry_price = self.df.iloc[entry_idx]['ask']  # SELL at ask
            exit_price = self.df.iloc[exit_idx]['bid']    # Cover at bid
            
            # Track price during trade period
            trade_data = self.df.iloc[entry_idx:exit_idx + 1].copy()
            
            # Calculate worst-case scenario during trade
            highest_ask = trade_data['ask'].max()  # Worst for short position
            lowest_bid = trade_data['bid'].min()   # Best for short position
            
            # P&L calculations
            price_change = entry_price - exit_price  # Positive = profit for SELL
            ticks_change = price_change / self.tick_size
            final_pnl = ticks_change * self.tick_value * self.position_size
            
            # Worst case P&L during trade
            worst_price_move = highest_ask - entry_price  # Against us
            worst_ticks = worst_price_move / self.tick_size
            worst_unrealized_pnl = -(worst_ticks * self.tick_value * self.position_size)
            
            # Best case P&L during trade
            best_price_move = entry_price - lowest_bid  # In our favor
            best_ticks = best_price_move / self.tick_size
            best_unrealized_pnl = best_ticks * self.tick_value * self.position_size
            
            # Account balance at worst point
            worst_balance = self.current_capital + worst_unrealized_pnl
            
            # Update actual balance
            previous_balance = self.current_capital
            self.current_capital += final_pnl
            
            trade = {
                'trade_num': len(self.trades) + 1,
                'spike_idx': spike_idx,
                'entry_time': self.df.iloc[entry_idx]['datetime'],
                'exit_time': self.df.iloc[exit_idx]['datetime'],
                'entry_price': entry_price,
                'exit_price': exit_price,
                'position_size': self.position_size,
                'price_move': price_change,
                'ticks_move': ticks_change,
                'final_pnl': final_pnl,
                'previous_balance': previous_balance,
                'final_balance': self.current_capital,
                'highest_price': highest_ask,
                'lowest_price': lowest_bid,
                'worst_price_move': worst_price_move,
                'worst_unrealized_pnl': worst_unrealized_pnl,
                'worst_balance': worst_balance,
                'best_unrealized_pnl': best_unrealized_pnl,
                'spike_size': self.df.iloc[spike_idx]['price_change'],
                'spike_pct': self.df.iloc[spike_idx]['price_change_pct'],
                'is_winner': final_pnl > 0,
                'margin_call_risk': worst_balance < 0
            }
            
            self.trades.append(trade)
        
        print(f"Analyzed {len(self.trades)} trades")
        return self.trades
    
    def analyze_losing_trades(self):
        """Detailed analysis of losing trades"""
        if not self.trades:
            return
        
        trades_df = pd.DataFrame(self.trades)
        losing_trades = trades_df[trades_df['final_pnl'] < 0].copy()
        winning_trades = trades_df[trades_df['final_pnl'] > 0].copy()
        
        print("\n" + "="*80)
        print("DETAILED LOSING TRADES ANALYSIS")
        print("="*80)
        
        print(f"\n📊 TRADE SUMMARY:")
        print(f"Total trades: {len(trades_df)}")
        print(f"Winning trades: {len(winning_trades)} ({len(winning_trades)/len(trades_df)*100:.1f}%)")
        print(f"Losing trades: {len(losing_trades)} ({len(losing_trades)/len(trades_df)*100:.1f}%)")
        
        if len(losing_trades) > 0:
            print(f"\n💰 P&L BREAKDOWN:")
            print(f"Total wins: ${winning_trades['final_pnl'].sum():.2f}")
            print(f"Total losses: ${losing_trades['final_pnl'].sum():.2f}")
            print(f"Net P&L: ${trades_df['final_pnl'].sum():.2f}")
            print(f"Final balance: ${self.current_capital:.2f}")
            
            print(f"\n🔴 LOSING TRADES DETAILS:")
            losing_trades_sorted = losing_trades.sort_values('final_pnl')
            
            for _, trade in losing_trades_sorted.iterrows():
                print(f"\n❌ Trade {trade['trade_num']} - LOSS: ${trade['final_pnl']:.2f}")
                print(f"   📅 Time: {trade['entry_time'].strftime('%m-%d %H:%M:%S')}")
                print(f"   📈 Entry: ${trade['entry_price']:.3f} → Exit: ${trade['exit_price']:.3f}")
                print(f"   📊 Price moved: {trade['price_move']:.3f} units ({trade['price_move']/trade['entry_price']*100:.3f}%)")
                print(f"   ⚠️  Worst price during trade: ${trade['highest_price']:.3f}")
                print(f"   💥 Worst move against us: {trade['worst_price_move']:.3f} units ({trade['worst_price_move']/trade['entry_price']*100:.3f}%)")
                print(f"   💸 Worst unrealized loss: ${trade['worst_unrealized_pnl']:.2f}")
                print(f"   🏦 Balance at worst point: ${trade['worst_balance']:.2f}")
                print(f"   🚨 Margin call risk: {'YES' if trade['margin_call_risk'] else 'NO'}")
                print(f"   🎯 Original spike: {trade['spike_pct']:.3f}%")
            
            # Risk statistics
            margin_call_count = sum(losing_trades['margin_call_risk'])
            avg_worst_move = losing_trades['worst_price_move'].mean()
            max_worst_move = losing_trades['worst_price_move'].max()
            avg_loss = losing_trades['final_pnl'].mean()
            max_loss = losing_trades['final_pnl'].min()
            
            print(f"\n📊 LOSING TRADES STATISTICS:")
            print(f"Average loss: ${avg_loss:.2f}")
            print(f"Worst single loss: ${max_loss:.2f}")
            print(f"Average worst price move: {avg_worst_move:.3f} units")
            print(f"Worst price move ever: {max_worst_move:.3f} units")
            print(f"Trades with margin call risk: {margin_call_count}/{len(losing_trades)}")
            
        else:
            print("\n✅ NO LOSING TRADES FOUND!")
            print(f"All {len(trades_df)} trades were profitable")
            print(f"Total profit: ${trades_df['final_pnl'].sum():.2f}")
        
        print("\n" + "="*80)

def main():
    """Run the losing trades analysis"""
    print("Starting Detailed Losing Trades Analysis")
    print("Analyzing accurate balance deterioration and price movements")
    
    csv_file = "C:\\Users\\<USER>\\META\\Boom_1000_Index_7days_20250619_20250626.csv"
    analyzer = LosingTradesAnalyzer(csv_file, starting_capital=20)
    
    # Execute analysis
    analyzer.load_and_parse_data()
    analyzer.detect_spikes()
    analyzer.execute_and_analyze_trades()
    analyzer.analyze_losing_trades()
    
    return analyzer

if __name__ == "__main__":
    analyzer = main()
