"""
COMPREHENSIVE XGBoost Analysis for Boom 1000 Index
Objective: Extract ALL possible features and create an aggressive trading strategy
Features: Spikes, double spikes, volatility, momentum, technical indicators, patterns
"""

import pandas as pd
import numpy as np
import xgboost as xgb
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
from sklearn.preprocessing import StandardScaler
import warnings
import pickle
from datetime import datetime
warnings.filterwarnings('ignore')

class ComprehensiveBoomAnalyzer:
    def __init__(self, csv_file):
        self.csv_file = csv_file
        self.df = None
        self.model = None
        self.scaler = StandardScaler()
        
    def load_and_parse_data(self):
        """Load and parse tick data"""
        print("Loading comprehensive data...")
        raw_df = pd.read_csv(self.csv_file)
        parsed_data = []
        
        for _, row in raw_df.iterrows():
            try:
                tuple_str = row['0']
                if tuple_str.startswith('(') and tuple_str.endswith(')'):
                    values = tuple_str[1:-1].split(', ')
                    parsed_data.append({
                        'timestamp': int(values[0]),
                        'bid': float(values[1]),
                        'ask': float(values[2]),
                        'mid_price': (float(values[1]) + float(values[2])) / 2,
                        'spread': float(values[2]) - float(values[1])
                    })
            except: continue
        
        self.df = pd.DataFrame(parsed_data)
        self.df['datetime'] = pd.to_datetime(self.df['timestamp'], unit='s')
        self.df = self.df.sort_values('timestamp').reset_index(drop=True)
        print(f"Loaded {len(self.df)} ticks")
        
        return self.df
    
    def extract_comprehensive_features(self):
        """Extract ALL possible features for ML"""
        print("Extracting comprehensive features...")
        
        # Basic price features
        self.df['price_change'] = self.df['mid_price'].diff()
        self.df['price_change_pct'] = (self.df['price_change'] / self.df['mid_price'].shift(1)) * 100
        self.df['log_return'] = np.log(self.df['mid_price'] / self.df['mid_price'].shift(1))
        
        # Volatility features (multiple windows)
        for window in [5, 10, 20, 50]:
            self.df[f'volatility_{window}'] = self.df['price_change'].rolling(window=window).std()
            self.df[f'rolling_mean_{window}'] = self.df['mid_price'].rolling(window=window).mean()
            self.df[f'rolling_max_{window}'] = self.df['mid_price'].rolling(window=window).max()
            self.df[f'rolling_min_{window}'] = self.df['mid_price'].rolling(window=window).min()
            self.df[f'price_position_{window}'] = (self.df['mid_price'] - self.df[f'rolling_min_{window}']) / (self.df[f'rolling_max_{window}'] - self.df[f'rolling_min_{window}'])
        
        # Momentum features
        for period in [3, 5, 10, 20]:
            self.df[f'momentum_{period}'] = self.df['mid_price'] - self.df['mid_price'].shift(period)
            self.df[f'roc_{period}'] = ((self.df['mid_price'] / self.df['mid_price'].shift(period)) - 1) * 100
        
        # RSI calculation
        for period in [7, 14, 21]:
            delta = self.df['mid_price'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            self.df[f'rsi_{period}'] = 100 - (100 / (1 + rs))
        
        # Spike detection features
        self.df['is_spike'] = ((self.df['price_change_pct'] > 0.1) & (self.df['price_change'] > 0)).astype(int)
        self.df['spike_magnitude'] = np.where(self.df['is_spike'] == 1, self.df['price_change'], 0)
        
        # Ticks since last spike
        spike_indices = self.df[self.df['is_spike'] == 1].index
        self.df['ticks_since_spike'] = 0
        for i in range(len(self.df)):
            last_spike = spike_indices[spike_indices < i]
            if len(last_spike) > 0:
                self.df.iloc[i, self.df.columns.get_loc('ticks_since_spike')] = i - last_spike[-1]
            else:
                self.df.iloc[i, self.df.columns.get_loc('ticks_since_spike')] = i
        
        # Double spike detection
        self.df['is_double_spike'] = 0
        for i in range(1, len(spike_indices)):
            if spike_indices[i] - spike_indices[i-1] <= 100:  # Within 100 ticks
                self.df.iloc[spike_indices[i-1], self.df.columns.get_loc('is_double_spike')] = 1
                self.df.iloc[spike_indices[i], self.df.columns.get_loc('is_double_spike')] = 1
        
        # Technical patterns
        # Support/Resistance levels
        for window in [20, 50]:
            self.df[f'support_{window}'] = self.df['mid_price'].rolling(window=window).min()
            self.df[f'resistance_{window}'] = self.df['mid_price'].rolling(window=window).max()
            self.df[f'distance_to_support_{window}'] = self.df['mid_price'] - self.df[f'support_{window}']
            self.df[f'distance_to_resistance_{window}'] = self.df[f'resistance_{window}'] - self.df['mid_price']
        
        # Moving average crossovers
        for fast, slow in [(5, 20), (10, 50)]:
            ma_fast = self.df['mid_price'].rolling(window=fast).mean()
            ma_slow = self.df['mid_price'].rolling(window=slow).mean()
            self.df[f'ma_cross_{fast}_{slow}'] = (ma_fast > ma_slow).astype(int)
            self.df[f'ma_distance_{fast}_{slow}'] = ma_fast - ma_slow
        
        # Price acceleration
        self.df['acceleration'] = self.df['price_change'].diff()
        
        # Volume-like features (using spread as proxy)
        self.df['spread_change'] = self.df['spread'].diff()
        self.df['spread_volatility'] = self.df['spread'].rolling(window=10).std()
        
        # Time-based features
        self.df['hour'] = self.df['datetime'].dt.hour
        self.df['minute'] = self.df['datetime'].dt.minute
        self.df['second'] = self.df['datetime'].dt.second
        
        # Fractal patterns
        for window in [5, 10]:
            self.df[f'local_max_{window}'] = (self.df['mid_price'] == self.df['mid_price'].rolling(window=window, center=True).max()).astype(int)
            self.df[f'local_min_{window}'] = (self.df['mid_price'] == self.df['mid_price'].rolling(window=window, center=True).min()).astype(int)
        
        print(f"Created {len([col for col in self.df.columns if col not in ['timestamp', 'bid', 'ask', 'mid_price', 'datetime', 'spread']])} features")
        
    def create_target_labels(self):
        """Create target labels for profitable opportunities"""
        print("Creating target labels...")
        
        # Look ahead for profitable opportunities
        # Target: Price drops in next 20-100 ticks (good for SELL positions)
        self.df['future_min_20'] = self.df['mid_price'].rolling(window=20).min().shift(-20)
        self.df['future_min_50'] = self.df['mid_price'].rolling(window=50).min().shift(-50)
        self.df['future_min_100'] = self.df['mid_price'].rolling(window=100).min().shift(-100)
        
        # Multiple targets for different strategies
        # Conservative: 0.1% drop in next 20 ticks
        self.df['target_conservative'] = ((self.df['mid_price'] - self.df['future_min_20']) / self.df['mid_price'] > 0.001).astype(int)
        
        # Moderate: 0.2% drop in next 50 ticks
        self.df['target_moderate'] = ((self.df['mid_price'] - self.df['future_min_50']) / self.df['mid_price'] > 0.002).astype(int)
        
        # Aggressive: 0.3% drop in next 100 ticks
        self.df['target_aggressive'] = ((self.df['mid_price'] - self.df['future_min_100']) / self.df['mid_price'] > 0.003).astype(int)
        
        # Ultimate target: Any profitable move in next 100 ticks
        self.df['target_any_profit'] = ((self.df['mid_price'] - self.df['future_min_100']) / self.df['mid_price'] > 0.0001).astype(int)
        
        print("Target distribution:")
        for target in ['target_conservative', 'target_moderate', 'target_aggressive', 'target_any_profit']:
            positive_rate = self.df[target].mean() * 100
            print(f"{target}: {positive_rate:.1f}% positive opportunities")
    
    def prepare_ml_data(self, target='target_any_profit'):
        """Prepare data for machine learning"""
        print(f"Preparing ML data with target: {target}")
        
        # Select feature columns (exclude metadata and target columns)
        feature_cols = [col for col in self.df.columns if col not in [
            'timestamp', 'bid', 'ask', 'mid_price', 'datetime', 'spread',
            'future_min_20', 'future_min_50', 'future_min_100',
            'target_conservative', 'target_moderate', 'target_aggressive', 'target_any_profit'
        ]]
        
        # Remove rows with NaN values
        clean_df = self.df[feature_cols + [target]].dropna()
        
        X = clean_df[feature_cols]
        y = clean_df[target]
        
        print(f"Features used: {len(feature_cols)}")
        print(f"Clean samples: {len(clean_df)}")
        print(f"Feature names: {feature_cols[:10]}...")  # Show first 10 features
        
        return X, y, feature_cols
    
    def train_xgboost_model(self, target='target_any_profit'):
        """Train comprehensive XGBoost model"""
        print(f"Training XGBoost model for {target}...")
        
        X, y, feature_cols = self.prepare_ml_data(target)
        
        # Train-test split
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, random_state=42, stratify=y
        )
        
        # Scale features
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # XGBoost model with optimized parameters for trading
        self.model = xgb.XGBClassifier(
            n_estimators=200,
            max_depth=6,
            learning_rate=0.1,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42,
            eval_metric='logloss'
        )
        
        # Train model
        self.model.fit(X_train_scaled, y_train)
        
        # Predictions
        y_pred = self.model.predict(X_test_scaled)
        y_prob = self.model.predict_proba(X_test_scaled)[:, 1]
        
        # Evaluation
        accuracy = accuracy_score(y_test, y_pred)
        
        print(f"\n📊 MODEL PERFORMANCE:")
        print(f"Accuracy: {accuracy:.3f}")
        print(f"Samples: {len(X)} total, {len(X_train)} train, {len(X_test)} test")
        print(f"\nClassification Report:")
        print(classification_report(y_test, y_pred))
        
        # Feature importance
        feature_importance = pd.DataFrame({
            'feature': feature_cols,
            'importance': self.model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        print(f"\n🔥 TOP 10 MOST IMPORTANT FEATURES:")
        for i, (_, row) in enumerate(feature_importance.head(10).iterrows()):
            print(f"{i+1:2d}. {row['feature']:<25} {row['importance']:.4f}")
        
        # High-confidence predictions
        high_conf_mask = y_prob > 0.7
        if high_conf_mask.sum() > 0:
            high_conf_accuracy = accuracy_score(y_test[high_conf_mask], y_pred[high_conf_mask])
            print(f"\n🎯 HIGH CONFIDENCE PREDICTIONS (>70% probability):")
            print(f"Count: {high_conf_mask.sum()} out of {len(y_test)} ({high_conf_mask.sum()/len(y_test)*100:.1f}%)")
            print(f"Accuracy: {high_conf_accuracy:.3f}")
        
        # Save model in UBJ format
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_filename = f"boom_comprehensive_xgboost_{target}_{timestamp}.ubj"
        scaler_filename = f"boom_comprehensive_scaler_{target}_{timestamp}.pkl"
        
        # Save XGBoost model in UBJ format
        self.model.save_model(model_filename)
        
        # Save scaler in pickle format
        with open(scaler_filename, 'wb') as f:
            pickle.dump(self.scaler, f)
        
        # Save feature names for later use
        features_filename = f"boom_comprehensive_features_{target}_{timestamp}.pkl"
        with open(features_filename, 'wb') as f:
            pickle.dump(feature_cols, f)
        
        print(f"\n💾 MODEL SAVED:")
        print(f"Model: {model_filename}")
        print(f"Scaler: {scaler_filename}")
        print(f"Features: {features_filename}")
        
        return self.model, feature_cols, feature_importance
    
    def simulate_trading_strategy(self, confidence_threshold=0.6):
        """Simulate trading strategy using the trained model"""
        print(f"\n💰 SIMULATING TRADING STRATEGY (confidence ≥ {confidence_threshold})")
        
        if self.model is None:
            print("No model trained yet!")
            return
        
        # Prepare all data for prediction
        X, y, feature_cols = self.prepare_ml_data()
        X_scaled = self.scaler.transform(X)
        
        # Get predictions and probabilities
        predictions = self.model.predict(X_scaled)
        probabilities = self.model.predict_proba(X_scaled)[:, 1]
        
        # High-confidence trading signals
        trade_signals = probabilities >= confidence_threshold
        
        # Simulate trades
        starting_capital = 20
        position_size = 1  # lots
        tick_value = 0.001
        current_capital = starting_capital
        trades = []
        
        clean_df = self.df[['mid_price', 'datetime'] + feature_cols + ['target_any_profit']].dropna()
        
        for i, (_, row) in enumerate(clean_df.iterrows()):
            if i >= len(trade_signals):
                break
                
            if trade_signals[i] and i < len(clean_df) - 100:  # Ensure we have exit data
                # Entry
                entry_price = row['mid_price']
                entry_time = row['datetime']
                
                # Exit after 50 ticks (or find optimal exit)
                exit_idx = min(i + 50, len(clean_df) - 1)
                exit_price = clean_df.iloc[exit_idx]['mid_price']
                exit_time = clean_df.iloc[exit_idx]['datetime']
                
                # P&L for SELL position
                price_change = entry_price - exit_price
                ticks_gained = price_change / 0.001  # tick size
                pnl = ticks_gained * tick_value * position_size
                
                current_capital += pnl
                
                trades.append({
                    'entry_time': entry_time,
                    'exit_time': exit_time,
                    'entry_price': entry_price,
                    'exit_price': exit_price,
                    'pnl': pnl,
                    'probability': probabilities[i],
                    'actual_target': row['target_any_profit'],
                    'capital': current_capital
                })
        
        if trades:
            trades_df = pd.DataFrame(trades)
            winning_trades = trades_df[trades_df['pnl'] > 0]
            
            print(f"\n📈 TRADING SIMULATION RESULTS:")
            print(f"Total trades: {len(trades_df)}")
            print(f"Winning trades: {len(winning_trades)} ({len(winning_trades)/len(trades_df)*100:.1f}%)")
            print(f"Starting capital: ${starting_capital:.2f}")
            print(f"Final capital: ${current_capital:.2f}")
            print(f"Total return: {(current_capital - starting_capital) / starting_capital * 100:.1f}%")
            print(f"Average trade: ${trades_df['pnl'].mean():.2f}")
            print(f"Best trade: ${trades_df['pnl'].max():.2f}")
            print(f"Worst trade: ${trades_df['pnl'].min():.2f}")
            
            return trades_df
        else:
            print("No trades generated!")
            return None

def main():
    """Run comprehensive XGBoost analysis"""
    print("🚀 COMPREHENSIVE BOOM 1000 INDEX XGBOOST ANALYSIS")
    print("="*60)
    
    csv_file = "C:\\Users\\<USER>\\META\\Boom_1000_Index_7days_20250619_20250626.csv"
    analyzer = ComprehensiveBoomAnalyzer(csv_file)
    
    # Load and process data
    analyzer.load_and_parse_data()
    analyzer.extract_comprehensive_features()
    analyzer.create_target_labels()
    
    # Train model for different targets
    targets = ['target_any_profit', 'target_conservative', 'target_moderate', 'target_aggressive']
    
    best_model = None
    best_accuracy = 0
    
    for target in targets:
        print(f"\n{'='*60}")
        print(f"TRAINING FOR TARGET: {target}")
        print('='*60)
        
        try:
            model, features, importance = analyzer.train_xgboost_model(target)
            
            # Keep the best model
            X, y, _ = analyzer.prepare_ml_data(target)
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
            X_test_scaled = analyzer.scaler.transform(X_test)
            accuracy = accuracy_score(y_test, model.predict(X_test_scaled))
            
            if accuracy > best_accuracy:
                best_accuracy = accuracy
                best_model = target
                
        except Exception as e:
            print(f"Error training {target}: {e}")
    
    print(f"\n🏆 BEST MODEL: {best_model} (Accuracy: {best_accuracy:.3f})")
    
    # Simulate trading with best model
    if best_model:
        analyzer.train_xgboost_model(best_model)  # Retrain best model
        analyzer.simulate_trading_strategy(confidence_threshold=0.6)
    
    return analyzer

if __name__ == "__main__":
    analyzer = main()
