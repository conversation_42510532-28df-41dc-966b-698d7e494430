"""
Enhanced Streamlined Backtest with Advanced Trade Data Collection
===============================================================

This file demonstrates how to integrate the enhanced trade data collection
structure with an existing backtest loop. It extends the original streamlined_backtest.py
to store comprehensive per-trade results suitable for advanced analytics.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import os
from collections import defaultdict
from enhanced_trade_data_collection import EnhancedTradeTracker

# Set up logging
log_dir = "strategy_logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)
    
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
log_filename = f"{log_dir}/enhanced_backtest_{timestamp}.log"

def log_message(message):
    """Log message to file and print to console"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_msg = f"[{timestamp}] {message}"
    print(log_msg)
    with open(log_filename, 'a') as f:
        f.write(log_msg + '\n')

def calculate_market_conditions(df, current_idx, lookback=20):
    """
    Calculate market conditions for the current bar
    
    Args:
        df: Market data DataFrame
        current_idx: Current bar index
        lookback: Number of bars to look back for calculations
    
    Returns:
        Dictionary of market conditions
    """
    start_idx = max(0, current_idx - lookback)
    window_data = df.iloc[start_idx:current_idx + 1]
    
    if len(window_data) < 2:
        return {}
    
    # Calculate volatility (price movement)
    price_changes = window_data['close'].pct_change().dropna()
    volatility = price_changes.std() if len(price_changes) > 1 else 0.0
    
    # Calculate trend (slope of prices)
    prices = window_data['close'].values
    x = np.arange(len(prices))
    if len(prices) > 1:
        trend_slope = np.polyfit(x, prices, 1)[0]
    else:
        trend_slope = 0.0
    
    # Determine trend direction
    if trend_slope > 0.001:
        trend_direction = 'up'
    elif trend_slope < -0.001:
        trend_direction = 'down'
    else:
        trend_direction = 'sideways'
    
    # Calculate momentum
    if len(window_data) >= 5:
        recent_avg = window_data['close'].tail(5).mean()
        older_avg = window_data['close'].head(5).mean()
        momentum = (recent_avg - older_avg) / older_avg if older_avg != 0 else 0.0
    else:
        momentum = 0.0
    
    return {
        'volatility': round(volatility, 4),
        'trend_slope': round(trend_slope, 4),
        'trend_direction': trend_direction,
        'momentum': round(momentum, 4),
        'price_level': round(window_data['close'].iloc[-1], 4)
    }

def calculate_mfe_mae(df, entry_idx, exit_idx, direction, entry_price):
    """
    Calculate Maximum Favorable Excursion (MFE) and Maximum Adverse Excursion (MAE)
    
    Args:
        df: Market data DataFrame
        entry_idx: Entry bar index
        exit_idx: Exit bar index
        direction: Trade direction ('LONG' or 'SHORT')
        entry_price: Entry price
    
    Returns:
        Tuple of (MFE, MAE)
    """
    trade_data = df.iloc[entry_idx:exit_idx + 1]
    
    if direction.upper() == 'LONG':
        # For LONG positions
        high_prices = trade_data['close'].max() if 'high' not in trade_data.columns else trade_data['high'].max()
        low_prices = trade_data['close'].min() if 'low' not in trade_data.columns else trade_data['low'].min()
        
        mfe = high_prices - entry_price  # Best case (highest price reached)
        mae = entry_price - low_prices   # Worst case (lowest price reached)
    else:
        # For SHORT positions
        high_prices = trade_data['close'].max() if 'high' not in trade_data.columns else trade_data['high'].max()
        low_prices = trade_data['close'].min() if 'low' not in trade_data.columns else trade_data['low'].min()
        
        mfe = entry_price - low_prices   # Best case (lowest price reached)
        mae = high_prices - entry_price  # Worst case (highest price reached)
    
    return max(0, mfe), max(0, mae)

def run_enhanced_backtest():
    """Run the enhanced backtest with comprehensive trade data collection"""
    
    log_message("=== ENHANCED STREAMLINED RENKO BACKTEST ===")
    
    # Load the existing predictions
    predictions_file = "strategy_logs/predictions_20250624_182556.csv"
    log_message(f"Loading predictions from {predictions_file}...")
    
    try:
        df = pd.read_csv(predictions_file)
        df['datetime'] = pd.to_datetime(df['datetime'])
        log_message(f"Loaded {len(df)} prediction rows")
    except FileNotFoundError:
        log_message(f"Predictions file not found. Creating sample data for demonstration.")
        # Create sample data for demonstration
        dates = pd.date_range(start='2024-01-01', periods=1000, freq='5min')
        np.random.seed(42)
        df = pd.DataFrame({
            'datetime': dates,
            'close': 100 + np.cumsum(np.random.randn(1000) * 0.01),
            'direction': np.random.choice(['up', 'down'], 1000),
            'prediction': np.random.choice([-1, 0, 1], 1000),
            'prob_long': np.random.uniform(0, 1, 1000),
            'prob_short': np.random.uniform(0, 1, 1000),
            'prob_neutral': np.random.uniform(0, 1, 1000)
        })
    
    # Initialize enhanced trade tracker
    initial_balance = 10000.0
    trade_tracker = EnhancedTradeTracker(initial_balance=initial_balance)
    
    # Strategy parameters
    CONFIDENCE_THRESHOLD = 0.02
    BRICK_SIZE = 0.05
    SPREAD = 0.0
    MIN_VOL = 0.10
    MAX_VOL_PER_POS = 50.0
    MAX_TOTAL_VOL = 200.0
    
    log_message(f"Using confidence threshold: {CONFIDENCE_THRESHOLD}")
    log_message(f"Starting balance: ${initial_balance}")
    
    # Track additional metrics
    open_volume = 0
    trade_count = 0
    
    # Enhanced backtest loop
    log_message(f"Starting enhanced backtest with {len(df)} bars...")
    
    i = 30  # Start after some data
    while i < len(df) - 10:
        current_row = df.iloc[i]
        
        # Check for trading signals
        should_trade = False
        position_type = None
        confidence_score = 0.0
        strategy_signal = ""
        
        # Enhanced signal detection with confidence scoring
        if current_row['prob_long'] > current_row['prob_short'] and current_row['prob_long'] >= CONFIDENCE_THRESHOLD:
            should_trade = True
            position_type = "LONG"
            confidence_score = current_row['prob_long']
            strategy_signal = "momentum_long"
        elif current_row['prob_short'] > current_row['prob_long'] and current_row['prob_short'] >= CONFIDENCE_THRESHOLD:
            should_trade = True
            position_type = "SHORT"
            confidence_score = current_row['prob_short']
            strategy_signal = "momentum_short"
        
        if should_trade:
            # Position sizing
            risk_percentage = 0.02  # Fixed 2% risk
            risk_amount = trade_tracker.current_balance * risk_percentage
            price_risk = 0.2 + SPREAD  # 2 bricks + spread
            lot_size = min(risk_amount / (price_risk * 10), MAX_VOL_PER_POS)
            
            if lot_size >= MIN_VOL and open_volume + lot_size <= MAX_TOTAL_VOL:
                # Execute trade
                entry_time = current_row['datetime']
                entry_price = current_row['close']
                open_volume += lot_size
                trade_count += 1
                
                # Calculate market conditions at entry
                market_conditions = calculate_market_conditions(df, i)
                
                log_message(f"Trade #{trade_count}: {position_type} at {entry_time}, price: {entry_price:.4f}, volume: {lot_size:.2f}")
                
                # Enhanced trade management
                tp_bricks = 5  # Take profit at 5 bricks
                sl_bricks = 2  # Stop loss at 2 bricks
                
                profit = 0
                outcome = None
                exit_price = entry_price
                exit_time = entry_time
                exit_idx = i
                commission = lot_size * 0.1  # Simple commission model
                
                # Look ahead for trade outcome with enhanced tracking
                entry_idx = i
                for j in range(i + 1, min(i + 20, len(df))):
                    current_direction = df.iloc[j]['direction']
                    current_price = df.iloc[j]['close']
                    
                    if position_type == "LONG":
                        if current_direction == 'up':
                            tp_bricks -= 1
                            if tp_bricks == 0:
                                profit = (0.5 - SPREAD) * 10 * lot_size
                                outcome = 'LONG_TP'
                                exit_price = current_price
                                exit_time = df.iloc[j]['datetime']
                                exit_idx = j
                                break
                        elif current_direction == 'down':
                            sl_bricks -= 1
                            if sl_bricks == 0:
                                profit = -(0.2 + SPREAD) * 10 * lot_size
                                outcome = 'LONG_SL'
                                exit_price = current_price
                                exit_time = df.iloc[j]['datetime']
                                exit_idx = j
                                break
                    
                    elif position_type == "SHORT":
                        if current_direction == 'down':
                            tp_bricks -= 1
                            if tp_bricks == 0:
                                profit = (0.5 - SPREAD) * 10 * lot_size
                                outcome = 'SHORT_TP'
                                exit_price = current_price
                                exit_time = df.iloc[j]['datetime']
                                exit_idx = j
                                break
                        elif current_direction == 'up':
                            sl_bricks -= 1
                            if sl_bricks == 0:
                                profit = -(0.2 + SPREAD) * 10 * lot_size
                                outcome = 'SHORT_SL'
                                exit_price = current_price
                                exit_time = df.iloc[j]['datetime']
                                exit_idx = j
                                break
                
                # Time-based exit if no other exit
                if profit == 0:
                    exit_idx = min(i + 10, len(df) - 1)
                    exit_price = df.iloc[exit_idx]['close']
                    exit_time = df.iloc[exit_idx]['datetime']
                    
                    if position_type == "LONG":
                        profit = (exit_price - entry_price) * 10 * lot_size
                    else:
                        profit = (entry_price - exit_price) * 10 * lot_size
                    outcome = 'TIME_EXIT'
                
                # Calculate MFE and MAE
                mfe, mae = calculate_mfe_mae(df, entry_idx, exit_idx, position_type, entry_price)
                
                # Record the trade using enhanced tracker
                trade_tracker.record_trade(
                    entry_time=entry_time,
                    exit_time=exit_time,
                    direction=position_type,
                    entry_price=entry_price,
                    exit_price=exit_price,
                    volume=lot_size,
                    commission=commission,
                    strategy_signal=strategy_signal,
                    confidence_score=confidence_score,
                    market_conditions=market_conditions,
                    mfe=mfe,
                    mae=mae
                )
                
                open_volume -= lot_size
                
                # Log trade result
                net_pnl = profit - commission
                log_message(f"Trade closed: {outcome}, Net P&L: ${net_pnl:.2f}, Balance: ${trade_tracker.current_balance:.2f}")
                
                # Jump ahead to avoid immediate re-entry
                i += 5
            else:
                i += 1
        else:
            i += 1
    
    # Generate comprehensive results
    log_message("Generating comprehensive results...")
    
    # Get the enhanced DataFrame
    trades_df = trade_tracker.get_trades_dataframe()
    
    if not trades_df.empty:
        # Save detailed results
        results_filename = f"{log_dir}/enhanced_results_{timestamp}.csv"
        trades_df.to_csv(results_filename, index=False)
        log_message(f"Saved {len(trades_df)} trades to {results_filename}")
        
        # Display key columns
        log_message("Sample of enhanced trade data:")
        sample_cols = ['trade_id', 'direction', 'net_pnl', 'cumulative_balance', 
                      'strategy_signal', 'confidence_score', 'win_streak', 'drawdown']
        available_cols = [col for col in sample_cols if col in trades_df.columns]
        log_message(trades_df[available_cols].head().to_string())
    
    # Get performance metrics
    metrics = trade_tracker.get_performance_metrics()
    
    # Enhanced results reporting
    log_message("\n=== ENHANCED BACKTEST RESULTS ===")
    log_message(f"Total Trades: {metrics.get('total_trades', 0)}")
    log_message(f"Final Balance: ${metrics.get('final_balance', 0):.2f}")
    log_message(f"Total Return: {metrics.get('total_return', 0)*100:.2f}%")
    log_message(f"Win Rate: {metrics.get('win_rate', 0)*100:.2f}%")
    log_message(f"Profit Factor: {metrics.get('profit_factor', 0):.2f}")
    log_message(f"Expectancy: ${metrics.get('expectancy', 0):.2f}")
    log_message(f"Max Win Streak: {metrics.get('max_win_streak', 0)}")
    log_message(f"Max Loss Streak: {metrics.get('max_loss_streak', 0)}")
    log_message(f"Max Drawdown: {metrics.get('max_drawdown', 0)*100:.2f}%")
    log_message(f"Sharpe Ratio: {metrics.get('sharpe_ratio', 0):.3f}")
    
    # Monte Carlo Analysis
    if not trades_df.empty and len(trades_df) > 5:
        log_message("\nRunning Monte Carlo simulation...")
        mc_results = trade_tracker.simulate_monte_carlo(num_simulations=1000)
        
        if not mc_results.empty:
            mc_filename = f"{log_dir}/monte_carlo_{timestamp}.csv"
            mc_results.to_csv(mc_filename, index=False)
            
            log_message("Monte Carlo Results Summary:")
            log_message(f"Simulations run: {len(mc_results)}")
            log_message(f"Average return: {mc_results['total_return'].mean()*100:.2f}%")
            log_message(f"Return std dev: {mc_results['total_return'].std()*100:.2f}%")
            log_message(f"Worst case return: {mc_results['total_return'].min()*100:.2f}%")
            log_message(f"Best case return: {mc_results['total_return'].max()*100:.2f}%")
            log_message(f"Average max drawdown: {mc_results['max_drawdown'].mean()*100:.2f}%")
            log_message(f"Worst drawdown: {mc_results['max_drawdown'].max()*100:.2f}%")
            log_message(f"Saved Monte Carlo results to {mc_filename}")
    
    # Create enhanced visualizations
    if not trades_df.empty:
        create_enhanced_plots(trades_df, trade_tracker, timestamp)
    
    log_message("Enhanced backtest completed!")
    return trade_tracker, trades_df, metrics

def create_enhanced_plots(trades_df, trade_tracker, timestamp):
    """Create enhanced visualizations"""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Enhanced Backtest Analysis', fontsize=16)
    
    # Equity curve
    axes[0, 0].plot(trades_df['cumulative_balance'])
    axes[0, 0].set_title('Equity Curve')
    axes[0, 0].set_xlabel('Trade Number')
    axes[0, 0].set_ylabel('Balance ($)')
    axes[0, 0].grid(True)
    
    # Drawdown curve
    axes[0, 1].fill_between(range(len(trades_df)), trades_df['drawdown'] * 100, alpha=0.3, color='red')
    axes[0, 1].set_title('Drawdown Curve')
    axes[0, 1].set_xlabel('Trade Number')
    axes[0, 1].set_ylabel('Drawdown (%)')
    axes[0, 1].grid(True)
    
    # P&L distribution
    axes[1, 0].hist(trades_df['net_pnl'], bins=30, alpha=0.7, edgecolor='black')
    axes[1, 0].set_title('P&L Distribution')
    axes[1, 0].set_xlabel('Net P&L ($)')
    axes[1, 0].set_ylabel('Frequency')
    axes[1, 0].grid(True)
    
    # Rolling win rate
    axes[1, 1].plot(trades_df['rolling_win_rate_10'] * 100)
    axes[1, 1].set_title('Rolling Win Rate (10 trades)')
    axes[1, 1].set_xlabel('Trade Number')
    axes[1, 1].set_ylabel('Win Rate (%)')
    axes[1, 1].grid(True)
    
    plt.tight_layout()
    plot_filename = f"{log_dir}/enhanced_analysis_{timestamp}.png"
    plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
    plt.close()
    
    log_message(f"Saved enhanced analysis plots to {plot_filename}")

if __name__ == "__main__":
    # Run the enhanced backtest
    tracker, trades_df, metrics = run_enhanced_backtest()
    
    # Additional analysis examples
    if not trades_df.empty:
        print("\n=== Additional Analysis Examples ===")
        
        # Streak analysis
        max_win_streak = trades_df['win_streak'].max()
        max_loss_streak = trades_df['loss_streak'].max()
        print(f"Longest win streak: {max_win_streak}")
        print(f"Longest loss streak: {max_loss_streak}")
        
        # Performance by signal type
        signal_performance = trades_df.groupby('strategy_signal')['net_pnl'].agg(['count', 'mean', 'sum'])
        print("\nPerformance by signal type:")
        print(signal_performance)
        
        # Performance by confidence level
        trades_df['confidence_bucket'] = pd.cut(trades_df['confidence_score'], 
                                              bins=[0, 0.3, 0.6, 1.0], 
                                              labels=['Low', 'Medium', 'High'])
        confidence_performance = trades_df.groupby('confidence_bucket')['net_pnl'].agg(['count', 'mean'])
        print("\nPerformance by confidence level:")
        print(confidence_performance)
        
        print("\nData is now ready for advanced analytics:")
        print("- Streak analysis: Use 'win_streak' and 'loss_streak' columns")
        print("- Drawdown analysis: Use 'drawdown' and 'running_max_balance' columns") 
        print("- Monte Carlo: Use the simulate_monte_carlo() method")
        print("- Risk analysis: Use 'max_adverse_excursion' and 'max_favorable_excursion' columns")
