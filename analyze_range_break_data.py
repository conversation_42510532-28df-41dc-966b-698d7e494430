import pandas as pd
import numpy as np

def analyze_range_break_data(file_path):
    """
    Analyzes the Range Break 100 Index.0 tick data for ranges and spikes.
    """
    print(f"Loading data from {file_path}...")
    try:
        df = pd.read_csv(file_path)
        print("Data loaded successfully.")
    except FileNotFoundError:
        print(f"Error: File not found at {file_path}")
        return
    except Exception as e:
        print(f"Error loading data: {e}")
        return

    # Ensure 'time' column is datetime
    df['time'] = pd.to_datetime(df['time'])
    df = df.sort_values('time').reset_index(drop=True)

    print("\n--- Basic Price Statistics ---")
    print(f"Bid Price - Min: {df['bid'].min():.2f}, Max: {df['bid'].max():.2f}, Mean: {df['bid'].mean():.2f}")
    print(f"Ask Price - Min: {df['ask'].min():.2f}, Max: {df['ask'].max():.2f}, Mean: {df['ask'].mean():.2f}")

    print("\n--- Spike Analysis ---")
    # Calculate percentage change in bid price
    df['bid_change_pct'] = df['bid'].pct_change() * 100

    # Define a spike threshold (e.g., 0.1% change in a single tick)
    spike_threshold_pct = 0.1
    spikes = df[df['bid_change_pct'].abs() > spike_threshold_pct]

    print(f"Identified {len(spikes)} spikes (absolute bid change > {spike_threshold_pct:.2f}%).")
    if not spikes.empty:
        print("Sample Spikes:")
        print(spikes[['time', 'bid', 'bid_change_pct']].head())

    print("\n--- Minute-level Range Analysis ---")
    # Resample to 1-minute OHLC to get minute-level ranges
    # Using 'bid' price for simplicity
    minute_ohlc = df.set_index('time')['bid'].resample('1min').ohlc()
    minute_ohlc['range'] = minute_ohlc['high'] - minute_ohlc['low']
    
    # Filter out minutes with no data (NaN ranges)
    minute_ohlc = minute_ohlc.dropna(subset=['range'])

    print(f"Analyzed {len(minute_ohlc)} one-minute intervals.")
    print(f"Minute Range - Min: {minute_ohlc['range'].min():.2f}, Max: {minute_ohlc['range'].max():.2f}, Mean: {minute_ohlc['range'].mean():.2f}")

    print("\n--- Range Occurrence Analysis ---")
    # Group ranges into bins for occurrence analysis
    # Determine appropriate bin edges based on data distribution
    min_range = minute_ohlc['range'].min()
    max_range = minute_ohlc['range'].max()
    
    # Create 10 bins, or fewer if the range is very small
    num_bins = min(10, int(max_range - min_range) + 1) if max_range > min_range else 1
    bins = np.linspace(min_range, max_range, num_bins + 1)
    
    range_occurrence = pd.cut(minute_ohlc['range'], bins=bins, include_lowest=True).value_counts().sort_index()
    range_occurrence_pct = (range_occurrence / len(minute_ohlc)) * 100

    print("Minute Range Occurrences (Count and Percentage):")
    for interval, count in range_occurrence.items():
        print(f"  {interval}: {count} ({range_occurrence_pct[interval]:.2f}%)")

if __name__ == "__main__":
    data_file = "Range_Break_100_7days_20250623_20250630.csv"
    analyze_range_break_data(data_file)
