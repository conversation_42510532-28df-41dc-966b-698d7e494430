import pytest
import pandas as pd
from streak_analysis import detect_consecutive_streaks, analyze_win_loss_streaks

# Sample data for testing
@pytest.fixture
def trading_data():
    return pd.DataFrame({
        'trade_id': range(1, 21),
        'is_winner': [True, True, False, False, False, True, True, True, True, False, 
                      False, True, True, False, True, True, True, True, True, False],
        'is_loser': [False, False, True, True, True, False, False, False, False, True,
                     True, False, False, True, False, False, False, False, False, True],
        'pnl': [100, 150, -50, -75, -25, 200, 125, 175, 90, -60,
                -40, 110, 130, -80, 95, 105, 120, 140, 160, -45]
    })

def test_detect_consecutive_streaks(trading_data):
    results = trading_data['is_winner']
    streaks = detect_consecutive_streaks(results)
    expected_streaks = [('win', 2), ('loss', 3), ('win', 4), ('loss', 2), ('win', 1), ('loss', 1), ('win', 5), ('loss', 1)]
    assert streaks == expected_streaks


def test_analyze_win_loss_streaks(trading_data):
    analysis = analyze_win_loss_streaks(trading_data)
    assert analysis['longest_win_streak'] == 5
    assert analysis['longest_loss_streak'] == 3
    assert analysis['total_streaks'] == 8
    expected_win_distribution = {2: 1, 4: 1, 1: 1, 5: 1}
    assert analysis['win_streak_distribution'] == expected_win_distribution
    expected_loss_distribution = {3: 1, 2: 1, 1: 1}
    assert analysis['loss_streak_distribution'] == expected_loss_distribution

