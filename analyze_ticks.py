import pandas as pd
import matplotlib.pyplot as plt

def analyze_tick_data(file_path):
    """
    Analyzes tick data from a CSV file.

    Args:
        file_path (str): The path to the CSV file.
    """
    try:
        df = pd.read_csv(file_path)

        print("--- DataFrame Info ---")
        df.info()

        print("\n--- First 5 Rows ---")
        print(df.head())

        print("\n--- Descriptive Statistics for Price ---")
        print(df['price'].describe())

        # Convert 'datetime' column to datetime objects
        df['datetime'] = pd.to_datetime(df['datetime'])

        # Plotting price over time
        plt.figure(figsize=(12, 6))
        plt.plot(df['datetime'], df['price'])
        plt.xlabel('Date Time')
        plt.ylabel('Price')
        plt.title('Price Over Time')
        plt.grid(True)
        plt.tight_layout()
        plt.show()

    except FileNotFoundError:
        print(f"Error: File not found at {file_path}")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    csv_file = r"C:\Users\<USER>\META\stpRNG_90days_ticks.csv"
    analyze_tick_data(csv_file)

