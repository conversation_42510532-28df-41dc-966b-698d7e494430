import pandas as pd
import numpy as np

def calculate_rsi(df, column='close', period=14):
    delta = df[column].diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)

    avg_gain = gain.ewm(com=period-1, adjust=False).mean()
    avg_loss = loss.ewm(com=period-1, adjust=False).mean()

    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def calculate_sma(df, column, period):
    return df[column].rolling(window=period).mean()

def calculate_bollinger_bands(df, column, window=20, num_std_dev=2):
    rolling_mean = df[column].rolling(window=window).mean()
    rolling_std = df[column].rolling(window=window).std()
    upper_band = rolling_mean + (rolling_std * num_std_dev)
    lower_band = rolling_mean - (rolling_std * num_std_dev)
    return upper_band, lower_band

def calculate_tdi(df, rsi_period=14, rsi_price_line_period=2, trade_signal_line_period=7, market_base_line_period=34, volatility_band_period=34, volatility_band_std_dev=1.618):
    # 1. Calculate RSI Line (Green Line)
    df['rsi'] = calculate_rsi(df, column='close', period=rsi_period)

    # 2. Calculate Trade Signal Line (Red Line) - SMA of RSI Line
    df['trade_signal_line'] = calculate_sma(df, 'rsi', trade_signal_line_period)

    # 3. Calculate Market Base Line (Yellow Line) - SMA of RSI Line
    df['market_base_line'] = calculate_sma(df, 'rsi', market_base_line_period)

    # 4. Calculate Volatility Bands (Blue Lines) - Bollinger Bands on RSI Line
    df['upper_band'], df['lower_band'] = calculate_bollinger_bands(df, 'rsi', window=volatility_band_period, num_std_dev=volatility_band_std_dev)

    return df

if __name__ == "__main__":
    # Example Usage with stpRNG_90days_ticks.csv
    df_ticks = pd.read_csv(r"C:\Users\<USER>\META\stpRNG_90days_ticks.csv")
    df_ticks['datetime'] = pd.to_datetime(df_ticks['datetime'])
    df_ticks = df_ticks.set_index('datetime')

    # Resample to 1-minute OHLC for TDI calculation
    ohlc_1min = df_ticks['price'].resample('1min').ohlc()
    ohlc_1min = ohlc_1min.dropna()

    # Calculate TDI on 1-minute OHLC data
    ohlc_1min_with_tdi = calculate_tdi(ohlc_1min.copy())

    print("\n--- 1-Minute OHLC with TDI (Tail) ---")
    print(ohlc_1min_with_tdi.tail())

