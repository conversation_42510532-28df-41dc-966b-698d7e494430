"""
DETAILED Boom 1000 Index Post-Spike Analysis
Focus on LOSING TRADES and ACCURATE balance deterioration
Analyzes price movements that go against the SELL position
"""

import pandas as pd
import numpy as np
from datetime import datetime

class DetailedBoomAnalyzer:
    def __init__(self, csv_file, starting_capital=20):
        self.csv_file = csv_file
        self.starting_capital = starting_capital
        self.current_capital = starting_capital
        
        # Boom 1000 Index specifications
        self.min_volume = 0.20  # lots
        self.max_volume = 50.0  # lots
        self.volume_limit = 120.0  # lots for multiple entries
        self.tick_size = 0.001  # 0.001 for Boom 1000
        self.tick_value = 0.001  # $0.001 per tick per lot
        self.spread = 0.0  # Zero spread as specified
        
        # Strategy parameters
        self.collection_window = 100  # ticks to hold position after spike
        
        # Results tracking
        self.trades = []
        self.equity_curve = []
        self.df = None
        self.spikes = []
        
    def load_and_parse_data(self):
        """Load and parse the tick data"""
        print("Loading tick data for DETAILED analysis...")
        
        raw_df = pd.read_csv(self.csv_file)
        parsed_data = []
        
        for _, row in raw_df.iterrows():
            try:
                tuple_str = row['0']
                if tuple_str.startswith('(') and tuple_str.endswith(')'):
                    values = tuple_str[1:-1].split(', ')
                    
                    tick_data = {
                        'timestamp': int(values[0]),
                        'bid': float(values[1]),
                        'ask': float(values[2]),
                        'last': float(values[3]),
                        'volume': int(values[4]),
                        'timestamp_msc': int(values[5]),
                        'flags': int(values[6]),
                        'volume_real': float(values[7]),
                        'symbol': row['symbol']
                    }
                    parsed_data.append(tick_data)
            except:
                continue
        
        self.df = pd.DataFrame(parsed_data)
        self.df['datetime'] = pd.to_datetime(self.df['timestamp'], unit='s')
        self.df['datetime_msc'] = pd.to_datetime(self.df['timestamp_msc'], unit='ms')
        self.df['mid_price'] = (self.df['bid'] + self.df['ask']) / 2
        self.df = self.df.sort_values('timestamp_msc').reset_index(drop=True)
        
        print(f"Loaded {len(self.df)} ticks for detailed analysis")
        return self.df
    
    def detect_spikes(self):
        """Detect spikes using the same enhanced method"""
        print("Detecting spikes for detailed analysis...")
        
        # Calculate price changes
        self.df['price_change'] = self.df['mid_price'].diff()
        self.df['price_change_pct'] = (self.df['price_change'] / self.df['mid_price'].shift(1)) * 100
        
        # Rolling statistics
        window = 100
        self.df['rolling_mean'] = self.df['price_change'].rolling(window=window).mean()
        self.df['rolling_std'] = self.df['price_change'].rolling(window=window).std()
        
        # Multiple spike detection methods
        pct_spikes = self.df[
            (self.df['price_change_pct'] > 0.1) & 
            (self.df['price_change'] > 0)
        ].index.tolist()
        
        std_threshold = 2.5
        std_spikes = self.df[
            (self.df['price_change'] > self.df['rolling_mean'] + std_threshold * self.df['rolling_std']) &
            (self.df['price_change'] > 0)
        ].index.tolist()
        
        abs_threshold = 0.5
        abs_spikes = self.df[
            (self.df['price_change'] > abs_threshold)
        ].index.tolist()
        
        # Combine and filter
        all_spike_candidates = list(set(pct_spikes + std_spikes + abs_spikes))
        all_spike_candidates.sort()
        
        filtered_spikes = []
        min_distance = 5
        
        for spike_idx in all_spike_candidates:
            if not filtered_spikes or spike_idx - filtered_spikes[-1] >= min_distance:
                filtered_spikes.append(spike_idx)
        
        self.spikes = filtered_spikes
        self.df['is_spike'] = False
        self.df.loc[self.spikes, 'is_spike'] = True
        
        print(f"Detected {len(self.spikes)} spikes for detailed analysis")
        return self.spikes
    
    def calculate_position_size(self):
        """Calculate position size based on current capital"""
        # Fixed position size for consistent analysis
        return 20.0  # Use fixed 20 lots for clearer analysis
    
    def execute_detailed_strategy(self):
        """Execute SELL-ONLY strategy with detailed tracking"""
        print("Executing DETAILED SELL-ONLY post-spike strategy...")
        print("Tracking: Price movements, adverse moves, exact balance calculations")
        
        self.current_capital = self.starting_capital
        self.equity_curve = [self.current_capital]
        
        for spike_idx in self.spikes:
            # Check if we have enough data after the spike
            if spike_idx + self.collection_window >= len(self.df):
                continue
            
            # Entry: Immediately after spike (SELL position)
            entry_idx = spike_idx + 1
            exit_idx = min(spike_idx + self.collection_window, len(self.df) - 1)
            
            # Track price movements during the trade
            trade_period = self.df.iloc[entry_idx:exit_idx + 1].copy()
            
            # Entry and exit prices for SELL position
            entry_price = self.df.iloc[entry_idx]['ask']  # Sell at ask price
            exit_price = self.df.iloc[exit_idx]['bid']    # Buy back at bid price
            
            # Position size
            position_size = self.calculate_position_size()
            
            # Calculate P&L for SELL position
            price_change = entry_price - exit_price  # Positive when price drops (profit)
            ticks_gained = price_change / self.tick_size
            pnl = ticks_gained * self.tick_value * position_size
            
            # Detailed price movement analysis
            highest_price = trade_period['ask'].max()  # Worst case for SELL position
            lowest_price = trade_period['bid'].min()   # Best case for SELL position
            
            # Calculate maximum adverse excursion (MAE) and maximum favorable excursion (MFE)
            max_adverse_move = highest_price - entry_price  # How much price went against us
            max_favorable_move = entry_price - lowest_price  # How much price went in our favor
            
            # Calculate unrealized P&L at worst point
            max_adverse_ticks = max_adverse_move / self.tick_size
            max_unrealized_loss = -(max_adverse_ticks * self.tick_value * position_size)
            
            # Calculate if this would have caused margin call or account blow-up
            lowest_account_value = self.current_capital + max_unrealized_loss
            
            # Update capital
            previous_capital = self.current_capital
            self.current_capital += pnl
            
            # Record detailed trade information
            trade = {
                'trade_number': len(self.trades) + 1,
                'spike_idx': spike_idx,
                'direction': 'SELL',
                'entry_time': self.df.iloc[entry_idx]['datetime'],
                'exit_time': self.df.iloc[exit_idx]['datetime'],
                'entry_price': entry_price,
                'exit_price': exit_price,
                'position_size': position_size,
                'price_change': price_change,
                'ticks_gained': ticks_gained,
                'pnl': pnl,
                'previous_capital': previous_capital,
                'capital_after': self.current_capital,
                'spike_size': self.df.iloc[spike_idx]['price_change'],
                'spike_pct': self.df.iloc[spike_idx]['price_change_pct'],
                'duration_ticks': exit_idx - entry_idx,
                'reversion_success': price_change > 0,
                # Detailed analysis fields
                'highest_price_during_trade': highest_price,
                'lowest_price_during_trade': lowest_price,
                'max_adverse_move': max_adverse_move,
                'max_favorable_move': max_favorable_move,
                'max_adverse_ticks': max_adverse_ticks,
                'max_unrealized_loss': max_unrealized_loss,
                'lowest_account_value': lowest_account_value,
                'margin_call_risk': lowest_account_value < 0,
                'adverse_move_pct': (max_adverse_move / entry_price) * 100,
                'favorable_move_pct': (max_favorable_move / entry_price) * 100
            }
            
            self.trades.append(trade)
            self.equity_curve.append(self.current_capital)
        
        print(f"DETAILED backtesting completed: {len(self.trades)} trades executed")
        return self.trades
    
    def analyze_losing_trades(self):
        """Detailed analysis of losing trades"""
        if not self.trades:
            return None
        
        trades_df = pd.DataFrame(self.trades)
        losing_trades = trades_df[trades_df['pnl'] < 0].copy()
        
        if len(losing_trades) == 0:
            print("No losing trades found!")
            return None
        
        print(f"\n🔴 DETAILED LOSING TRADES ANALYSIS:")
        print(f"Total losing trades: {len(losing_trades)} out of {len(trades_df)} ({len(losing_trades)/len(trades_df)*100:.1f}%)")
        
        # Sort by loss size
        losing_trades = losing_trades.sort_values('pnl')
        
        print(f"\n💀 WORST LOSING TRADES:")
        for i, (_, trade) in enumerate(losing_trades.head(10).iterrows()):
            print(f"\nTrade {trade['trade_number']} - Loss: ${trade['pnl']:.2f}")
            print(f"  📅 Time: {trade['entry_time'].strftime('%m-%d %H:%M:%S')} → {trade['exit_time'].strftime('%H:%M:%S')}")
            print(f"  📈 Entry: ${trade['entry_price']:.3f} → Exit: ${trade['exit_price']:.3f}")
            print(f"  📊 Price Movement: {trade['price_change']:.3f} units ({trade['price_change']/trade['entry_price']*100:.3f}%)")
            print(f"  ⚠️  Max Adverse Move: {trade['max_adverse_move']:.3f} units ({trade['adverse_move_pct']:.3f}%)")
            print(f"  💥 Max Unrealized Loss: ${trade['max_unrealized_loss']:.2f}")
            print(f"  💰 Account Low Point: ${trade['lowest_account_value']:.2f}")
            print(f"  🚨 Margin Call Risk: {'YES' if trade['margin_call_risk'] else 'NO'}")
            print(f"  🎯 Original Spike: {trade['spike_pct']:.3f}%")
        
        # Summary statistics for losing trades
        total_loss = losing_trades['pnl'].sum()
        avg_loss = losing_trades['pnl'].mean()
        max_loss = losing_trades['pnl'].min()
        avg_adverse_move = losing_trades['adverse_move_pct'].mean()
        max_adverse_move = losing_trades['adverse_move_pct'].max()
        margin_call_count = sum(losing_trades['margin_call_risk'])
        
        print(f"\n📊 LOSING TRADES SUMMARY:")
        print(f"Total losses: ${total_loss:.2f}")
        print(f"Average loss: ${avg_loss:.2f}")
        print(f"Worst single loss: ${max_loss:.2f}")
        print(f"Average adverse move: {avg_adverse_move:.3f}%")
        print(f"Worst adverse move: {max_adverse_move:.3f}%")
        print(f"Trades with margin call risk: {margin_call_count}/{len(losing_trades)}")
        
        return losing_trades
    
    def print_detailed_results(self):
        """Print comprehensive results with focus on losing trades"""
        if not self.trades:
            print("No trades to analyze")
            return
        
        trades_df = pd.DataFrame(self.trades)
        
        # Basic statistics
        total_trades = len(trades_df)
        winning_trades = len(trades_df[trades_df['pnl'] > 0])
        losing_trades_count = len(trades_df[trades_df['pnl'] < 0])
        
        win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
        total_pnl = trades_df['pnl'].sum()
        total_return = ((self.current_capital - self.starting_capital) / self.starting_capital) * 100
        
        # Risk analysis
        margin_call_trades = sum(trades_df['margin_call_risk'])
        avg_adverse_move = trades_df['adverse_move_pct'].mean()
        max_adverse_move = trades_df['adverse_move_pct'].max()
        
        print("\n" + "="*80)
        print("DETAILED BOOM 1000 INDEX SELL-ONLY STRATEGY ANALYSIS")
        print("="*80)
        
        print(f"\n📊 OVERALL PERFORMANCE:")
        print(f"{'Starting Capital:':<25} ${self.starting_capital:.2f}")
        print(f"{'Final Capital:':<25} ${self.current_capital:.2f}")
        print(f"{'Total Return:':<25} {total_return:.2f}%")
        print(f"{'Total P&L:':<25} ${total_pnl:.2f}")
        
        print(f"\n📈 TRADE BREAKDOWN:")
        print(f"{'Total Trades:':<25} {total_trades}")
        print(f"{'Winning Trades:':<25} {winning_trades} ({win_rate:.1f}%)")
        print(f"{'Losing Trades:':<25} {losing_trades_count} ({100-win_rate:.1f}%)")
        
        print(f"\n⚠️  RISK ANALYSIS:")
        print(f"{'Margin Call Risk Trades:':<25} {margin_call_trades}/{total_trades}")
        print(f"{'Average Adverse Move:':<25} {avg_adverse_move:.3f}%")
        print(f"{'Worst Adverse Move:':<25} {max_adverse_move:.3f}%")
        
        # Analyze losing trades in detail
        self.analyze_losing_trades()
        
        print("\n" + "="*80)

def main():
    """Run the detailed analysis"""
    print("Starting DETAILED Boom 1000 Index Analysis")
    print("Focus: Losing trades and accurate balance calculations")
    print("Starting Capital: $20")
    
    # Initialize analyzer
    csv_file = "C:\\Users\\<USER>\\META\\Boom_1000_Index_7days_20250619_20250626.csv"
    analyzer = DetailedBoomAnalyzer(csv_file, starting_capital=20)
    
    # Load data
    df = analyzer.load_and_parse_data()
    
    # Detect spikes
    spikes = analyzer.detect_spikes()
    
    # Execute detailed strategy
    trades = analyzer.execute_detailed_strategy()
    
    # Print detailed results
    analyzer.print_detailed_results()
    
    return analyzer

if __name__ == "__main__":
    analyzer = main()
