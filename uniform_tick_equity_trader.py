import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score
import warnings
warnings.filterwarnings('ignore')

class UniformTickEquityTrader:
    def __init__(self, data_file, initial_balance=10.0, initial_lot_size=0.20, 
                 equity_increase_threshold=0.02, uniform_threshold=0.0005):
        """
        Initialize the Uniform Tick Equity Trading System
        
        Args:
            data_file: Path to tick data CSV
            initial_balance: Starting balance in USD
            initial_lot_size: Starting lot size
            equity_increase_threshold: Percentage increase to compound lots (2% = 0.02)
            uniform_threshold: Threshold for uniform tick detection
        """
        self.data_file = data_file
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        self.initial_lot_size = initial_lot_size
        self.current_lot_size = initial_lot_size
        self.equity_increase_threshold = equity_increase_threshold
        self.uniform_threshold = uniform_threshold
        
        # Trading variables
        self.model = None
        self.data = None
        self.features = None
        self.targets = None
        self.trades = []
        self.equity_curve = []
        
    def load_and_prepare_data(self):
        """Load tick data and prepare features"""
        print("Loading tick data...")
        
        # Try different delimiters
        for delimiter in [',', '\t', ';']:
            try:
                self.data = pd.read_csv(self.data_file, delimiter=delimiter)
                if len(self.data.columns) > 1:
                    break
            except:
                continue
        
        print(f"Loaded {len(self.data)} tick records")
        
        # Find price column (use bid price)
        price_col = None
        for col in ['Bid', 'bid', 'Price', 'price', 'Close', 'close']:
            if col in self.data.columns:
                price_col = col
                break
        
        if price_col is None:
            numeric_cols = self.data.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) > 0:
                price_col = numeric_cols[0]
        
        self.data['price'] = pd.to_numeric(self.data[price_col], errors='coerce')
        self.data = self.data.dropna(subset=['price']).reset_index(drop=True)
        
        print(f"Clean data: {len(self.data)} records using {price_col} as price")
        
    def create_uniform_targets(self):
        """Create uniform tick targets for prediction"""
        print("Creating uniform tick targets...")
        
        # Calculate price changes
        self.data['price_change'] = self.data['price'].diff()
        self.data['price_change_pct'] = self.data['price_change'] / self.data['price'].shift(1)
        
        # Detect uniform ticks (small price changes)
        self.data['is_uniform'] = (abs(self.data['price_change_pct']) <= self.uniform_threshold).astype(int)
        
        # Create forward-looking targets - only predict next uniform tick
        self.data['next_uniform'] = self.data['is_uniform'].shift(-1)
        
        # Create price direction for uniform ticks only
        self.data['next_price_direction'] = np.where(
            self.data['price'].shift(-1) > self.data['price'], 1,
            np.where(self.data['price'].shift(-1) < self.data['price'], -1, 0)
        )
        
        print(f"Uniform tick rate: {self.data['is_uniform'].mean():.4f}")
        
    def create_features(self):
        """Create focused trading features"""
        print("Creating trading features...")
        
        # Price-based features (keep minimal for uniform tick prediction)
        for lag in [1, 2, 3]:
            self.data[f'price_lag_{lag}'] = self.data['price'].shift(lag)
            self.data[f'price_change_lag_{lag}'] = self.data['price_change'].shift(lag)
        
        # Rolling uniform behavior
        for window in [5, 10]:
            self.data[f'uniform_rate_{window}'] = self.data['is_uniform'].rolling(window).mean()
        
        # Recent uniform behavior
        for lag in [1, 2, 3]:
            self.data[f'uniform_lag_{lag}'] = self.data['is_uniform'].shift(lag)
        
        # Small volatility features
        self.data['volatility_5'] = self.data['price_change'].rolling(5).std()
        
        # Feature columns
        feature_cols = [col for col in self.data.columns if any(x in col for x in 
                       ['lag_', 'rate_', 'volatility', 'uniform_lag'])]
        
        self.features = self.data[feature_cols].copy()
        self.targets = self.data[['next_uniform', 'next_price_direction']].copy()
        
        # Remove NaN rows
        valid_mask = self.features.notna().all(axis=1) & self.targets.notna().all(axis=1)
        self.features = self.features[valid_mask]
        self.targets = self.targets[valid_mask]
        self.data = self.data[valid_mask].reset_index(drop=True)
        
        print(f"Features: {self.features.shape}")
        
    def train_uniform_model(self):
        """Train model to predict uniform ticks only"""
        print("Training uniform tick prediction model...")
        
        # Split data (70% train, 30% test)
        split_idx = int(len(self.features) * 0.7)
        
        X_train = self.features.iloc[:split_idx]
        X_test = self.features.iloc[split_idx:]
        y_train = self.targets['next_uniform'].iloc[:split_idx]
        y_test = self.targets['next_uniform'].iloc[split_idx:]
        
        # Train model
        self.model = RandomForestClassifier(n_estimators=50, random_state=42, max_depth=8)
        self.model.fit(X_train, y_train)
        
        # Evaluate
        y_pred = self.model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        
        print(f"Uniform tick prediction accuracy: {accuracy:.4f}")
        
        # Store test data for trading simulation
        self.test_start_idx = split_idx
        
    def calculate_lot_size(self, current_equity, base_equity):
        """Calculate lot size based on equity growth with 50 lot maximum cap"""
        if current_equity <= base_equity:
            return self.initial_lot_size
        
        # Calculate percentage increase
        equity_growth = (current_equity - base_equity) / base_equity
        
        # Increase lot size by 2% for every 2% equity increase
        lot_multiplier = 1 + (equity_growth // self.equity_increase_threshold) * self.equity_increase_threshold
        
        calculated_lot_size = round(self.initial_lot_size * lot_multiplier, 2)
        
        # Cap at maximum 50 lots
        return min(calculated_lot_size, 50.0)
    
    def simulate_uniform_trading(self):
        """Simulate trading using only uniform tick predictions"""
        print("Simulating uniform tick trading with equity management...")
        
        test_data = self.data.iloc[self.test_start_idx:].copy()
        test_features = self.features.iloc[self.test_start_idx:]
        
        # Generate uniform tick predictions
        uniform_pred_proba = self.model.predict_proba(test_features)[:, 1]
        test_data['uniform_prob'] = uniform_pred_proba
        
        # Trading simulation variables
        trades = []
        current_balance = self.initial_balance
        current_lot_size = self.initial_lot_size
        equity_curve = [{'tick': 0, 'balance': current_balance, 'lot_size': current_lot_size}]
        
        position = 0
        entry_price = 0
        entry_tick = 0
        last_balance_check = self.initial_balance
        
        # Trading parameters for Boom 1000
        uniform_prob_threshold = 0.8  # High confidence for uniform ticks
        tick_size = 0.1  # Boom 1000 tick size
        tick_value = 0.10  # $0.10 per tick (0.1 point) for 1 lot
        point_value = 1.0  # $1 per full point for 1 lot
        max_trade_duration = 3  # Close after 3 ticks max
        
        print(f"Starting trading with ${current_balance:.2f} and {current_lot_size} lots")
        print(f"Target: Trade only high-probability uniform ticks (>{uniform_prob_threshold*100}%)")
        
        for i in range(len(test_data)):
            current_price = test_data.iloc[i]['price']
            uniform_prob = test_data.iloc[i]['uniform_prob']
            price_direction = test_data.iloc[i]['next_price_direction']
            
            # Close existing position
            if position != 0:
                trade_duration = i - entry_tick
                price_change = current_price - entry_price
                
                # Close position after max duration or if direction changes
                if trade_duration >= max_trade_duration:
                    # Calculate profit/loss in points for Boom 1000
                    # Boom 1000: $1 per point for 1 lot
                    if position == 1:  # Long position
                        profit_loss = price_change * point_value * current_lot_size
                    else:  # Short position
                        profit_loss = -price_change * point_value * current_lot_size
                    
                    current_balance += profit_loss
                    points_gained = price_change if position == 1 else -price_change
                    
                    trades.append({
                        'entry_tick': entry_tick,
                        'exit_tick': i,
                        'entry_price': entry_price,
                        'exit_price': current_price,
                        'position': position,
                        'lot_size': current_lot_size,
                        'points': points_gained,
                        'profit_loss': profit_loss,
                        'balance': current_balance,
                        'duration': trade_duration
                    })
                    
                    # Check for lot size increase (every 2% equity growth)
                    if current_balance >= last_balance_check * (1 + self.equity_increase_threshold):
                        new_lot_size = self.calculate_lot_size(current_balance, self.initial_balance)
                        if new_lot_size > current_lot_size:
                            if new_lot_size >= 50.0:
                                print(f"Equity grew {((current_balance/self.initial_balance-1)*100):.1f}% - Lot size capped at maximum 50.0 lots")
                            else:
                                print(f"Equity grew {((current_balance/self.initial_balance-1)*100):.1f}% - Increasing lot size from {current_lot_size} to {new_lot_size}")
                            current_lot_size = new_lot_size
                            last_balance_check = current_balance
                    
                    equity_curve.append({
                        'tick': i,
                        'balance': current_balance,
                        'lot_size': current_lot_size
                    })
                    
                    position = 0
            
            # Open new position on high-probability uniform tick signals
            if position == 0 and uniform_prob > uniform_prob_threshold and price_direction != 0:
                position = price_direction  # 1 for long, -1 for short
                entry_price = current_price
                entry_tick = i
        
        # Close final position if open
        if position != 0:
            price_change = test_data.iloc[-1]['price'] - entry_price
            
            # Calculate profit/loss using Boom 1000 specifications
            if position == 1:  # Long position
                profit_loss = price_change * point_value * current_lot_size
            else:  # Short position
                profit_loss = -price_change * point_value * current_lot_size
                
            current_balance += profit_loss
            points_gained = price_change if position == 1 else -price_change
            
            trades.append({
                'entry_tick': entry_tick,
                'exit_tick': len(test_data)-1,
                'entry_price': entry_price,
                'exit_price': test_data.iloc[-1]['price'],
                'position': position,
                'lot_size': current_lot_size,
                'points': points_gained,
                'profit_loss': profit_loss,
                'balance': current_balance,
                'duration': len(test_data) - 1 - entry_tick
            })
        
        self.trades = pd.DataFrame(trades)
        self.equity_curve = pd.DataFrame(equity_curve)
        self.final_balance = current_balance
        
        return self.trades, self.equity_curve
    
    def calculate_performance_metrics(self):
        """Calculate comprehensive trading performance metrics"""
        if len(self.trades) == 0:
            return {"error": "No trades generated"}
        
        trades_df = self.trades
        
        # Basic metrics
        total_trades = len(trades_df)
        winning_trades = len(trades_df[trades_df['profit_loss'] > 0])
        losing_trades = len(trades_df[trades_df['profit_loss'] < 0])
        
        win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
        
        # Profit metrics
        total_profit = trades_df['profit_loss'].sum()
        total_points = trades_df['points'].sum()
        
        winning_trades_df = trades_df[trades_df['profit_loss'] > 0]
        losing_trades_df = trades_df[trades_df['profit_loss'] < 0]
        
        avg_win = winning_trades_df['profit_loss'].mean() if len(winning_trades_df) > 0 else 0
        avg_loss = losing_trades_df['profit_loss'].mean() if len(losing_trades_df) > 0 else 0
        
        profit_factor = abs(winning_trades_df['profit_loss'].sum() / losing_trades_df['profit_loss'].sum()) if len(losing_trades_df) > 0 else float('inf')
        
        # Equity metrics
        initial_balance = self.initial_balance
        final_balance = self.final_balance
        total_return = ((final_balance - initial_balance) / initial_balance) * 100
        
        # Risk metrics
        max_balance = trades_df['balance'].max()
        running_max = trades_df['balance'].expanding().max()
        drawdown = (trades_df['balance'] - running_max) / running_max * 100
        max_drawdown = drawdown.min()
        
        metrics = {
            'initial_balance': initial_balance,
            'final_balance': final_balance,
            'total_return_pct': total_return,
            'total_profit_usd': total_profit,
            'total_points': total_points,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate_pct': win_rate,
            'avg_win_usd': avg_win,
            'avg_loss_usd': avg_loss,
            'profit_factor': profit_factor,
            'max_profit_trade': trades_df['profit_loss'].max(),
            'max_loss_trade': trades_df['profit_loss'].min(),
            'max_drawdown_pct': max_drawdown,
            'avg_trade_duration': trades_df['duration'].mean(),
            'final_lot_size': trades_df['lot_size'].iloc[-1] if len(trades_df) > 0 else self.initial_lot_size
        }
        
        return metrics
    
    def run_uniform_trading_analysis(self):
        """Run the complete uniform tick trading analysis"""
        print("=== Uniform Tick Equity Trading System ===")
        print(f"Initial Balance: ${self.initial_balance}")
        print(f"Initial Lot Size: {self.initial_lot_size}")
        print(f"Equity Compounding: {self.equity_increase_threshold*100}% threshold")
        print("-" * 50)
        
        # Load and prepare data
        self.load_and_prepare_data()
        self.create_uniform_targets()
        self.create_features()
        
        # Train model
        self.train_uniform_model()
        
        # Simulate trading
        trades_df, equity_curve = self.simulate_uniform_trading()
        
        # Calculate performance
        metrics = self.calculate_performance_metrics()
        
        print("\n=== TRADING RESULTS ===")
        print(f"Final Balance: ${metrics['final_balance']:.2f}")
        print(f"Total Return: {metrics['total_return_pct']:.2f}%")
        print(f"Total Profit: ${metrics['total_profit_usd']:.2f}")
        print(f"Total Points: {metrics['total_points']:.1f}")
        print(f"Final Lot Size: {metrics['final_lot_size']}")
        print()
        print(f"Total Trades: {metrics['total_trades']}")
        print(f"Win Rate: {metrics['win_rate_pct']:.1f}%")
        print(f"Profit Factor: {metrics['profit_factor']:.2f}")
        print(f"Average Win: ${metrics['avg_win_usd']:.2f}")
        print(f"Average Loss: ${metrics['avg_loss_usd']:.2f}")
        print(f"Max Drawdown: {metrics['max_drawdown_pct']:.2f}%")
        print(f"Avg Trade Duration: {metrics['avg_trade_duration']:.1f} ticks")
        
        # Show last 10 trades
        if len(trades_df) > 0:
            print("\n=== LAST 10 TRADES ===")
            last_trades = trades_df.tail(10)[['entry_price', 'exit_price', 'position', 'lot_size', 'points', 'profit_loss', 'balance']]
            for i, trade in last_trades.iterrows():
                direction = "LONG" if trade['position'] == 1 else "SHORT"
                print(f"{direction} {trade['lot_size']} lots: {trade['entry_price']:.5f} → {trade['exit_price']:.5f} "
                      f"| {trade['points']:.3f} points | ${trade['profit_loss']:.2f} | Balance: ${trade['balance']:.2f}")
        
        print("\n=== Analysis Complete ===")
        
        return metrics, trades_df, equity_curve

if __name__ == "__main__":
    # Initialize and run the uniform tick trading system
    trader = UniformTickEquityTrader(
        data_file="Boom_1000_Index_7days_20250620_20250627.csv",
        initial_balance=10.0,           # Start with $10
        initial_lot_size=0.20,          # Start with 0.20 lots
        equity_increase_threshold=0.02,  # Increase lots every 2% equity growth
        uniform_threshold=0.0005        # 0.05% uniform tick threshold
    )
    
    metrics, trades, equity = trader.run_uniform_trading_analysis()
