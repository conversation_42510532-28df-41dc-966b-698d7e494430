"""
ENHANCED BOOM 1000 XGBOOST MODEL - LOSS MINIMIZATION
===================================================
Based on loss analysis findings, this enhanced model adds:
1. Price reversal detection features
2. Temporal risk features  
3. Market regime detection
4. Advanced momentum features
5. Volatility regime classification
6. Exit timing optimization features

Target: Reduce the 89 losses significantly while maintaining 99.9% win rate
"""

import pandas as pd
import numpy as np
import xgboost as xgb
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
from sklearn.preprocessing import StandardScaler
import warnings
import pickle
from datetime import datetime
warnings.filterwarnings('ignore')

class EnhancedBoomAnalyzer:
    def __init__(self, csv_file):
        self.csv_file = csv_file
        self.df = None
        self.model = None
        self.scaler = StandardScaler()
        
    def load_and_parse_data(self):
        """Load and parse raw tick data"""
        print("Loading and parsing tick data...")
        raw_df = pd.read_csv(self.csv_file)
        parsed_data = []
        
        for _, row in raw_df.iterrows():
            try:
                tuple_str = row['0']
                if tuple_str.startswith('(') and tuple_str.endswith(')'):
                    values = tuple_str[1:-1].split(', ')
                    parsed_data.append({
                        'timestamp': int(values[0]),
                        'bid': float(values[1]),
                        'ask': float(values[2]),
                        'mid_price': (float(values[1]) + float(values[2])) / 2,
                        'spread': float(values[2]) - float(values[1])
                    })
            except: continue
        
        self.df = pd.DataFrame(parsed_data)
        self.df['datetime'] = pd.to_datetime(self.df['timestamp'], unit='s')
        self.df = self.df.sort_values('timestamp').reset_index(drop=True)
        print(f"Loaded {len(self.df)} ticks")
        
    def create_base_features(self):
        """Create original features from comprehensive model"""
        print("Creating base features...")
        
        # Basic price features
        self.df['price_change'] = self.df['mid_price'].diff()
        self.df['price_change_pct'] = (self.df['price_change'] / self.df['mid_price'].shift(1)) * 100
        self.df['log_return'] = np.log(self.df['mid_price'] / self.df['mid_price'].shift(1))
        
        # Volatility features
        for window in [5, 10, 20, 50]:
            self.df[f'volatility_{window}'] = self.df['price_change'].rolling(window=window).std()
            self.df[f'rolling_mean_{window}'] = self.df['mid_price'].rolling(window=window).mean()
            self.df[f'rolling_max_{window}'] = self.df['mid_price'].rolling(window=window).max()
            self.df[f'rolling_min_{window}'] = self.df['mid_price'].rolling(window=window).min()
            self.df[f'price_position_{window}'] = (self.df['mid_price'] - self.df[f'rolling_min_{window}']) / (self.df[f'rolling_max_{window}'] - self.df[f'rolling_min_{window}'])
        
        # Momentum features
        for period in [3, 5, 10, 20]:
            self.df[f'momentum_{period}'] = self.df['mid_price'] - self.df['mid_price'].shift(period)
            self.df[f'roc_{period}'] = ((self.df['mid_price'] / self.df['mid_price'].shift(period)) - 1) * 100
        
        # RSI calculation
        for period in [7, 14, 21]:
            delta = self.df['mid_price'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            self.df[f'rsi_{period}'] = 100 - (100 / (1 + rs))
        
        # Spike detection
        self.df['is_spike'] = ((self.df['price_change_pct'] > 0.1) & (self.df['price_change'] > 0)).astype(int)
        self.df['spike_magnitude'] = np.where(self.df['is_spike'] == 1, self.df['price_change'], 0)
        
        # Ticks since last spike
        spike_indices = self.df[self.df['is_spike'] == 1].index
        self.df['ticks_since_spike'] = 0
        for i in range(len(self.df)):
            last_spike = spike_indices[spike_indices < i]
            if len(last_spike) > 0:
                self.df.iloc[i, self.df.columns.get_loc('ticks_since_spike')] = i - last_spike[-1]
            else:
                self.df.iloc[i, self.df.columns.get_loc('ticks_since_spike')] = i
        
        # Support/Resistance levels
        for window in [20, 50]:
            self.df[f'support_{window}'] = self.df['mid_price'].rolling(window=window).min()
            self.df[f'resistance_{window}'] = self.df['mid_price'].rolling(window=window).max()
        
        # Moving average crossovers
        for fast, slow in [(5, 20), (10, 50)]:
            ma_fast = self.df['mid_price'].rolling(window=fast).mean()
            ma_slow = self.df['mid_price'].rolling(window=slow).mean()
            self.df[f'ma_cross_{fast}_{slow}'] = (ma_fast > ma_slow).astype(int)
            self.df[f'ma_distance_{fast}_{slow}'] = ma_fast - ma_slow
        
        # Price acceleration
        self.df['acceleration'] = self.df['price_change'].diff()
        
        # Time features
        self.df['hour'] = self.df['datetime'].dt.hour
        self.df['minute'] = self.df['datetime'].dt.minute
        self.df['second'] = self.df['datetime'].dt.second
        
        print("Base features created")
        
    def create_loss_minimization_features(self):
        """Create advanced features based on loss analysis to minimize losses"""
        print("Creating enhanced loss-minimization features...")
        
        # 1. PRICE REVERSAL DETECTION FEATURES
        # Strong momentum divergence detection (main cause of losses)
        self.df['momentum_divergence_3_10'] = self.df['momentum_3'] - self.df['momentum_10']
        self.df['momentum_divergence_5_20'] = self.df['momentum_5'] - self.df['momentum_20']
        self.df['momentum_acceleration'] = self.df['momentum_5'].diff()
        self.df['momentum_deceleration'] = (self.df['momentum_5'] - self.df['momentum_10']) / self.df['momentum_10']
        
        # MA distance ratios (critical loss indicator)
        self.df['ma_distance_ratio_10_50'] = self.df['ma_distance_10_50'] / self.df['volatility_10']
        self.df['ma_distance_ratio_5_20'] = self.df['ma_distance_5_20'] / self.df['volatility_5']
        self.df['ma_distance_trend'] = self.df['ma_distance_10_50'].diff()
        
        # Price position instability
        self.df['price_position_instability'] = self.df['price_position_5'].rolling(window=5).std()
        self.df['price_position_trend'] = self.df['price_position_20'].diff()
        
        # 2. VOLATILITY REGIME DETECTION
        # Volatility percentiles (high vol = high loss risk)
        for window in [10, 20, 50]:
            self.df[f'vol_percentile_{window}'] = self.df[f'volatility_{window}'].rolling(window=100).rank(pct=True)
            self.df[f'vol_regime_{window}'] = (self.df[f'vol_percentile_{window}'] > 0.75).astype(int)  # High vol regime
        
        # Volatility spike detection
        self.df['vol_spike_5'] = (self.df['volatility_5'] > self.df['volatility_5'].rolling(window=20).quantile(0.9)).astype(int)
        self.df['vol_spike_10'] = (self.df['volatility_10'] > self.df['volatility_10'].rolling(window=20).quantile(0.9)).astype(int)
        
        # 3. TEMPORAL RISK FEATURES (losses cluster at specific times)
        # Hour-based risk (30% of losses at hour 21)
        self.df['high_risk_hour'] = (self.df['hour'] == 21).astype(int)
        self.df['medium_risk_hour'] = ((self.df['hour'] == 14) | (self.df['hour'] == 5)).astype(int)
        
        # Day of week risk (75% of losses on Friday)
        self.df['day_of_week'] = self.df['datetime'].dt.day_of_week
        self.df['high_risk_day'] = (self.df['day_of_week'] == 4).astype(int)  # Friday = 4
        
        # 4. ADVANCED MOMENTUM FEATURES
        # Momentum consistency (reversals have inconsistent momentum)
        for period in [3, 5, 10]:
            self.df[f'momentum_consistency_{period}'] = (
                self.df[f'momentum_{period}'].rolling(window=5).apply(
                    lambda x: (x < 0).sum() / len(x)  # Proportion of negative momentum
                )
            )
        
        # Momentum strength relative to volatility
        for period in [5, 10, 20]:
            self.df[f'momentum_strength_{period}'] = abs(self.df[f'momentum_{period}']) / self.df[f'volatility_{period}']
        
        # 5. MARKET MICROSTRUCTURE FEATURES
        # Spread dynamics (might indicate reversals)
        self.df['spread_change'] = self.df['spread'].diff()
        self.df['spread_volatility'] = self.df['spread'].rolling(window=10).std()
        self.df['spread_percentile'] = self.df['spread'].rolling(window=50).rank(pct=True)
        
        # Tick-by-tick price behavior
        self.df['price_direction'] = np.sign(self.df['price_change'])
        self.df['direction_consistency'] = self.df['price_direction'].rolling(window=5).mean()
        self.df['direction_reversal'] = (self.df['price_direction'] != self.df['price_direction'].shift(1)).astype(int)
        
        # 6. SPIKE TIMING RISK FEATURES (losses concentrate 1000-2000 ticks after spike)
        self.df['spike_timing_risk'] = ((self.df['ticks_since_spike'] >= 1000) & 
                                       (self.df['ticks_since_spike'] <= 2000)).astype(int)
        self.df['spike_timing_medium_risk'] = ((self.df['ticks_since_spike'] >= 500) & 
                                              (self.df['ticks_since_spike'] <= 1000)).astype(int)
        
        # 7. REVERSAL CONFIRMATION FEATURES
        # RSI momentum (losses have RSI = 0, wins have positive RSI)
        self.df['rsi_momentum_7'] = self.df['rsi_7'].diff()
        self.df['rsi_momentum_14'] = self.df['rsi_14'].diff()
        self.df['rsi_divergence'] = self.df['rsi_7'] - self.df['rsi_14']
        
        # Price vs RSI divergence
        self.df['price_rsi_divergence'] = (
            np.sign(self.df['price_change']) != np.sign(self.df['rsi_momentum_14'])
        ).astype(int)
        
        # 8. CONFIDENCE ADJUSTMENT FEATURES
        # Multi-timeframe confirmation
        self.df['multi_timeframe_down'] = (
            (self.df['momentum_3'] < 0) & 
            (self.df['momentum_5'] < 0) & 
            (self.df['momentum_10'] < 0)
        ).astype(int)
        
        self.df['multi_timeframe_consistency'] = (
            (self.df['momentum_3'] < 0).astype(int) + 
            (self.df['momentum_5'] < 0).astype(int) + 
            (self.df['momentum_10'] < 0).astype(int) + 
            (self.df['momentum_20'] < 0).astype(int)
        ) / 4
        
        # 9. LOSS SEVERITY PREDICTION
        # Features to predict when large losses (>$2) might occur
        self.df['large_loss_risk'] = (
            (self.df['vol_regime_10'] == 1) & 
            (self.df['high_risk_hour'] == 1) & 
            (self.df['ma_distance_10_50'] > -0.1)
        ).astype(int)
        
        print(f"Enhanced features created. Total features: {len(self.df.columns)}")
        
    def create_target_labels(self):
        """Create target labels for any profit prediction"""
        print("Creating enhanced target labels...")
        
        # Enhanced target with stricter criteria to avoid losses
        targets = []
        
        for i in range(len(self.df) - 100):
            current_price = self.df.iloc[i]['mid_price']
            
            # Look ahead 50 ticks (same as original)
            future_slice = self.df.iloc[i+1:i+51]
            min_future_price = future_slice['mid_price'].min()
            
            # Standard target: Any profit
            profit_pct = (current_price - min_future_price) / current_price
            standard_target = 1 if profit_pct > 0.0 else 0
            
            # Enhanced loss avoidance: Additional checks
            # Check if this looks like a loss scenario
            current_row = self.df.iloc[i]
            
            loss_risk_score = 0
            
            # Risk factor 1: High volatility regime
            if hasattr(self, 'vol_regime_10') and current_row.get('vol_regime_10', 0) == 1:
                loss_risk_score += 1
                
            # Risk factor 2: High risk time
            if current_row.get('high_risk_hour', 0) == 1 or current_row.get('high_risk_day', 0) == 1:
                loss_risk_score += 1
                
            # Risk factor 3: Momentum divergence (main loss cause)
            if current_row.get('ma_distance_10_50', 0) > -0.05:  # Not strongly negative
                loss_risk_score += 1
                
            # Risk factor 4: Spike timing risk
            if current_row.get('spike_timing_risk', 0) == 1:
                loss_risk_score += 1
                
            # Enhanced target: Much more aggressive loss avoidance
            # Must have profit AND very low risk
            enhanced_target = 1 if (standard_target == 1 and loss_risk_score == 0) else 0
            
            targets.append(enhanced_target)
        
        # Pad with zeros for the last 100 ticks
        targets.extend([0] * 100)
        self.df['target_enhanced'] = targets
        
        # Also keep original target
        targets_original = []
        for i in range(len(self.df) - 100):
            current_price = self.df.iloc[i]['mid_price']
            future_slice = self.df.iloc[i+1:i+51]
            min_future_price = future_slice['mid_price'].min()
            profit_pct = (current_price - min_future_price) / current_price
            targets_original.append(1 if profit_pct > 0.0 else 0)
        
        targets_original.extend([0] * 100)
        self.df['target_original'] = targets_original
        
        # Show target statistics
        enhanced_positive = sum(targets[:-100])
        original_positive = sum(targets_original[:-100])
        total_valid = len(targets) - 100
        
        print(f"Original target: {original_positive}/{total_valid} ({original_positive/total_valid*100:.1f}%) positive")
        print(f"Enhanced target: {enhanced_positive}/{total_valid} ({enhanced_positive/total_valid*100:.1f}%) positive")
        print(f"Risk reduction: {(original_positive-enhanced_positive)/original_positive*100:.1f}% fewer signals")
        
        return enhanced_positive > 1000  # Need reasonable number of positive cases
        
    def train_enhanced_model(self, target='target_enhanced'):
        """Train enhanced XGBoost model"""
        print(f"Training enhanced model with {target}...")
        
        # Get all feature columns (exclude non-feature columns)
        exclude_cols = [
            'timestamp', 'bid', 'ask', 'mid_price', 'datetime', 'spread',
            'target_enhanced', 'target_original', 'day_of_week'
        ]
        feature_cols = [col for col in self.df.columns if col not in exclude_cols]
        
        # Prepare data
        clean_data = self.df[feature_cols + [target]].dropna()
        X = clean_data[feature_cols]
        y = clean_data[target]
        
        # Time-series split
        split_idx = int(len(clean_data) * 0.7)
        X_train = X.iloc[:split_idx]
        X_test = X.iloc[split_idx:]
        y_train = y.iloc[:split_idx]
        y_test = y.iloc[split_idx:]
        
        # Scale features
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # Train enhanced model with class balancing
        self.model = xgb.XGBClassifier(
            n_estimators=300,  # More trees for better pattern detection
            max_depth=6,       # Slightly deeper for complex patterns
            learning_rate=0.08,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42,
            eval_metric='logloss',
            early_stopping_rounds=15,
            scale_pos_weight=len(y_train[y_train == 0]) / len(y_train[y_train == 1])  # Handle imbalance
        )
        
        # Train with validation
        self.model.fit(
            X_train_scaled, y_train,
            eval_set=[(X_test_scaled, y_test)],
            verbose=False
        )
        
        # Evaluate
        y_pred = self.model.predict(X_test_scaled)
        y_prob = self.model.predict_proba(X_test_scaled)[:, 1]
        accuracy = accuracy_score(y_test, y_pred)
        
        print(f"\\n📊 ENHANCED MODEL PERFORMANCE:")
        print(f"Accuracy: {accuracy:.3f}")
        print(f"Samples: {len(X)} total, {len(X_train)} train, {len(X_test)} test")
        print(classification_report(y_test, y_pred))
        
        # Feature importance
        feature_importance = pd.DataFrame({
            'feature': feature_cols,
            'importance': self.model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        print(f"\\n🔥 TOP 20 ENHANCED FEATURES:")
        for i, (_, row) in enumerate(feature_importance.head(20).iterrows()):
            print(f"{i+1:2d}. {row['feature']:<30} {row['importance']:.4f}")
        
        # High confidence analysis
        high_conf_mask = y_prob > 0.8  # Higher threshold for enhanced model
        if high_conf_mask.sum() > 0:
            high_conf_acc = accuracy_score(y_test[high_conf_mask], y_pred[high_conf_mask])
            print(f"\\n🎯 HIGH CONFIDENCE (>80%): {high_conf_mask.sum()} samples, {high_conf_acc:.3f} accuracy")
        
        # Save enhanced model
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_filename = f"boom_enhanced_xgboost_{target}_{timestamp}.ubj"
        scaler_filename = f"boom_enhanced_scaler_{target}_{timestamp}.pkl"
        features_filename = f"boom_enhanced_features_{target}_{timestamp}.pkl"
        
        self.model.save_model(model_filename)
        with open(scaler_filename, 'wb') as f:
            pickle.dump(self.scaler, f)
        with open(features_filename, 'wb') as f:
            pickle.dump(feature_cols, f)
            
        print(f"\\n💾 ENHANCED MODEL SAVED:")
        print(f"Model: {model_filename}")
        print(f"Scaler: {scaler_filename}")
        print(f"Features: {features_filename}")
        
        return feature_cols, feature_importance
        
    def simulate_enhanced_trading(self, confidence_threshold=0.8):
        """Simulate trading with enhanced model"""
        print(f"\\n💰 SIMULATING ENHANCED TRADING STRATEGY (confidence ≥ {confidence_threshold})")
        
        # Get all features for prediction
        exclude_cols = [
            'timestamp', 'bid', 'ask', 'mid_price', 'datetime', 'spread',
            'target_enhanced', 'target_original', 'day_of_week'
        ]
        feature_cols = [col for col in self.df.columns if col not in exclude_cols]
        
        # Prepare data
        clean_data = self.df[['mid_price', 'datetime'] + feature_cols + ['target_enhanced']].dropna()
        feature_data = clean_data[feature_cols]
        feature_data_scaled = self.scaler.transform(feature_data)
        
        # Get predictions
        probabilities = self.model.predict_proba(feature_data_scaled)[:, 1]
        trade_signals = probabilities >= confidence_threshold
        
        # Simulate trades
        starting_capital = 20
        position_size = 0.20
        tick_value = 0.001
        current_capital = starting_capital
        trades = []
        
        for i, (_, row) in enumerate(clean_data.iterrows()):
            if i >= len(trade_signals):
                break
                
            if trade_signals[i] and i < len(clean_data) - 100:
                # Entry with 1 tick delay
                entry_idx = min(i + 1, len(clean_data) - 1)
                entry_price = clean_data.iloc[entry_idx]['mid_price']
                entry_time = clean_data.iloc[entry_idx]['datetime']
                
                # Exit after 50 ticks
                exit_idx = min(i + 50, len(clean_data) - 1)
                exit_price = clean_data.iloc[exit_idx]['mid_price']
                exit_time = clean_data.iloc[exit_idx]['datetime']
                
                # P&L calculation
                price_change = entry_price - exit_price
                ticks_gained = price_change / 0.001
                pnl = ticks_gained * tick_value * position_size
                
                current_capital += pnl
                
                trades.append({
                    'entry_time': entry_time,
                    'exit_time': exit_time,
                    'entry_price': entry_price,
                    'exit_price': exit_price,
                    'pnl': pnl,
                    'probability': probabilities[i],
                    'capital': current_capital
                })
        
        if trades:
            trades_df = pd.DataFrame(trades)
            winning_trades = trades_df[trades_df['pnl'] > 0]
            losing_trades = trades_df[trades_df['pnl'] < 0]
            
            print(f"\\n📈 ENHANCED TRADING RESULTS:")
            print(f"Total trades: {len(trades_df)}")
            print(f"Winning trades: {len(winning_trades)} ({len(winning_trades)/len(trades_df)*100:.1f}%)")
            print(f"Losing trades: {len(losing_trades)} ({len(losing_trades)/len(trades_df)*100:.1f}%)")
            print(f"Starting capital: ${starting_capital:.2f}")
            print(f"Final capital: ${current_capital:.2f}")
            print(f"Total return: {(current_capital - starting_capital) / starting_capital * 100:.1f}%")
            print(f"Average trade: ${trades_df['pnl'].mean():.4f}")
            print(f"Best trade: ${trades_df['pnl'].max():.4f}")
            print(f"Worst trade: ${trades_df['pnl'].min():.4f}")
            
            if len(losing_trades) > 0:
                print(f"\\n📉 LOSS ANALYSIS:")
                print(f"Average loss: ${losing_trades['pnl'].mean():.4f}")
                print(f"Total losses: ${losing_trades['pnl'].sum():.4f}")
                print(f"Loss reduction vs original: TBD (compare with original 89 losses)")
            
            return trades_df
        else:
            print("No trades generated!")
            return None

def main():
    """Run enhanced analysis"""
    print("🚀 ENHANCED BOOM 1000 XGBOOST - LOSS MINIMIZATION")
    print("=" * 60)
    
    csv_file = "C:\\\\Users\\\\<USER>\\\\META\\\\Boom_1000_Index_7days_20250619_20250626.csv"
    analyzer = EnhancedBoomAnalyzer(csv_file)
    
    try:
        # Load and process data
        analyzer.load_and_parse_data()
        analyzer.create_base_features()
        analyzer.create_loss_minimization_features()
        
        # Create enhanced targets
        if analyzer.create_target_labels():
            # Train enhanced model
            feature_cols, importance = analyzer.train_enhanced_model('target_enhanced')
            
            # Test trading simulation
            trades_df = analyzer.simulate_enhanced_trading(confidence_threshold=0.8)
            
            print(f"\\n✅ Enhanced model training completed!")
            
        else:
            print("❌ Insufficient positive targets for enhanced model!")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    return analyzer

if __name__ == "__main__":
    analyzer = main()
