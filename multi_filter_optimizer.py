import pandas as pd
import numpy as np
import os
from itertools import product
import warnings
warnings.filterwarnings('ignore')

# Load OHLC data
DATA_PATH = "stpRNG_1min.csv"
if not os.path.isfile(DATA_PATH):
    raise FileNotFoundError(f"File not found: {DATA_PATH}")

df = pd.read_csv(DATA_PATH)
df = df.dropna().reset_index(drop=True)

print(f"Loaded {len(df)} rows of data")

class TradingSystemOptimizer:
    def __init__(self, df):
        self.df = df.copy()
        self.results = []
    
    def calculate_ema(self, series, period):
        """Calculate Exponential Moving Average"""
        return series.ewm(span=period, adjust=False).mean()
    
    def calculate_sma(self, series, period):
        """Calculate Simple Moving Average"""
        return series.rolling(window=period).mean()
    
    def calculate_rsi(self, series, period):
        """Calculate RSI"""
        delta = series.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def calculate_atr(self, df, period):
        """Calculate ATR"""
        tr1 = df["high"] - df["low"]
        tr2 = abs(df["high"] - df["close"].shift(1))
        tr3 = abs(df["low"] - df["close"].shift(1))
        tr = pd.DataFrame([tr1, tr2, tr3]).max()
        return tr.rolling(window=period).mean()
    
    def calculate_stoch(self, high, low, close, period):
        """Calculate Stochastic oscillator"""
        lowest_low = low.rolling(window=period).min()
        highest_high = high.rolling(window=period).max()
        k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        return k_percent
    
    def calculate_qtrend_signals(self, df, trend_period=200, atr_period=3, atr_mult=1.6):
        """Calculate Q-Trend signals"""
        df = df.copy()
        
        # Q-Trend calculations
        df["src"] = df["close"]
        df["h"] = df["src"].rolling(window=trend_period).max()
        df["l"] = df["src"].rolling(window=trend_period).min()
        df["d"] = df["h"] - df["l"]
        df["m"] = (df["h"] + df["l"]) / 2
        
        df["atr"] = self.calculate_atr(df, atr_period).shift(1)
        df["epsilon"] = atr_mult * df["atr"]
        
        # Initialize
        df["trend"] = np.nan
        df["last_signal"] = ""
        df["qtrend_signal"] = ""
        
        # Process each bar
        for i in range(len(df)):
            if i < trend_period:
                df.at[i, "trend"] = df.at[i, "m"] if not pd.isna(df.at[i, "m"]) else np.nan
                continue
            
            src = df.at[i, "src"]
            prev_trend = df.at[i-1, "trend"] if i > 0 and not pd.isna(df.at[i-1, "trend"]) else df.at[i, "m"]
            epsilon = df.at[i, "epsilon"]
            
            if pd.isna(epsilon):
                df.at[i, "trend"] = prev_trend
                continue
            
            change_up = src > prev_trend + epsilon
            change_down = src < prev_trend - epsilon
            
            # Strong signals logic
            h = df.at[i, "h"]
            l = df.at[i, "l"]
            d = df.at[i, "d"]
            
            sb = False
            ss = False
            for j in range(max(0, i-4), i+1):
                if j < len(df):
                    open_price = df.at[j, "open"]
                    if open_price < l + d / 8 and open_price >= l:
                        sb = True
                    if open_price > h - d / 8 and open_price <= h:
                        ss = True
            
            # Update trend line
            if change_up or change_down:
                if change_up:
                    new_trend = prev_trend + epsilon
                elif change_down:
                    new_trend = prev_trend - epsilon
                else:
                    new_trend = prev_trend
            else:
                new_trend = prev_trend
            
            df.at[i, "trend"] = new_trend
            
            # Signal logic
            prev_last_signal = df.at[i-1, "last_signal"] if i > 0 else ""
            
            if change_up and prev_last_signal != "B":
                signal = "strong_buy" if sb else "buy"
                df.at[i, "last_signal"] = "B"
            elif change_down and prev_last_signal != "S":
                signal = "strong_sell" if ss else "sell"
                df.at[i, "last_signal"] = "S"
            else:
                signal = ""
                df.at[i, "last_signal"] = prev_last_signal
            
            df.at[i, "qtrend_signal"] = signal
        
        return df
    
    def calculate_all_indicators(self, params):
        """Calculate all indicators with given parameters"""
        df = self.df.copy()
        
        # Q-Trend
        df = self.calculate_qtrend_signals(df, 
                                          trend_period=params['qtrend_period'],
                                          atr_period=params['atr_period'], 
                                          atr_mult=params['atr_mult'])
        
        # EMA signals
        df['fast_ema'] = self.calculate_ema(df['close'], params['fast_ema'])
        df['slow_ema'] = self.calculate_ema(df['close'], params['slow_ema'])
        df['ema_buy'] = df['fast_ema'] > df['slow_ema']
        df['ema_sell'] = df['fast_ema'] < df['slow_ema']
        
        # EMA crossover signals
        df['ema_cross_up'] = (df['ema_buy'] & ~df['ema_buy'].shift(1).fillna(False))
        df['ema_cross_down'] = (df['ema_sell'] & ~df['ema_sell'].shift(1).fillna(False))
        
        # RSI
        df['rsi'] = self.calculate_rsi(df['close'], params['rsi_period'])
        df['rsi_oversold'] = df['rsi'] < params['rsi_oversold']
        df['rsi_overbought'] = df['rsi'] > params['rsi_overbought']
        
        # Stochastic RSI
        rsi_for_stoch = df['rsi']
        df['stoch_k_raw'] = self.calculate_stoch(rsi_for_stoch, rsi_for_stoch, rsi_for_stoch, params['stoch_period'])
        df['stoch_k'] = self.calculate_sma(df['stoch_k_raw'], params['stoch_smooth'])
        df['stoch_d'] = self.calculate_sma(df['stoch_k'], params['stoch_smooth'])
        
        df['stoch_cross_up'] = (df['stoch_k'] > df['stoch_d']) & (df['stoch_k'].shift(1) <= df['stoch_d'].shift(1))
        df['stoch_cross_down'] = (df['stoch_k'] < df['stoch_d']) & (df['stoch_k'].shift(1) >= df['stoch_d'].shift(1))
        
        # Moving Average Filter
        df['ma_filter'] = self.calculate_ema(df['close'], params['ma_filter_period'])
        df['above_ma'] = df['close'] > df['ma_filter']
        df['below_ma'] = df['close'] < df['ma_filter']
        
        # Volume filter (if available)
        if 'volume' in df.columns:
            df['volume_ma'] = df['volume'].rolling(params['volume_period']).mean()
            df['high_volume'] = df['volume'] > df['volume_ma'] * params['volume_mult']
        else:
            df['high_volume'] = True  # No volume filter
        
        # Price momentum
        df['momentum'] = df['close'].pct_change(params['momentum_period'])
        df['positive_momentum'] = df['momentum'] > params['momentum_threshold']
        df['negative_momentum'] = df['momentum'] < -params['momentum_threshold']
        
        return df
    
    def apply_filters(self, df, filter_config):
        """Apply selected filters to generate final signals"""
        
        # Base signals
        qtrend_buy = df['qtrend_signal'].isin(['buy', 'strong_buy'])
        qtrend_sell = df['qtrend_signal'].isin(['sell', 'strong_sell'])
        
        # Initialize final signals
        final_buy = qtrend_buy
        final_sell = qtrend_sell
        
        # Apply EMA filter
        if filter_config['use_ema']:
            final_buy = final_buy & df['ema_buy']
            final_sell = final_sell & df['ema_sell']
        
        # Apply EMA crossover filter
        if filter_config['use_ema_cross']:
            final_buy = final_buy & df['ema_cross_up']
            final_sell = final_sell & df['ema_cross_down']
        
        # Apply RSI filter
        if filter_config['use_rsi']:
            final_buy = final_buy & df['rsi_oversold']
            final_sell = final_sell & df['rsi_overbought']
        
        # Apply Stoch RSI filter
        if filter_config['use_stoch']:
            final_buy = final_buy & df['stoch_cross_up']
            final_sell = final_sell & df['stoch_cross_down']
        
        # Apply MA trend filter
        if filter_config['use_ma_trend']:
            final_buy = final_buy & df['above_ma']
            final_sell = final_sell & df['below_ma']
        
        # Apply volume filter
        if filter_config['use_volume']:
            final_buy = final_buy & df['high_volume']
            final_sell = final_sell & df['high_volume']
        
        # Apply momentum filter
        if filter_config['use_momentum']:
            final_buy = final_buy & df['positive_momentum']
            final_sell = final_sell & df['negative_momentum']
        
        return final_buy, final_sell
    
    def backtest_strategy(self, df, buy_signals, sell_signals):
        """Run backtest with given signals"""
        capital = 100_000
        risk_per_trade = 0.004
        balance = capital
        position = 0
        entry_price = 0
        trades = []
        
        for i in range(1, len(df)):
            current_price = df.iloc[i]['open']
            buy_signal = buy_signals.iloc[i]
            sell_signal = sell_signals.iloc[i]
            
            # Exit positions
            if position > 0 and sell_signal:
                pnl = (current_price - entry_price) / entry_price
                trades.append({'pnl': pnl, 'type': 'long'})
                if pnl > 0:
                    balance *= (1 + risk_per_trade)
                else:
                    balance *= (1 - risk_per_trade)
                position = 0
                
            elif position < 0 and buy_signal:
                pnl = (entry_price - current_price) / entry_price
                trades.append({'pnl': pnl, 'type': 'short'})
                if pnl > 0:
                    balance *= (1 + risk_per_trade)
                else:
                    balance *= (1 - risk_per_trade)
                position = 0
            
            # Enter new positions
            if position == 0:
                if buy_signal:
                    position = 1
                    entry_price = current_price
                elif sell_signal:
                    position = -1
                    entry_price = current_price
        
        # Calculate results
        total_trades = len(trades)
        wins = len([t for t in trades if t['pnl'] > 0])
        win_rate = wins / total_trades * 100 if total_trades > 0 else 0
        total_return = (balance - capital) / capital * 100
        
        return {
            'total_trades': total_trades,
            'win_rate': win_rate,
            'total_return': total_return,
            'final_balance': balance
        }
    
    def optimize_parameters(self):
        """Optimize all parameters and filter combinations"""
        
        # Parameter ranges to test
        param_ranges = {
            'qtrend_period': [100, 150, 200, 250],
            'atr_period': [3, 5, 10, 14],
            'atr_mult': [1.0, 1.2, 1.6, 2.0],
            'fast_ema': [8, 12, 21],
            'slow_ema': [21, 25, 50],
            'rsi_period': [14, 21],
            'rsi_oversold': [20, 30],
            'rsi_overbought': [70, 80],
            'stoch_period': [14, 21],
            'stoch_smooth': [3, 5],
            'ma_filter_period': [50, 100, 200],
            'volume_period': [20, 50],
            'volume_mult': [1.2, 1.5, 2.0],
            'momentum_period': [5, 10, 20],
            'momentum_threshold': [0.001, 0.002, 0.005]
        }
        
        # Filter combinations to test
        filter_combinations = [
            # Q-Trend only
            {'use_ema': False, 'use_ema_cross': False, 'use_rsi': False, 'use_stoch': False, 'use_ma_trend': False, 'use_volume': False, 'use_momentum': False},
            
            # Q-Trend + single filters
            {'use_ema': True, 'use_ema_cross': False, 'use_rsi': False, 'use_stoch': False, 'use_ma_trend': False, 'use_volume': False, 'use_momentum': False},
            {'use_ema': False, 'use_ema_cross': True, 'use_rsi': False, 'use_stoch': False, 'use_ma_trend': False, 'use_volume': False, 'use_momentum': False},
            {'use_ema': False, 'use_ema_cross': False, 'use_rsi': True, 'use_stoch': False, 'use_ma_trend': False, 'use_volume': False, 'use_momentum': False},
            {'use_ema': False, 'use_ema_cross': False, 'use_rsi': False, 'use_stoch': True, 'use_ma_trend': False, 'use_volume': False, 'use_momentum': False},
            {'use_ema': False, 'use_ema_cross': False, 'use_rsi': False, 'use_stoch': False, 'use_ma_trend': True, 'use_volume': False, 'use_momentum': False},
            {'use_ema': False, 'use_ema_cross': False, 'use_rsi': False, 'use_stoch': False, 'use_ma_trend': False, 'use_volume': True, 'use_momentum': False},
            {'use_ema': False, 'use_ema_cross': False, 'use_rsi': False, 'use_stoch': False, 'use_ma_trend': False, 'use_volume': False, 'use_momentum': True},
            
            # Popular combinations
            {'use_ema': True, 'use_ema_cross': False, 'use_rsi': True, 'use_stoch': False, 'use_ma_trend': False, 'use_volume': False, 'use_momentum': False},
            {'use_ema': False, 'use_ema_cross': True, 'use_rsi': False, 'use_stoch': True, 'use_ma_trend': False, 'use_volume': False, 'use_momentum': False},
            {'use_ema': True, 'use_ema_cross': False, 'use_rsi': False, 'use_stoch': False, 'use_ma_trend': True, 'use_volume': False, 'use_momentum': False},
            {'use_ema': True, 'use_ema_cross': False, 'use_rsi': True, 'use_stoch': False, 'use_ma_trend': True, 'use_volume': False, 'use_momentum': False},
            
            # Kitchen sink (all filters)
            {'use_ema': True, 'use_ema_cross': True, 'use_rsi': True, 'use_stoch': True, 'use_ma_trend': True, 'use_volume': True, 'use_momentum': True},
        ]
        
        print(f"Testing {len(filter_combinations)} filter combinations...")
        
        best_result = None
        best_return = -float('inf')
        test_count = 0
        
        # Sample parameters for faster testing (reduce combinations)
        sampled_params = {
            'qtrend_period': [200],  # Use best from previous
            'atr_period': [3],
            'atr_mult': [1.6],
            'fast_ema': [12, 21],
            'slow_ema': [25, 50],
            'rsi_period': [14],
            'rsi_oversold': [20, 30],
            'rsi_overbought': [70, 80],
            'stoch_period': [14],
            'stoch_smooth': [3],
            'ma_filter_period': [50, 200],
            'volume_period': [20],
            'volume_mult': [1.5],
            'momentum_period': [10],
            'momentum_threshold': [0.002]
        }
        
        # Generate all parameter combinations
        param_names = list(sampled_params.keys())
        param_combinations = list(product(*sampled_params.values()))
        
        print(f"Testing {len(param_combinations)} parameter combinations x {len(filter_combinations)} filter combinations = {len(param_combinations) * len(filter_combinations)} total tests")
        
        # Test combinations (sample for speed)
        max_tests = 1000
        step_size = max(1, (len(param_combinations) * len(filter_combinations)) // max_tests)
        
        for filter_idx, filter_config in enumerate(filter_combinations):
            for param_idx, param_values in enumerate(param_combinations[::step_size]):
                if test_count >= max_tests:
                    break
                    
                test_count += 1
                if test_count % 100 == 0:
                    print(f"Completed {test_count} tests...")
                
                # Create parameter dict
                params = dict(zip(param_names, param_values))
                
                try:
                    # Calculate indicators
                    df_with_indicators = self.calculate_all_indicators(params)
                    
                    # Apply filters
                    buy_signals, sell_signals = self.apply_filters(df_with_indicators, filter_config)
                    
                    # Skip if no signals
                    if buy_signals.sum() == 0 and sell_signals.sum() == 0:
                        continue
                    
                    # Backtest
                    result = self.backtest_strategy(df_with_indicators, buy_signals, sell_signals)
                    
                    # Store result
                    result_record = {
                        'params': params.copy(),
                        'filters': filter_config.copy(),
                        'total_trades': result['total_trades'],
                        'win_rate': result['win_rate'],
                        'total_return': result['total_return'],
                        'buy_signals': buy_signals.sum(),
                        'sell_signals': sell_signals.sum()
                    }
                    
                    self.results.append(result_record)
                    
                    # Track best
                    if result['total_return'] > best_return:
                        best_return = result['total_return']
                        best_result = result_record
                        
                except Exception as e:
                    continue
        
        print(f"Completed {test_count} tests total")
        return best_result

# Run optimization
optimizer = TradingSystemOptimizer(df)
print("Starting parameter optimization...")

best_strategy = optimizer.optimize_parameters()

print(f"\n=== OPTIMIZATION RESULTS ===")
print(f"Total strategies tested: {len(optimizer.results)}")

if best_strategy:
    print(f"\n=== BEST STRATEGY ===")
    print(f"Return: {best_strategy['total_return']:+.2f}%")
    print(f"Trades: {best_strategy['total_trades']}")
    print(f"Win Rate: {best_strategy['win_rate']:.2f}%")
    print(f"Buy Signals: {best_strategy['buy_signals']}")
    print(f"Sell Signals: {best_strategy['sell_signals']}")
    
    print(f"\nBest Parameters:")
    for param, value in best_strategy['params'].items():
        print(f"  {param}: {value}")
    
    print(f"\nActive Filters:")
    for filter_name, active in best_strategy['filters'].items():
        if active:
            print(f"  {filter_name}: YES")

# Sort all results by return
optimizer.results.sort(key=lambda x: x['total_return'], reverse=True)

print(f"\n=== TOP 10 STRATEGIES ===")
for i, result in enumerate(optimizer.results[:10]):
    active_filters = [name for name, active in result['filters'].items() if active]
    filter_str = "+".join(active_filters) if active_filters else "Q-Trend Only"
    print(f"{i+1:2d}. {result['total_return']:+6.2f}% | {result['total_trades']:3d} trades | {result['win_rate']:5.1f}% WR | {filter_str}")

# Save results
results_df = pd.DataFrame(optimizer.results)
results_df.to_csv('optimization_results.csv', index=False)
print(f"\nFull results saved to 'optimization_results.csv'")

# Filter analysis
print(f"\n=== FILTER EFFECTIVENESS ANALYSIS ===")
filter_performance = {}

for filter_name in ['use_ema', 'use_ema_cross', 'use_rsi', 'use_stoch', 'use_ma_trend', 'use_volume', 'use_momentum']:
    with_filter = [r for r in optimizer.results if r['filters'][filter_name]]
    without_filter = [r for r in optimizer.results if not r['filters'][filter_name]]
    
    if with_filter and without_filter:
        avg_return_with = np.mean([r['total_return'] for r in with_filter])
        avg_return_without = np.mean([r['total_return'] for r in without_filter])
        improvement = avg_return_with - avg_return_without
        
        filter_performance[filter_name] = {
            'improvement': improvement,
            'avg_with': avg_return_with,
            'avg_without': avg_return_without,
            'count_with': len(with_filter),
            'count_without': len(without_filter)
        }

# Sort filters by effectiveness
sorted_filters = sorted(filter_performance.items(), key=lambda x: x[1]['improvement'], reverse=True)

print("Filter effectiveness (average return improvement):")
for filter_name, stats in sorted_filters:
    print(f"  {filter_name:15s}: {stats['improvement']:+6.2f}% ({stats['avg_with']:+5.2f}% vs {stats['avg_without']:+5.2f}%)")
