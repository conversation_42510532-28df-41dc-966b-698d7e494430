
"""
Batch download 7 days of Range Break 100 tick data
Downloads in chunks to avoid MT5 limitations
"""

import MetaTrader5 as mt5
import pandas as pd
from datetime import datetime, timedelta
import pytz
import os
import time

def download_range_break_7days():
    """Download 7 days of Range Break 100 tick data in batches"""
    
    # Initialize MT5
    if not mt5.initialize():
        print(f"initialize() failed, error code = {mt5.last_error()}")
        return None
    
    print("MT5 connection established")
    
    try:
        symbol = "Range Break 100 Index.0"
        
        # Check if symbol is available
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info is None:
            print(f"Symbol {symbol} not found")
            return None
        
        print(f"Symbol {symbol} found")
        
        # Set timezone to UTC
        timezone = pytz.timezone("Etc/UTC")
        
        # Set up 7-day range
        days = 7
        end_time = datetime.now(timezone)
        start_time = end_time - timedelta(days=days)
        
        print(f"Downloading {days} days of data from {start_time} to {end_time}")
        
        # Break into 6-hour chunks to avoid MT5 limitations
        chunk_hours = 6
        df_list = []
        
        current_time = start_time
        chunk_count = 0
        
        while current_time < end_time:
            chunk_end = min(current_time + timedelta(hours=chunk_hours), end_time)
            chunk_count += 1
            
            print(f"Downloading chunk {chunk_count}: {current_time} to {chunk_end}")
            
            # Try to download this chunk
            ticks = mt5.copy_ticks_range(symbol, current_time, chunk_end, mt5.COPY_TICKS_ALL)
            
            if ticks is not None and len(ticks) > 0:
                # Convert chunk to DataFrame and append to list
                chunk_df = pd.DataFrame(ticks)
                df_list.append(chunk_df)
                print(f"Downloaded {len(ticks)} ticks for this chunk.")
            else:
                print("No ticks downloaded for this chunk.")

            current_time += timedelta(hours=chunk_hours)
            time.sleep(1) # Be nice to the server

        if not df_list:
            print("No ticks downloaded for the entire period.")
            return None

        # Concatenate all DataFrames
        ticks_df = pd.concat(df_list, ignore_index=True)
        ticks_df.columns = ['time', 'bid', 'ask', 'last', 'volume', 'time_msc', 'flags', 'volume_real']
        
        # Convert time columns to datetime objects
        ticks_df['time'] = pd.to_datetime(ticks_df['time'], unit='s')
        ticks_df['time_msc'] = pd.to_datetime(ticks_df['time_msc'], unit='ms')

        # Add symbol column
        ticks_df['symbol'] = symbol
        
        # Remove duplicates based on time_msc (in case of overlapping chunks)
        initial_count = len(ticks_df)
        ticks_df = ticks_df.drop_duplicates(subset=['time_msc']).sort_values('time_msc').reset_index(drop=True)
        print(f"Removed {initial_count - len(ticks_df)} duplicate ticks.")
        
        print(f"Total unique ticks downloaded: {len(ticks_df)}")
        
        # Save to CSV
        filename = f"Range_Break_100_7days_{start_time.strftime('%Y%m%d')}_{end_time.strftime('%Y%m%d')}.csv"
        ticks_df.to_csv(filename, index=False)
        print(f"Data saved to {filename}")
        
        print("\nData Summary:")
        print(f"Shape: {ticks_df.shape}")
        print(f"Date range: {ticks_df['time'].min()} to {ticks_df['time'].max()}")
        duration = ticks_df['time'].max() - ticks_df['time'].min()
        print(f"Duration: {duration}")
        if duration.total_seconds() > 0:
            print(f"Average ticks per hour: {len(ticks_df) / (duration.total_seconds() / 3600):.0f}")
        
        print("\nSample data:")
        print(ticks_df.head())
        
        return ticks_df
        
    except Exception as e:
        print(f"Error during download: {e}")
        return None
    
    finally:
        mt5.shutdown()
        print("MT5 connection closed")

if __name__ == "__main__":
    print("Starting 7-day batch download for Range Break 100...")
    ticks_data = download_range_break_7days()
