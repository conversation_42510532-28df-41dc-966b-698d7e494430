import pandas as pd
import numpy as np
from datetime import datetime

def analyze_data_leakage(csv_file):
    """Comprehensive analysis to detect data leakage in the Boom spike model"""
    
    print("="*60)
    print("DATA LEAKAGE ANALYSIS FOR BOOM SPIKE MODEL")
    print("="*60)
    
    # Load data
    data = pd.read_csv(csv_file)
    data['datetime'] = pd.to_datetime(data['time'])
    data = data.sort_values('datetime').reset_index(drop=True)
    data['price'] = data['bid']
    
    print(f"Total records: {len(data)}")
    print(f"Time range: {data['datetime'].min()} to {data['datetime'].max()}")
    
    # Replicate spike detection logic exactly as in the model
    data['price_change'] = data['price'].diff()
    data['price_change_pct'] = (data['price_change'] / data['price'].shift(1)) * 100
    
    spike_threshold_pct = 0.3
    uniform_threshold = 0.05
    
    data['is_spike'] = (
        (data['price_change_pct'] > spike_threshold_pct) | 
        (data['price_change_pct'] < -spike_threshold_pct)
    ).astype(int)
    
    # Count uniform ticks after each spike (same logic as model)
    data['uniform_ticks_after_spike'] = 0
    spike_indices = data[data['is_spike'] == 1].index
    
    print(f"\nSpike Analysis:")
    print(f"Number of spikes detected: {len(spike_indices)}")
    print(f"Spike indices: {list(spike_indices)}")
    
    for spike_idx in spike_indices:
        uniform_count = 0
        idx = spike_idx + 1
        
        while idx < len(data):
            if abs(data.loc[idx, 'price_change_pct']) <= uniform_threshold:
                uniform_count += 1
                idx += 1
            else:
                break
                
            if uniform_count >= 100:
                break
        
        data.loc[spike_idx, 'uniform_ticks_after_spike'] = uniform_count
        print(f"  Spike at index {spike_idx}: {uniform_count} uniform ticks follow")
    
    # Analyze feature creation for potential leakage
    print(f"\nFeature Creation Analysis:")
    print("Checking for future data leakage...")
    
    # Check lag features (should be safe)
    lookback_window = 20
    leakage_issues = []
    
    print(f"1. LAG FEATURES (lookback_window={lookback_window}):")
    for i in range(1, lookback_window + 1):
        # These shift BACKWARD (safe)
        price_lag = data['price'].shift(i)
        change_lag = data['price_change'].shift(i)
        change_pct_lag = data['price_change_pct'].shift(i)
        print(f"   lag_{i}: Uses data from {i} steps in the PAST ✓")
    
    # Check moving averages
    print(f"\n2. MOVING AVERAGES:")
    for window in [5, 10, 20]:
        ma = data['price'].rolling(window=window).mean()
        print(f"   ma_{window}: Uses {window} PAST values only ✓")
    
    # Check volatility features
    print(f"\n3. VOLATILITY FEATURES:")
    for window in [5, 10, 20]:
        vol = data['price_change_pct'].rolling(window=window).std()
        print(f"   volatility_{window}: Uses {window} PAST values only ✓")
    
    # Check momentum features
    print(f"\n4. MOMENTUM FEATURES:")
    for window in [3, 5, 10]:
        momentum = data['price'] / data['price'].shift(window) - 1
        print(f"   momentum_{window}: Compares current to {window} steps PAST ✓")
    
    # Check recent spike activity - POTENTIAL ISSUE
    print(f"\n5. RECENT SPIKE ACTIVITY:")
    for window in [10, 50, 100]:
        recent_spikes = data['is_spike'].rolling(window=window).sum()
        print(f"   recent_spikes_{window}: Uses {window} PAST spike counts ✓")
    
    # Critical analysis: Check if uniform_ticks_after_spike uses future data
    print(f"\n" + "="*60)
    print("CRITICAL ANALYSIS: UNIFORM TICKS COUNTING")
    print("="*60)
    
    print("Checking uniform tick counting logic for future data leakage...")
    
    future_leakage_found = False
    
    for spike_idx in spike_indices:
        print(f"\nSpike at index {spike_idx}:")
        print(f"  Target (uniform_ticks_after_spike): {data.loc[spike_idx, 'uniform_ticks_after_spike']}")
        
        # Check what data was used to calculate this target
        idx = spike_idx + 1
        ticks_checked = []
        while idx < len(data) and len(ticks_checked) < 10:  # Show first 10 ticks
            if abs(data.loc[idx, 'price_change_pct']) <= uniform_threshold:
                ticks_checked.append(f"idx {idx}: {data.loc[idx, 'price_change_pct']:.4f}% (uniform)")
                idx += 1
            else:
                ticks_checked.append(f"idx {idx}: {data.loc[idx, 'price_change_pct']:.4f}% (breaks pattern)")
                break
        
        print(f"  Calculation used these FUTURE ticks:")
        for tick in ticks_checked:
            print(f"    {tick}")
        
        # This is the problem! We're using future data to predict future data
        if idx > spike_idx + 1:
            future_leakage_found = True
    
    print(f"\n" + "="*60)
    print("TRAIN-TEST SPLIT ANALYSIS")
    print("="*60)
    
    # Analyze train-test split
    total_samples = len(data) - 20  # After feature creation
    test_size = 0.2
    train_samples = int(total_samples * 0.8)
    test_samples = total_samples - train_samples
    
    print(f"Total samples after feature creation: {total_samples}")
    print(f"Train samples: {train_samples}")
    print(f"Test samples: {test_samples}")
    print("Split method: sklearn train_test_split with stratify=y_spike")
    print("This is RANDOM split, not time-based!")
    
    # Check if spikes are distributed across train/test
    spike_positions = list(spike_indices)
    print(f"\nSpike positions in dataset: {spike_positions}")
    
    # Simulate the train-test split
    from sklearn.model_selection import train_test_split
    
    # Create dummy features and labels like the model does
    features = np.random.random((total_samples, 78))
    y_spike = np.zeros(total_samples)
    
    # Set spike labels at the correct positions (adjusted for feature creation offset)
    for spike_idx in spike_indices:
        if spike_idx >= 20 and spike_idx < len(data):  # After dropna in feature creation
            adjusted_idx = spike_idx - 20
            if adjusted_idx < total_samples:
                y_spike[adjusted_idx] = 1
    
    try:
        X_train, X_test, y_train, y_test = train_test_split(
            features, y_spike, test_size=0.2, random_state=42, stratify=y_spike
        )
        
        train_spikes = np.sum(y_train)
        test_spikes = np.sum(y_test)
        
        print(f"\nAfter train-test split:")
        print(f"Spikes in training set: {train_spikes}")
        print(f"Spikes in test set: {test_spikes}")
        
    except Exception as e:
        print(f"Error in train-test split simulation: {e}")
    
    print(f"\n" + "="*60)
    print("LEAKAGE ASSESSMENT SUMMARY")
    print("="*60)
    
    issues_found = []
    
    # Issue 1: Future data leakage in target variable
    if future_leakage_found:
        issues_found.append("CRITICAL: uniform_ticks_after_spike uses FUTURE data")
    
    # Issue 2: Random split instead of time-based
    issues_found.append("WARNING: Random train-test split instead of time-based")
    
    # Issue 3: Very few spikes (overfitting risk)
    if len(spike_indices) < 10:
        issues_found.append(f"WARNING: Only {len(spike_indices)} spikes - high overfitting risk")
    
    if issues_found:
        print("ISSUES IDENTIFIED:")
        for i, issue in enumerate(issues_found, 1):
            print(f"{i}. {issue}")
    else:
        print("No major issues identified")
    
    print(f"\n" + "="*60)
    print("RECOMMENDATIONS")
    print("="*60)
    
    print("1. TARGET VARIABLE FIX:")
    print("   - Don't predict uniform_ticks_after_spike for current time")
    print("   - Instead, predict if NEXT tick will be uniform")
    print("   - Or predict spike probability only (no future counting)")
    
    print("\n2. TRAIN-TEST SPLIT FIX:")
    print("   - Use time-based split (first 80% for train, last 20% for test)")
    print("   - This prevents future information leaking to past predictions")
    
    print("\n3. VALIDATION:")
    print("   - Walk-forward validation on time series")
    print("   - Out-of-sample testing on completely different time periods")
    
    return issues_found

if __name__ == "__main__":
    csv_file = "Boom_1000_Index_7days_20250620_20250627.csv"
    issues = analyze_data_leakage(csv_file)
    
    print(f"\n\n{'='*60}")
    print("FINAL VERDICT")
    print("="*60)
    
    if "CRITICAL" in str(issues):
        print("❌ CRITICAL DATA LEAKAGE DETECTED")
        print("The perfect results are due to data leakage, not genuine predictive power.")
        print("The model cannot be used for real trading without fixing these issues.")
    else:
        print("✅ No critical data leakage detected")
        print("Results appear valid for trading use.")
